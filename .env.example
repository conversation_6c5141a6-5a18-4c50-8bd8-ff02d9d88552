# AI Studio Proxy API 配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 服务端口配置
# =============================================================================

# FastAPI 服务端口
PORT=2048

# GUI 启动器默认端口配置
DEFAULT_FASTAPI_PORT=2048
DEFAULT_CAMOUFOX_PORT=9222

# 流式代理服务配置
STREAM_PORT=3120
# 设置为 0 禁用流式代理服务

# =============================================================================
# 代理配置
# =============================================================================

# HTTP/HTTPS 代理设置
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890

# 统一代理配置 (优先级高于 HTTP_PROXY/HTTPS_PROXY)
UNIFIED_PROXY_CONFIG=http://127.0.0.1:7890

# 代理绕过列表 (用分号分隔)
# NO_PROXY=localhost;127.0.0.1;*.local

# =============================================================================
# 日志配置
# =============================================================================

# 服务器日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
SERVER_LOG_LEVEL=INFO

# 是否重定向 print 输出到日志
SERVER_REDIRECT_PRINT=false

# 启用调试日志
DEBUG_LOGS_ENABLED=false

# 启用跟踪日志
TRACE_LOGS_ENABLED=false

# =============================================================================
# 认证配置
# =============================================================================

# 自动保存认证信息
AUTO_SAVE_AUTH=false

# 认证保存超时时间 (秒)
AUTH_SAVE_TIMEOUT=30

# 自动确认登录
AUTO_CONFIRM_LOGIN=true

# =============================================================================
# 浏览器配置
# =============================================================================

# Camoufox WebSocket 端点
# CAMOUFOX_WS_ENDPOINT=ws://127.0.0.1:9222

# 启动模式 (normal, headless, virtual_display, direct_debug_no_browser)
LAUNCH_MODE=normal

# =============================================================================
# API 默认参数配置
# =============================================================================

# 默认温度值 (0.0-2.0)
DEFAULT_TEMPERATURE=1.0

# 默认最大输出令牌数
DEFAULT_MAX_OUTPUT_TOKENS=65536

# 默认 Top-P 值 (0.0-1.0)
DEFAULT_TOP_P=0.95

# 默认停止序列 (JSON 数组格式)
DEFAULT_STOP_SEQUENCES=["用户:"]

# 是否在处理请求时自动打开并使用 "URL Context" 功能,此工具功能详情可参考:https://ai.google.dev/gemini-api/docs/url-context
ENABLE_URL_CONTEXT=true

# 是否默认启用 "指定思考预算" 功能 (true/false),不启用时模型一般将自行决定思考预算
# 当 API 请求中未提供 reasoning_effort 参数时将使用此值。
ENABLE_THINKING_BUDGET=false

# "指定思考预算量" 的默认值 (token)
# 当 API 请求中未提供 reasoning_effort 参数时，将使用此值。
DEFAULT_THINKING_BUDGET=8192

# 是否默认启用 "Google Search" 功能 (true/false)
# 当 API 请求中未提供 tools 参数时，将使用此设置作为 Google Search 的默认开关状态。
ENABLE_GOOGLE_SEARCH=false

# =============================================================================
# 超时配置 (毫秒)
# =============================================================================

# 响应完成总超时时间
RESPONSE_COMPLETION_TIMEOUT=300000

# 初始等待时间
INITIAL_WAIT_MS_BEFORE_POLLING=500

# 轮询间隔
POLLING_INTERVAL=300
POLLING_INTERVAL_STREAM=180

# 静默超时
SILENCE_TIMEOUT_MS=60000

# 页面操作超时
POST_SPINNER_CHECK_DELAY_MS=500
FINAL_STATE_CHECK_TIMEOUT_MS=1500
POST_COMPLETION_BUFFER=700

# 清理聊天相关超时
CLEAR_CHAT_VERIFY_TIMEOUT_MS=4000
CLEAR_CHAT_VERIFY_INTERVAL_MS=4000

# 点击和剪贴板操作超时
CLICK_TIMEOUT_MS=3000
CLIPBOARD_READ_TIMEOUT_MS=3000

# 元素等待超时
WAIT_FOR_ELEMENT_TIMEOUT_MS=10000

# 流相关配置
PSEUDO_STREAM_DELAY=0.01

# =============================================================================
# GUI 启动器配置
# =============================================================================

# GUI 默认代理地址
GUI_DEFAULT_PROXY_ADDRESS=http://127.0.0.1:7890

# GUI 默认流式代理端口
GUI_DEFAULT_STREAM_PORT=3120

# GUI 默认 Helper 端点
GUI_DEFAULT_HELPER_ENDPOINT=

# =============================================================================
# 脚本注入配置
# =============================================================================

# 是否启用油猴脚本注入功能
ENABLE_SCRIPT_INJECTION=true

# 油猴脚本文件路径（相对于项目根目录）
# 模型数据直接从此脚本文件中解析，无需额外配置文件
USERSCRIPT_PATH=browser_utils/more_modles.js

# =============================================================================
# 其他配置
# =============================================================================

# 模型名称
MODEL_NAME=AI-Studio_Proxy_API

# 聊天完成 ID 前缀
CHAT_COMPLETION_ID_PREFIX=chatcmpl-

# 默认回退模型 ID
DEFAULT_FALLBACK_MODEL_ID=no model list

# 排除模型文件名
EXCLUDED_MODELS_FILENAME=excluded_models.txt

# AI Studio URL 模式
AI_STUDIO_URL_PATTERN=aistudio.google.com/

# 模型端点 URL 包含字符串
MODELS_ENDPOINT_URL_CONTAINS=MakerSuiteService/ListModels

# 用户输入标记符
USER_INPUT_START_MARKER_SERVER=__USER_INPUT_START__
USER_INPUT_END_MARKER_SERVER=__USER_INPUT_END__

# =============================================================================
# 流状态配置
# =============================================================================

# 流超时日志状态配置
STREAM_MAX_INITIAL_ERRORS=3
STREAM_WARNING_INTERVAL_AFTER_SUPPRESS=60.0
STREAM_SUPPRESS_DURATION_AFTER_INITIAL_BURST=400.0
