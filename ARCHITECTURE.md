```mermaid
graph TB
    subgraph "User Interface Layer"
        User[👤 User/Client]
        WebUI[🌐 Web UI<br/>index.html + webui.js/css]
        APIClient[🔌 API Client<br/>OpenAI Compatible]
        GUILauncher[🖥️ GUI Launcher<br/>gui_launcher.py]
    end

    subgraph "Entry Points & Configuration"
        CLILauncher[⚡ CLI Launcher<br/>launch_camoufox.py]
        ServerPy[🚀 Server Entry<br/>server.py]
        EnvConfig[⚙️ Environment Config<br/>.env + config/]
        AuthFiles[🔐 Authentication<br/>auth_profiles/ + key.txt]
    end

    subgraph "Core Application Layer"
        FastAPIApp[🎯 FastAPI Application<br/>api_utils/app.py]
        Routes[🛣️ API Routes<br/>api_utils/routes.py]
        RequestProcessor[⚙️ Request Processor<br/>api_utils/request_processor.py]
        QueueWorker[📋 Queue Worker<br/>api_utils/queue_worker.py]
        AuthUtils[🔒 Auth Utils<br/>api_utils/auth_utils.py]
        Dependencies[🔗 Dependencies<br/>api_utils/dependencies.py]
    end

    subgraph "Browser Automation Layer"
        PageController[🎮 Page Controller<br/>browser_utils/page_controller.py]
        ModelManager[🤖 Model Management<br/>browser_utils/model_management.py]
        ScriptManager[📜 Script Manager<br/>browser_utils/script_manager.py]
        BrowserOps[🔧 Browser Operations<br/>browser_utils/operations.py]
        BrowserInit[🚀 Browser Initialization<br/>browser_utils/initialization.py]
    end

    subgraph "Streaming & Proxy Layer"
        StreamProxy[🌊 Stream Proxy Server<br/>stream/proxy_server.py]
        ProxyConnector[🔌 Proxy Connector<br/>stream/proxy_connector.py]
        Interceptors[🕵️ HTTP Interceptors<br/>stream/interceptors.py]
        CertManager[📜 Certificate Manager<br/>stream/cert_manager.py]
        StreamMain[⚡ Stream Main<br/>stream/main.py]
    end

    subgraph "Data & Configuration Layer"
        Models[📊 Data Models<br/>models/chat.py]
        Exceptions[❌ Exception Models<br/>models/exceptions.py]
        LoggingModels[📝 Logging Models<br/>models/logging.py]
        ConfigSettings[⚙️ Settings<br/>config/settings.py]
        ConfigConstants[📋 Constants<br/>config/constants.py]
        ConfigSelectors[🎯 CSS Selectors<br/>config/selectors.py]
        ConfigTimeouts[⏱️ Timeouts<br/>config/timeouts.py]
    end

    subgraph "Logging & Monitoring"
        LoggingUtils[📊 Logging Setup<br/>logging_utils/setup.py]
        WSManager[🔌 WebSocket Manager<br/>models/logging.py]
        LogFiles[📁 Log Files<br/>logs/]
    end

    subgraph "External Dependencies"
        CamoufoxBrowser[🦊 Camoufox Browser<br/>Anti-fingerprint Firefox]
        AIStudio[🤖 Google AI Studio<br/>aistudio.google.com]
        UserScripts[📜 User Scripts<br/>more_models.js]
        UpstreamProxy[🌐 Upstream Proxy<br/>Optional SOCKS/HTTP]
    end

    %% User Interactions
    User --> GUILauncher
    User --> CLILauncher
    User --> WebUI
    APIClient --> FastAPIApp

    %% Launch Flow
    GUILauncher --> CLILauncher
    CLILauncher --> ServerPy
    CLILauncher --> CamoufoxBrowser

    %% Configuration Flow
    EnvConfig --> ConfigSettings
    ConfigSettings --> ConfigConstants
    ConfigSettings --> ConfigSelectors
    ConfigSettings --> ConfigTimeouts
    AuthFiles --> AuthUtils

    %% Core Application Flow
    ServerPy --> FastAPIApp
    FastAPIApp --> Routes
    FastAPIApp --> LoggingUtils
    Routes --> RequestProcessor
    Routes --> AuthUtils
    Routes --> Dependencies
    RequestProcessor --> QueueWorker

    %% Browser Automation Flow
    QueueWorker --> PageController
    PageController --> ModelManager
    PageController --> ScriptManager
    PageController --> BrowserOps
    BrowserInit --> PageController
    ScriptManager --> UserScripts
    PageController --> CamoufoxBrowser

    %% Streaming & Proxy Flow
    FastAPIApp --> StreamMain
    StreamMain --> StreamProxy
    StreamProxy --> ProxyConnector
    StreamProxy --> Interceptors
    StreamProxy --> CertManager
    ProxyConnector --> UpstreamProxy
    StreamProxy --> AIStudio

    %% Data Flow
    Models --> RequestProcessor
    Exceptions --> RequestProcessor
    LoggingModels --> WSManager
    WSManager --> WebUI

    %% Browser to AI Studio
    CamoufoxBrowser --> AIStudio
    BrowserOps --> CamoufoxBrowser

    %% Three-Tier Response Flow
    AIStudio -.->|Tier 1: Stream Proxy| StreamProxy
    AIStudio -.->|Tier 2: Helper Service| RequestProcessor
    AIStudio -.->|Tier 3: Playwright| BrowserOps

    %% Response Back to Client
    StreamProxy --> RequestProcessor
    BrowserOps --> RequestProcessor
    RequestProcessor --> Routes
    Routes --> FastAPIApp
    FastAPIApp --> APIClient
    FastAPIApp --> WebUI

    %% Logging Flow
    LoggingUtils --> LogFiles
    WSManager --> WebUI
    FastAPIApp --> WSManager

    %% Styling
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef entryLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef coreLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef browserLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef streamLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef loggingLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef externalLayer fill:#fafafa,stroke:#424242,stroke-width:2px

    class User,WebUI,APIClient,GUILauncher userLayer
    class CLILauncher,ServerPy,EnvConfig,AuthFiles entryLayer
    class FastAPIApp,Routes,RequestProcessor,QueueWorker,AuthUtils,Dependencies coreLayer
    class PageController,ModelManager,ScriptManager,BrowserOps,BrowserInit browserLayer
    class StreamProxy,ProxyConnector,Interceptors,CertManager,StreamMain streamLayer
    class Models,Exceptions,LoggingModels,ConfigSettings,ConfigConstants,ConfigSelectors,ConfigTimeouts dataLayer
    class LoggingUtils,WSManager,LogFiles loggingLayer
    class CamoufoxBrowser,AIStudio,UserScripts,UpstreamProxy externalLayer
```
