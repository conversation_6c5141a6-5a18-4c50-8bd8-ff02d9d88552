#!/usr/bin/env python3
"""Simple test to verify session persistence works."""

import asyncio
import sys
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from runninghub_proxy.browser_utils.session_manager import SessionManager


async def test_basic_session_persistence():
    """Test basic session creation and persistence."""
    print("🧪 Testing basic session persistence...")
    
    # Create temporary session file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
        tmp_path = Path(tmp.name)
    
    try:
        # Test 1: Create session manager and session
        print("1. Creating session manager...")
        sm1 = SessionManager(session_file=tmp_path.name)
        
        # Wait for initialization
        while not sm1._initialized:
            await asyncio.sleep(0.01)
        
        print("2. Creating test session...")
        print(f"   SessionManager file path: {sm1.session_file_path}")
        
        user_info = {
            "user_id": "test_user_123",
            "username": "testuser",
            "email": "<EMAIL>"
        }
        
        session_data = await sm1.create_session(user_info=user_info)
        session_token = session_data["session_token"]
        
        print(f"   Session created: {session_token[:8]}...")
        print(f"   User ID: {session_data['user_id']}")
        
        # Give a moment for async save to complete
        await asyncio.sleep(0.1)
        
        # Test 2: Verify session file was created
        print("3. Checking session file...")
        # Use the actual session file path from the session manager
        actual_session_file = sm1.session_file_path
        print(f"   Session file path: {actual_session_file}")
        print(f"   File exists: {actual_session_file.exists()}")
        
        if actual_session_file.exists():
            file_size = actual_session_file.stat().st_size
            print(f"   File size: {file_size} bytes")
            
            if file_size == 0:
                print("   ❌ Session file is empty!")
                return False
        else:
            print("   ❌ Session file was not created")
            return False
        
        with open(actual_session_file, "r") as f:
            content = f.read()
            print(f"   File content preview: {content[:100]}...")
            
        with open(actual_session_file, "r") as f:
            import json
            saved_data = json.load(f)
        
        assert "sessions" in saved_data, "Sessions not found in file"
        assert session_token in saved_data["sessions"], "Session not saved to file"
        print("   ✅ Session file created and contains session")
        
        # Test 3: Cleanup first session manager
        await sm1.cleanup()
        
        # Test 4: Create new session manager (simulating app restart)
        print("4. Testing session restoration...")
        sm2 = SessionManager(session_file=tmp_path.name)
        
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Test 5: Verify session was restored
        restored_session = await sm2.get_session(session_token)
        assert restored_session is not None, "Session was not restored"
        assert restored_session["user_id"] == "test_user_123", "Session data corrupted"
        
        print("   ✅ Session successfully restored from disk")
        
        # Test 6: Verify session validation
        is_valid = await sm2.is_session_valid(session_token)
        assert is_valid, "Restored session is not valid"
        
        print("   ✅ Restored session is valid")
        
        # Cleanup
        await sm2.cleanup()
        
        print("\n🎉 All tests passed! Session persistence is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup temp file
        if tmp_path.exists():
            tmp_path.unlink()


async def test_auth_status_simulation():
    """Simulate the auth status flow."""
    print("\n🔍 Testing auth status simulation...")
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
        tmp_path = Path(tmp.name)
    
    try:
        # Create session manager
        sm = SessionManager(session_file=tmp_path.name)
        while not sm._initialized:
            await asyncio.sleep(0.01)
        
        # Simulate login - create session
        user_info = {
            "user_id": "status_test_user",
            "username": "statustest",
            "email": "<EMAIL>"
        }
        
        session_data = await sm.create_session(user_info=user_info)
        session_token = session_data["session_token"]
        
        print(f"   Login simulation: Created session {session_token[:8]}...")
        
        # Simulate auth status check
        retrieved_session = await sm.get_session(session_token)
        is_valid = await sm.is_session_valid(session_token)
        
        if retrieved_session and is_valid:
            print("   ✅ Auth status would return: is_authenticated=True")
            print(f"   ✅ User info: {retrieved_session['user_info']}")
        else:
            print("   ❌ Auth status would return: is_authenticated=False")
            return False
        
        await sm.cleanup()
        return True
        
    except Exception as e:
        print(f"   ❌ Auth status simulation failed: {e}")
        return False
        
    finally:
        if tmp_path.exists():
            tmp_path.unlink()


async def main():
    """Run all tests."""
    print("🚀 Session Persistence Test Suite")
    print("=" * 40)
    
    test1_passed = await test_basic_session_persistence()
    test2_passed = await test_auth_status_simulation()
    
    print("\n" + "=" * 40)
    if test1_passed and test2_passed:
        print("🎉 All tests passed! Implementation is working correctly.")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
