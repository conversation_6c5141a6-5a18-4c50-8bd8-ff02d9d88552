#!/usr/bin/env python3
"""Test runner for session persistence functionality."""

import subprocess
import sys
import os
from pathlib import Path


def run_tests():
    """Run all session persistence tests."""
    project_root = Path(__file__).parent.parent
    tests_dir = project_root / "tests"
    
    # Ensure we're in the project directory
    os.chdir(project_root)
    
    print("🧪 Running Session Persistence Tests")
    print("=" * 50)
    
    # Test files to run
    test_files = [
        "tests/test_session_persistence.py",
        "tests/test_auth_dependencies.py", 
        "tests/test_auth_routes_e2e.py"
    ]
    
    # Install test dependencies if needed
    print("📦 Installing test dependencies...")
    subprocess.run([
        sys.executable, "-m", "pip", "install", 
        "pytest", "pytest-asyncio", "httpx"
    ], check=False)
    
    all_passed = True
    
    for test_file in test_files:
        if not Path(test_file).exists():
            print(f"⚠️  Test file not found: {test_file}")
            continue
            
        print(f"\n🔍 Running {test_file}...")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", test_file, 
                "-v", "--tb=short"
            ], check=True, capture_output=False)
            
            print(f"✅ {test_file} passed")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {test_file} failed with exit code {e.returncode}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(run_tests())
