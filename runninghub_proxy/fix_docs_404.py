#!/usr/bin/env python3
"""Fix the /docs 404 issue."""

import os
import sys
from pathlib import Path


def fix_docs_404():
    """Fix the /docs 404 issue."""
    print("🔧 Fixing /docs 404 Issue")
    print("=" * 40)
    
    # Solution 1: Ensure APP_DEBUG=true in .env
    print("1. 📝 Checking .env configuration...")
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file, "r") as f:
            lines = f.readlines()
        
        # Check and fix APP_DEBUG
        fixed_lines = []
        app_debug_found = False
        
        for line in lines:
            if line.startswith("APP_DEBUG="):
                if "false" in line:
                    fixed_lines.append("APP_DEBUG=true\n")
                    print("   ✅ Changed APP_DEBUG=false to APP_DEBUG=true")
                else:
                    fixed_lines.append(line)
                    print("   ✅ APP_DEBUG already set to true")
                app_debug_found = True
            else:
                fixed_lines.append(line)
        
        if not app_debug_found:
            # Add APP_DEBUG=true after PORT line
            for i, line in enumerate(fixed_lines):
                if line.startswith("PORT="):
                    fixed_lines.insert(i + 1, "APP_DEBUG=true\n")
                    print("   ✅ Added APP_DEBUG=true")
                    break
        
        # Write back
        with open(env_file, "w") as f:
            f.writelines(fixed_lines)
        
        print("   ✅ .env file updated")
    else:
        print("   ❌ .env file not found")
    
    # Solution 2: Force enable docs in app.py (optional)
    print("\n2. 🚀 Checking FastAPI app configuration...")
    app_file = Path("api_utils/app.py")
    
    if app_file.exists():
        with open(app_file, "r") as f:
            content = f.read()
        
        # Check if docs are conditionally enabled
        if 'docs_url="/docs" if settings.debug else None' in content:
            print("   ⚠️  Docs are conditionally enabled (only when debug=True)")
            print("   💡 This is correct behavior - ensure APP_DEBUG=true instead")
        elif 'docs_url="/docs"' in content:
            print("   ✅ Docs are permanently enabled")
        else:
            print("   ❌ Docs configuration not found")
    else:
        print("   ❌ app.py file not found")
    
    # Solution 3: Test the configuration
    print("\n3. 🧪 Testing configuration...")
    try:
        # Set environment variable for this test
        os.environ["APP_DEBUG"] = "true"
        
        from runninghub_proxy.config.settings import settings
        print(f"   Settings debug value: {settings.debug}")
        
        if settings.debug:
            print("   ✅ Debug is enabled - docs should work")
        else:
            print("   ❌ Debug is disabled - this is the problem")
            
            # Try to understand why
            app_debug_env = os.environ.get("APP_DEBUG", "NOT SET")
            print(f"   APP_DEBUG environment: {app_debug_env}")
            
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
    
    return True


def provide_restart_instructions():
    """Provide restart instructions."""
    print("\n🔄 Next Steps:")
    print("=" * 40)
    print("1. Stop the current server:")
    print("   Press Ctrl+C in the terminal where the server is running")
    print()
    print("2. Restart the server:")
    print("   uv run python server.py")
    print()
    print("3. Test the /docs endpoint:")
    print("   Open browser: http://localhost:8973/docs")
    print("   Or run: curl http://localhost:8973/docs")
    print()
    print("4. Verify other endpoints:")
    print("   Health: http://localhost:8973/health")
    print("   ReDoc: http://localhost:8973/redoc")
    print("   OpenAPI: http://localhost:8973/openapi.json")


def main():
    """Main fix function."""
    print("🔧 RunningHub Proxy - Fix /docs 404 Issue")
    print("=" * 60)
    
    success = fix_docs_404()
    
    if success:
        provide_restart_instructions()
        
        print("\n💡 Why this happened:")
        print("The /docs endpoint is only enabled when debug=True")
        print("This is a security feature to hide docs in production")
        print("APP_DEBUG=true enables the interactive documentation")
        
        print("\n🎯 Expected result:")
        print("After restart, /docs should return 200 OK with Swagger UI")
    
    return success


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
