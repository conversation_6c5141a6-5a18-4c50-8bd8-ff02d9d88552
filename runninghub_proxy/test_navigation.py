#!/usr/bin/env python3
"""Test browser navigation to RunningHub.cn"""

import asyncio
import sys
from playwright.async_api import async_playwright


async def test_navigation():
    """Test navigation to RunningHub.cn with different strategies."""
    print("🌐 Testing Navigation to RunningHub.cn")
    print("=" * 50)
    
    async with async_playwright() as p:
        # Launch browser in non-headless mode
        browser = await p.chromium.launch(
            headless=False,
            args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--start-maximized",
                "--disable-infobars",
                "--disable-notifications",
            ],
            slow_mo=500  # Slow down for debugging
        )
        
        try:
            # Create context
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # Create page
            page = await context.new_page()
            
            print("🔄 Testing different navigation strategies...")
            
            # Strategy 1: Direct navigation
            print("\n1. Testing direct navigation...")
            try:
                response = await page.goto("https://www.runninghub.cn", timeout=60000)
                if response:
                    print(f"   Status: {response.status}")
                    print(f"   URL: {page.url}")
                    if response.status < 400 and page.url != "about:blank":
                        print("   ✅ Direct navigation successful!")
                        return True
                    else:
                        print("   ❌ Direct navigation failed")
                else:
                    print("   ❌ No response received")
            except Exception as e:
                print(f"   ❌ Direct navigation error: {e}")
            
            # Strategy 2: Navigation with domcontentloaded
            print("\n2. Testing navigation with domcontentloaded...")
            try:
                response = await page.goto("https://www.runninghub.cn", wait_until="domcontentloaded", timeout=60000)
                if response:
                    print(f"   Status: {response.status}")
                    print(f"   URL: {page.url}")
                    if response.status < 400 and page.url != "about:blank":
                        print("   ✅ DOMContentLoaded navigation successful!")
                        return True
                    else:
                        print("   ❌ DOMContentLoaded navigation failed")
                else:
                    print("   ❌ No response received")
            except Exception as e:
                print(f"   ❌ DOMContentLoaded navigation error: {e}")
            
            # Strategy 3: Try alternative URL
            print("\n3. Testing alternative URL (without www)...")
            try:
                response = await page.goto("https://runninghub.cn", wait_until="domcontentloaded", timeout=60000)
                if response:
                    print(f"   Status: {response.status}")
                    print(f"   URL: {page.url}")
                    if response.status < 400 and page.url != "about:blank":
                        print("   ✅ Alternative URL navigation successful!")
                        return True
                    else:
                        print("   ❌ Alternative URL navigation failed")
                else:
                    print("   ❌ No response received")
            except Exception as e:
                print(f"   ❌ Alternative URL navigation error: {e}")
            
            # Strategy 4: Test with a known working site first
            print("\n4. Testing with a known working site (Google)...")
            try:
                response = await page.goto("https://www.google.com", wait_until="domcontentloaded", timeout=30000)
                if response:
                    print(f"   Status: {response.status}")
                    print(f"   URL: {page.url}")
                    if response.status < 400 and page.url != "about:blank":
                        print("   ✅ Google navigation successful!")
                        print("   Network connectivity is working")
                        
                        # Now try RunningHub again
                        print("   Trying RunningHub.cn again...")
                        response2 = await page.goto("https://www.runninghub.cn", wait_until="domcontentloaded", timeout=60000)
                        if response2:
                            print(f"   RunningHub Status: {response2.status}")
                            print(f"   RunningHub URL: {page.url}")
                            if response2.status < 400 and page.url != "about:blank":
                                print("   ✅ RunningHub navigation successful after Google!")
                                return True
                    else:
                        print("   ❌ Google navigation failed - network issue?")
                else:
                    print("   ❌ No response from Google")
            except Exception as e:
                print(f"   ❌ Google navigation error: {e}")
            
            print("\n❌ All navigation strategies failed")
            return False
            
        finally:
            await browser.close()


async def main():
    """Main test function."""
    try:
        success = await test_navigation()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 Navigation test PASSED!")
            print("The browser can successfully navigate to RunningHub.cn")
            print("You can now restart your server and try authentication again.")
        else:
            print("❌ Navigation test FAILED!")
            print("Possible issues:")
            print("1. Network connectivity problems")
            print("2. RunningHub.cn is down or blocking requests")
            print("3. Firewall or proxy blocking the connection")
            print("4. DNS resolution issues")
            print("\nTroubleshooting:")
            print("- Check if you can access https://www.runninghub.cn in a regular browser")
            print("- Check your internet connection")
            print("- Try using a VPN if the site is geo-blocked")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
