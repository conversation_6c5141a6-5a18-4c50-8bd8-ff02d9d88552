"""Authentication data models for RunningHub Proxy API."""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from ..config.constants import AuthStatus


class AuthMethod(str, Enum):
    """Authentication method types."""
    PHONE_SMS = "phone_sms"
    WECHAT = "wechat"
    PASSWORD = "password"
    QR_CODE = "qr_code"


class AuthRequest(BaseModel):
    """Authentication request model."""
    
    method: AuthMethod = Field(..., description="Authentication method")
    phone: Optional[str] = Field(default=None, description="Phone number for SMS auth")
    sms_code: Optional[str] = Field(default=None, description="SMS verification code")
    password: Optional[str] = Field(default=None, description="Password for password auth")
    username: Optional[str] = Field(default=None, description="Username for password auth")
    timeout: Optional[int] = Field(
        default=300, 
        description="Authentication timeout in seconds"
    )
    
    @validator('phone')
    def validate_phone(cls, v, values):
        """Validate phone number when using phone_sms method."""
        if values.get('method') == AuthMethod.PHONE_SMS and not v:
            raise ValueError('Phone number is required for SMS authentication')
        return v
    
    @validator('sms_code')
    def validate_sms_code(cls, v, values):
        """Validate SMS code when provided."""
        if v and len(v) != 6:
            raise ValueError('SMS code must be 6 digits')
        return v


class AuthResponse(BaseModel):
    """Authentication response model."""
    
    status: AuthStatus = Field(..., description="Authentication status")
    session_token: Optional[str] = Field(
        default=None, 
        description="Session token for authenticated requests"
    )
    expires_at: Optional[datetime] = Field(
        default=None, 
        description="Session expiration time"
    )
    user_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="User information if available"
    )
    message: str = Field(..., description="Status message")
    requires_manual_action: bool = Field(
        default=False, 
        description="Whether manual user action is required"
    )
    manual_action_type: Optional[str] = Field(
        default=None, 
        description="Type of manual action required (e.g., 'qr_scan', 'sms_input')"
    )
    qr_code_url: Optional[str] = Field(
        default=None, 
        description="QR code URL for WeChat login"
    )


class SessionInfo(BaseModel):
    """Session information model."""
    
    session_token: str = Field(..., description="Session token")
    user_id: Optional[str] = Field(default=None, description="User identifier")
    username: Optional[str] = Field(default=None, description="Username")
    email: Optional[str] = Field(default=None, description="User email")
    phone: Optional[str] = Field(default=None, description="User phone number")
    avatar_url: Optional[str] = Field(default=None, description="User avatar URL")
    created_at: datetime = Field(..., description="Session creation time")
    expires_at: datetime = Field(..., description="Session expiration time")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    is_active: bool = Field(default=True, description="Whether session is active")


class AuthStatus(BaseModel):
    """Current authentication status."""
    
    is_authenticated: bool = Field(..., description="Whether user is authenticated")
    session_valid: bool = Field(..., description="Whether session is valid")
    user_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Current user information"
    )
    session_expires_at: Optional[datetime] = Field(
        default=None, 
        description="Session expiration time"
    )
    time_until_expiry: Optional[int] = Field(
        default=None, 
        description="Seconds until session expires"
    )


class LogoutRequest(BaseModel):
    """Logout request model."""
    
    session_token: Optional[str] = Field(
        default=None, 
        description="Session token to logout (optional if using current session)"
    )
    logout_all_sessions: bool = Field(
        default=False, 
        description="Whether to logout all user sessions"
    )


class LogoutResponse(BaseModel):
    """Logout response model."""
    
    success: bool = Field(..., description="Whether logout was successful")
    message: str = Field(..., description="Logout status message")
    sessions_cleared: int = Field(
        default=1, 
        description="Number of sessions cleared"
    )
