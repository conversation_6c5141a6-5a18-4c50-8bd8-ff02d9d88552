"""Custom exception classes for RunningHub Proxy API."""

from typing import Optional, Dict, Any
from fastapi import HTTPException
from ..config.constants import HTTP_STATUS


class RunningHubProxyException(Exception):
    """Base exception class for RunningHub Proxy API."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(RunningHubProxyException):
    """Authentication related errors."""
    
    def __init__(
        self, 
        message: str = "Authentication failed", 
        error_code: str = "AUTH_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class AuthenticationRequiredError(HTTPException):
    """Authentication required HTTP exception."""
    
    def __init__(self, detail: str = "Authentication required"):
        super().__init__(
            status_code=HTTP_STATUS["UNAUTHORIZED"],
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class SessionExpiredError(HTTPException):
    """Session expired HTTP exception."""
    
    def __init__(self, detail: str = "Session has expired"):
        super().__init__(
            status_code=HTTP_STATUS["UNAUTHORIZED"],
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class WorkflowNotFoundError(HTTPException):
    """Workflow not found HTTP exception."""
    
    def __init__(self, workflow_id: str):
        super().__init__(
            status_code=HTTP_STATUS["NOT_FOUND"],
            detail=f"Workflow with ID '{workflow_id}' not found"
        )


class WorkflowExecutionError(RunningHubProxyException):
    """Workflow execution related errors."""
    
    def __init__(
        self, 
        message: str = "Workflow execution failed", 
        error_code: str = "EXECUTION_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class WorkflowTimeoutError(RunningHubProxyException):
    """Workflow execution timeout error."""
    
    def __init__(
        self, 
        timeout_seconds: int,
        error_code: str = "EXECUTION_TIMEOUT"
    ):
        message = f"Workflow execution timed out after {timeout_seconds} seconds"
        super().__init__(message, error_code, {"timeout": timeout_seconds})


class BrowserAutomationError(RunningHubProxyException):
    """Browser automation related errors."""
    
    def __init__(
        self, 
        message: str = "Browser automation failed", 
        error_code: str = "BROWSER_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class ElementNotFoundError(BrowserAutomationError):
    """Element not found in browser automation."""
    
    def __init__(self, selector: str):
        message = f"Element not found: {selector}"
        super().__init__(message, "ELEMENT_NOT_FOUND", {"selector": selector})


class PageLoadError(BrowserAutomationError):
    """Page load error in browser automation."""
    
    def __init__(self, url: str, error_details: Optional[str] = None):
        message = f"Failed to load page: {url}"
        if error_details:
            message += f" - {error_details}"
        super().__init__(message, "PAGE_LOAD_ERROR", {"url": url, "details": error_details})


class SchemaExtractionError(RunningHubProxyException):
    """Schema extraction related errors."""
    
    def __init__(
        self, 
        message: str = "Failed to extract workflow schema", 
        error_code: str = "SCHEMA_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class InvalidWorkflowDataError(HTTPException):
    """Invalid workflow data HTTP exception."""
    
    def __init__(self, detail: str = "Invalid workflow data provided"):
        super().__init__(
            status_code=HTTP_STATUS["UNPROCESSABLE_ENTITY"],
            detail=detail
        )


class ConcurrencyLimitError(HTTPException):
    """Concurrency limit exceeded HTTP exception."""
    
    def __init__(self, limit: int):
        super().__init__(
            status_code=HTTP_STATUS["SERVICE_UNAVAILABLE"],
            detail=f"Maximum concurrent workflows limit ({limit}) exceeded. Please try again later."
        )


class FileDownloadError(RunningHubProxyException):
    """File download related errors."""
    
    def __init__(
        self, 
        message: str = "File download failed", 
        error_code: str = "DOWNLOAD_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class ConfigurationError(RunningHubProxyException):
    """Configuration related errors."""
    
    def __init__(
        self, 
        message: str = "Configuration error", 
        error_code: str = "CONFIG_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, details)


class RateLimitError(HTTPException):
    """Rate limit exceeded HTTP exception."""
    
    def __init__(self, retry_after: Optional[int] = None):
        headers = {}
        if retry_after:
            headers["Retry-After"] = str(retry_after)
        
        super().__init__(
            status_code=429,  # Too Many Requests
            detail="Rate limit exceeded. Please try again later.",
            headers=headers
        )
