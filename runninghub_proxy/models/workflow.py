"""Workflow data models for RunningHub Proxy API."""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from ..config.constants import WorkflowStatus


class NodeInfo(BaseModel):
    """Individual node information in workflow."""
    
    node_id: str = Field(..., description="Unique node identifier")
    field_name: str = Field(..., description="Field name for the node")
    field_value: Union[str, int, float, bool, List, Dict] = Field(
        ..., description="Field value or placeholder"
    )
    class_type: str = Field(..., description="Node class type")
    meta: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Metadata including title and other properties",
        alias="_meta"
    )
    
    model_config = {"populate_by_name": True}


class WorkflowSchema(BaseModel):
    """Workflow schema definition."""
    
    workflow_id: str = Field(..., description="Unique workflow identifier")
    title: Optional[str] = Field(default=None, description="Workflow title")
    description: Optional[str] = Field(default=None, description="Workflow description")
    author: Optional[str] = Field(default=None, description="Workflow author")
    tags: Optional[List[str]] = Field(default=None, description="Workflow tags")
    node_info_list: List[NodeInfo] = Field(
        ..., 
        description="List of node information",
        alias="nodeInfo_list"
    )
    created_at: Optional[datetime] = Field(default=None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    model_config = {"populate_by_name": True}


class WorkflowExecutionRequest(BaseModel):
    """Request model for workflow execution."""
    
    workflow_id: str = Field(..., description="Workflow identifier")
    node_info_list: List[NodeInfo] = Field(
        ..., 
        description="Populated node information list",
        alias="nodeInfo_list"
    )
    execution_options: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Additional execution options"
    )
    callback_url: Optional[str] = Field(
        default=None, 
        description="Callback URL for execution results"
    )
    
    model_config = {"populate_by_name": True}


class WorkflowProgress(BaseModel):
    """Workflow execution progress information."""
    
    execution_id: str = Field(..., description="Execution identifier")
    workflow_id: str = Field(..., description="Workflow identifier")
    status: WorkflowStatus = Field(..., description="Current execution status")
    progress_percent: float = Field(
        ..., 
        ge=0.0, 
        le=100.0, 
        description="Progress percentage (0-100)"
    )
    current_node: Optional[str] = Field(
        default=None, 
        description="Currently executing node"
    )
    message: Optional[str] = Field(default=None, description="Status message")
    started_at: datetime = Field(..., description="Execution start time")
    updated_at: datetime = Field(..., description="Last update time")
    estimated_completion: Optional[datetime] = Field(
        default=None, 
        description="Estimated completion time"
    )


class WorkflowResult(BaseModel):
    """Workflow execution result."""
    
    execution_id: str = Field(..., description="Execution identifier")
    workflow_id: str = Field(..., description="Workflow identifier")
    status: WorkflowStatus = Field(..., description="Final execution status")
    results: Dict[str, Any] = Field(..., description="Execution results")
    output_files: Optional[List[str]] = Field(
        default=None, 
        description="Generated output file URLs"
    )
    execution_time: float = Field(..., description="Total execution time in seconds")
    started_at: datetime = Field(..., description="Execution start time")
    completed_at: datetime = Field(..., description="Execution completion time")
    error_message: Optional[str] = Field(
        default=None, 
        description="Error message if execution failed"
    )


class WorkflowHistoryItem(BaseModel):
    """Individual workflow execution history item."""
    
    execution_id: str = Field(..., description="Execution identifier")
    workflow_id: str = Field(..., description="Workflow identifier")
    workflow_title: Optional[str] = Field(default=None, description="Workflow title")
    status: WorkflowStatus = Field(..., description="Execution status")
    started_at: datetime = Field(..., description="Execution start time")
    completed_at: Optional[datetime] = Field(
        default=None, 
        description="Execution completion time"
    )
    execution_time: Optional[float] = Field(
        default=None, 
        description="Total execution time in seconds"
    )
    output_count: int = Field(default=0, description="Number of output files generated")


class WorkflowHistory(BaseModel):
    """Workflow execution history response."""
    
    total_count: int = Field(..., description="Total number of executions")
    page: int = Field(default=1, description="Current page number")
    page_size: int = Field(default=20, description="Items per page")
    executions: List[WorkflowHistoryItem] = Field(
        ..., 
        description="List of workflow executions"
    )
