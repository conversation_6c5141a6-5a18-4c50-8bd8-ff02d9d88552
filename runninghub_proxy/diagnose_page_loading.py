#!/usr/bin/env python3
"""Comprehensive diagnostic script for page loading issues."""

import asyncio
import sys
import os
import time
import socket
import subprocess
from playwright.async_api import async_playwright
from datetime import datetime


class PageLoadDiagnostics:
    """Comprehensive page loading diagnostics."""
    
    def __init__(self):
        self.test_results = {}
        self.network_logs = []
        self.console_logs = []
        self.error_logs = []
        
    def log(self, message: str, level: str = "INFO"):
        """Log with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
        
    async def test_network_connectivity(self):
        """Test basic network connectivity."""
        self.log("🌐 Testing Network Connectivity", "INFO")
        print("-" * 50)
        
        # Test DNS resolution
        try:
            self.log("Testing DNS resolution for runninghub.cn...")
            result = socket.gethostbyname("runninghub.cn")
            self.log(f"✅ DNS resolved: runninghub.cn -> {result}")
            self.test_results["dns_resolution"] = True
        except Exception as e:
            self.log(f"❌ DNS resolution failed: {e}", "ERROR")
            self.test_results["dns_resolution"] = False
        
        # Test with www
        try:
            self.log("Testing DNS resolution for www.runninghub.cn...")
            result = socket.gethostbyname("www.runninghub.cn")
            self.log(f"✅ DNS resolved: www.runninghub.cn -> {result}")
            self.test_results["dns_resolution_www"] = True
        except Exception as e:
            self.log(f"❌ DNS resolution failed: {e}", "ERROR")
            self.test_results["dns_resolution_www"] = False
        
        # Test HTTP connectivity with curl
        self.log("Testing HTTP connectivity with curl...")
        try:
            result = subprocess.run(
                ["curl", "-I", "--connect-timeout", "10", "--max-time", "30", "https://www.runninghub.cn"],
                capture_output=True, text=True, timeout=35
            )
            if result.returncode == 0:
                self.log("✅ curl connection successful")
                self.log(f"Response headers:\n{result.stdout}")
                self.test_results["curl_connectivity"] = True
            else:
                self.log(f"❌ curl failed: {result.stderr}", "ERROR")
                self.test_results["curl_connectivity"] = False
        except Exception as e:
            self.log(f"❌ curl test failed: {e}", "ERROR")
            self.test_results["curl_connectivity"] = False
        
        print()
    
    async def test_playwright_basic(self):
        """Test basic Playwright functionality."""
        self.log("🎭 Testing Basic Playwright Functionality", "INFO")
        print("-" * 50)
        
        try:
            async with async_playwright() as p:
                # Test browser launch
                self.log("Launching browser...")
                browser = await p.chromium.launch(
                    headless=False,
                    args=[
                        "--no-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                    ]
                )
                self.log("✅ Browser launched successfully")
                
                # Test context creation
                self.log("Creating browser context...")
                context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                )
                self.log("✅ Browser context created")
                
                # Test page creation
                self.log("Creating new page...")
                page = await context.new_page()
                self.log("✅ Page created successfully")
                self.log(f"Initial page URL: {page.url}")
                
                # Test simple navigation (Google)
                self.log("Testing navigation to Google...")
                try:
                    response = await page.goto("https://www.google.com", timeout=30000)
                    self.log(f"✅ Google navigation: {response.status} {page.url}")
                    self.test_results["google_navigation"] = True
                except Exception as e:
                    self.log(f"❌ Google navigation failed: {e}", "ERROR")
                    self.test_results["google_navigation"] = False
                
                await browser.close()
                self.log("✅ Browser closed successfully")
                self.test_results["playwright_basic"] = True
                
        except Exception as e:
            self.log(f"❌ Playwright basic test failed: {e}", "ERROR")
            self.test_results["playwright_basic"] = False
        
        print()
    
    async def test_runninghub_navigation(self):
        """Test RunningHub.cn navigation with detailed logging."""
        self.log("🏃 Testing RunningHub.cn Navigation", "INFO")
        print("-" * 50)
        
        try:
            async with async_playwright() as p:
                # Launch with debug options
                browser = await p.chromium.launch(
                    headless=False,
                    args=[
                        "--no-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--enable-logging",
                        "--log-level=0",
                        "--v=1",
                    ],
                    slow_mo=500  # Slow down for observation
                )
                
                context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                )
                
                # Set up comprehensive logging
                async def log_request(request):
                    self.log(f"🌐 REQUEST: {request.method} {request.url}")
                    self.network_logs.append(f"REQUEST: {request.method} {request.url}")
                
                async def log_response(response):
                    self.log(f"📡 RESPONSE: {response.status} {response.url}")
                    self.network_logs.append(f"RESPONSE: {response.status} {response.url}")
                    if response.status >= 400:
                        self.error_logs.append(f"HTTP {response.status}: {response.url}")
                
                async def log_request_failed(request):
                    self.log(f"💥 REQUEST FAILED: {request.url}", "ERROR")
                    self.error_logs.append(f"REQUEST FAILED: {request.url}")
                
                async def log_console(msg):
                    self.log(f"🔵 CONSOLE {msg.type}: {msg.text}")
                    self.console_logs.append(f"{msg.type}: {msg.text}")
                
                context.on("request", log_request)
                context.on("response", log_response)
                context.on("requestfailed", log_request_failed)
                
                page = await context.new_page()
                page.on("console", log_console)
                
                # Test different URLs and strategies
                test_urls = [
                    "https://www.runninghub.cn",
                    "https://runninghub.cn",
                    "http://www.runninghub.cn",
                ]
                
                wait_strategies = ["domcontentloaded", "load", "networkidle"]
                
                for url in test_urls:
                    for strategy in wait_strategies:
                        self.log(f"Testing {url} with {strategy}...")
                        try:
                            start_time = time.time()
                            response = await page.goto(url, wait_until=strategy, timeout=60000)
                            end_time = time.time()
                            
                            self.log(f"✅ Success: {response.status} in {end_time-start_time:.2f}s")
                            self.log(f"Final URL: {page.url}")
                            
                            # Try to get page title
                            try:
                                title = await page.title()
                                self.log(f"Page title: {title}")
                            except:
                                pass
                            
                            self.test_results[f"{url}_{strategy}"] = True
                            break  # Success, move to next URL
                            
                        except Exception as e:
                            self.log(f"❌ Failed: {e}", "ERROR")
                            self.test_results[f"{url}_{strategy}"] = False
                            
                            # Wait before next attempt
                            await asyncio.sleep(2)
                
                await browser.close()
                
        except Exception as e:
            self.log(f"❌ RunningHub navigation test failed: {e}", "ERROR")
            self.test_results["runninghub_navigation"] = False
        
        print()
    
    def print_summary(self):
        """Print comprehensive test summary."""
        self.log("📊 DIAGNOSTIC SUMMARY", "INFO")
        print("=" * 60)
        
        # Test results
        print("🧪 Test Results:")
        for test, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test}")
        
        # Network logs
        if self.network_logs:
            print(f"\n🌐 Network Activity ({len(self.network_logs)} events):")
            for log in self.network_logs[-10:]:  # Show last 10
                print(f"  {log}")
            if len(self.network_logs) > 10:
                print(f"  ... and {len(self.network_logs) - 10} more")
        
        # Console logs
        if self.console_logs:
            print(f"\n🔵 Console Messages ({len(self.console_logs)} messages):")
            for log in self.console_logs[-5:]:  # Show last 5
                print(f"  {log}")
        
        # Error logs
        if self.error_logs:
            print(f"\n❌ Errors ({len(self.error_logs)} errors):")
            for error in self.error_logs:
                print(f"  {error}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        
        if not self.test_results.get("dns_resolution", False):
            print("  🔧 DNS resolution failed - check internet connection")
        
        if not self.test_results.get("curl_connectivity", False):
            print("  🔧 HTTP connectivity failed - site may be down or blocked")
        
        if not self.test_results.get("google_navigation", False):
            print("  🔧 Basic navigation failed - Playwright/browser issue")
        
        if self.error_logs:
            print("  🔧 Network errors detected - check firewall/proxy settings")
        
        success_rate = sum(self.test_results.values()) / len(self.test_results) * 100
        print(f"\n📈 Overall Success Rate: {success_rate:.1f}%")
        
        if success_rate < 50:
            print("🚨 Major connectivity issues detected!")
        elif success_rate < 80:
            print("⚠️  Some issues detected, but basic functionality works")
        else:
            print("✅ Most tests passed - minor issues only")


async def main():
    """Run comprehensive diagnostics."""
    print("🔍 RunningHub Proxy - Page Loading Diagnostics")
    print("=" * 60)
    print(f"Start time: {datetime.now().isoformat()}")
    print()
    
    # Set debug environment variables
    os.environ["DEBUG"] = "pw:api,pw:browser*"
    os.environ["PWDEBUG"] = "1"
    
    diagnostics = PageLoadDiagnostics()
    
    # Run all diagnostic tests
    await diagnostics.test_network_connectivity()
    await diagnostics.test_playwright_basic()
    await diagnostics.test_runninghub_navigation()
    
    # Print summary
    diagnostics.print_summary()
    
    return sum(diagnostics.test_results.values()) > len(diagnostics.test_results) / 2


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Diagnostics interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Diagnostics failed: {e}")
        sys.exit(1)
