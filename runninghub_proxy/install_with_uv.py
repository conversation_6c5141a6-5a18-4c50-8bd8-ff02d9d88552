#!/usr/bin/env python3
"""Installation script for RunningHub Proxy API using uv."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command: str, description: str = "") -> bool:
    """Run a command and return success status."""
    if description:
        print(f"🔄 {description}...")
    
    print(f"   Running: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ {description or 'Command'} completed successfully")
        if result.stdout.strip():
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed")
        if e.stdout:
            print(f"   STDOUT: {e.stdout}")
        if e.stderr:
            print(f"   STDERR: {e.stderr}")
        return False


def check_uv_installation():
    """Check if uv is installed and install if needed."""
    print("🔍 Checking uv installation...")
    
    if shutil.which("uv"):
        # Get uv version
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            print(f"✅ uv is installed: {result.stdout.strip()}")
            return True
        except Exception as e:
            print(f"⚠️  uv found but version check failed: {e}")
    
    print("📦 uv not found. Installing uv...")
    
    # Install uv
    install_command = "curl -LsSf https://astral.sh/uv/install.sh | sh"
    if run_command(install_command, "Installing uv"):
        print("✅ uv installed successfully")
        print("⚠️  You may need to restart your terminal or run: source ~/.bashrc")
        return True
    else:
        print("❌ Failed to install uv")
        return False


def install_dependencies():
    """Install project dependencies using uv."""
    print("\n📦 Installing project dependencies...")

    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)

    # Install dependencies with uv sync (creates venv automatically)
    commands = [
        ("uv sync", "Installing dependencies and creating virtual environment"),
        ("uv run playwright install chromium", "Installing Playwright browsers"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            return False

    return True


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating necessary directories...")
    
    directories = ["logs", "temp", "downloads"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def setup_environment():
    """Setup environment configuration."""
    print("\n⚙️  Setting up environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from .env.example")
        print("📝 Please edit .env file to configure your settings")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")
    
    return True


def test_installation():
    """Test the installation."""
    print("\n🧪 Testing installation...")
    
    test_commands = [
        ("uv run python -c 'import runninghub_proxy; print(\"✅ Module import successful\")'", "Testing module import"),
        ("uv run python -c 'from runninghub_proxy.config.settings import settings; print(\"✅ Settings loaded\")'", "Testing configuration"),
        ("uv run python -c 'from playwright.async_api import async_playwright; print(\"✅ Playwright available\")'", "Testing Playwright"),
    ]
    
    all_passed = True
    for command, description in test_commands:
        if not run_command(command, description):
            all_passed = False
    
    return all_passed


def main():
    """Main installation function."""
    print("🚀 RunningHub Proxy API - UV Installation Script")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version_info.major}.{sys.version_info.minor}")
    
    # Step 1: Check/install uv
    if not check_uv_installation():
        return False
    
    # Step 2: Install dependencies
    if not install_dependencies():
        return False
    
    # Step 3: Create directories
    if not create_directories():
        return False
    
    # Step 4: Setup environment
    if not setup_environment():
        return False
    
    # Step 5: Test installation
    if not test_installation():
        print("⚠️  Some tests failed, but installation may still work")
    
    # Success message
    print("\n" + "=" * 60)
    print("🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file to configure your settings:")
    print("   nano .env")
    print("\n2. Start the server:")
    print("   uv run python server.py")
    print("\n3. Visit the API documentation:")
    print("   http://localhost:8000/docs")
    print("\n4. Run tests:")
    print("   uv run python test_stability.py")
    print("\n💡 Useful commands:")
    print("   uv run python server.py          # Start server")
    print("   uv run pytest tests/             # Run tests")
    print("   uv add <package>                 # Add new dependency")
    print("   uv sync                          # Update dependencies")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed with error: {e}")
        sys.exit(1)
