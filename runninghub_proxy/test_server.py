#!/usr/bin/env python3
"""Test server startup and basic functionality."""

import asyncio
import sys
import time
import subprocess
import signal
import requests
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))


class ServerTester:
    """Test server startup and basic functionality."""
    
    def __init__(self):
        self.server_process = None
        self.base_url = "http://localhost:8000"
    
    def log(self, message: str, level: str = "INFO"):
        """Log messages with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def start_server(self, timeout: int = 30):
        """Start the server process."""
        self.log("🚀 Starting RunningHub Proxy API server...")
        
        try:
            # Start server process
            self.server_process = subprocess.Popen(
                [sys.executable, "server.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for server to start
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=2)
                    if response.status_code == 200:
                        self.log("✅ Server started successfully")
                        return True
                except requests.exceptions.ConnectionError:
                    time.sleep(1)
                    continue
                except Exception as e:
                    self.log(f"⚠️  Health check error: {e}", "WARNING")
                    time.sleep(1)
                    continue
            
            self.log("❌ Server failed to start within timeout", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"❌ Failed to start server: {e}", "ERROR")
            return False
    
    def stop_server(self):
        """Stop the server process."""
        if self.server_process:
            self.log("🛑 Stopping server...")
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                self.log("✅ Server stopped successfully")
            except subprocess.TimeoutExpired:
                self.log("⚠️  Force killing server...", "WARNING")
                self.server_process.kill()
                self.server_process.wait()
            except Exception as e:
                self.log(f"⚠️  Error stopping server: {e}", "WARNING")
    
    def test_basic_endpoints(self):
        """Test basic server endpoints."""
        self.log("🧪 Testing basic endpoints...")
        
        tests = [
            ("Health Check", "GET", "/health", 200),
            ("API Docs", "GET", "/docs", 200),
            ("OpenAPI Schema", "GET", "/openapi.json", 200),
            ("Auth Status (No Token)", "GET", "/auth/status", 200),
            ("Nonexistent Endpoint", "GET", "/nonexistent", 404),
        ]
        
        results = []
        
        for test_name, method, endpoint, expected_status in tests:
            try:
                if method == "GET":
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                else:
                    response = requests.request(method, f"{self.base_url}{endpoint}", timeout=10)
                
                if response.status_code == expected_status:
                    self.log(f"✅ {test_name}: {response.status_code}")
                    results.append(True)
                else:
                    self.log(f"❌ {test_name}: Expected {expected_status}, got {response.status_code}", "ERROR")
                    results.append(False)
                    
            except Exception as e:
                self.log(f"❌ {test_name}: {e}", "ERROR")
                results.append(False)
        
        return all(results)
    
    def test_api_structure(self):
        """Test API structure and documentation."""
        self.log("🧪 Testing API structure...")
        
        try:
            # Get OpenAPI schema
            response = requests.get(f"{self.base_url}/openapi.json", timeout=10)
            if response.status_code != 200:
                self.log("❌ Cannot access OpenAPI schema", "ERROR")
                return False
            
            schema = response.json()
            
            # Check required fields
            required_fields = ["openapi", "info", "paths"]
            for field in required_fields:
                if field not in schema:
                    self.log(f"❌ Missing required field in OpenAPI schema: {field}", "ERROR")
                    return False
            
            # Check for key endpoints
            paths = schema["paths"]
            required_endpoints = [
                "/health",
                "/auth/login",
                "/auth/status",
                "/workflows/{workflow_id}/schema",
                "/workflows/{workflow_id}/execute"
            ]
            
            missing_endpoints = []
            for endpoint in required_endpoints:
                if endpoint not in paths:
                    missing_endpoints.append(endpoint)
            
            if missing_endpoints:
                self.log(f"❌ Missing endpoints: {missing_endpoints}", "ERROR")
                return False
            
            self.log("✅ API structure is correct")
            return True
            
        except Exception as e:
            self.log(f"❌ API structure test failed: {e}", "ERROR")
            return False
    
    def test_error_handling(self):
        """Test error handling."""
        self.log("🧪 Testing error handling...")
        
        try:
            # Test invalid JSON
            response = requests.post(
                f"{self.base_url}/auth/login",
                data="invalid json",
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code in [400, 422]:
                self.log("✅ Invalid JSON handling works")
            else:
                self.log(f"⚠️  Unexpected response to invalid JSON: {response.status_code}", "WARNING")
            
            # Test missing required fields
            response = requests.post(
                f"{self.base_url}/auth/login",
                json={},
                timeout=10
            )
            
            if response.status_code in [400, 422]:
                self.log("✅ Missing fields validation works")
            else:
                self.log(f"⚠️  Unexpected response to missing fields: {response.status_code}", "WARNING")
            
            return True
            
        except Exception as e:
            self.log(f"❌ Error handling test failed: {e}", "ERROR")
            return False
    
    def run_all_tests(self):
        """Run all server tests."""
        self.log("🧪 Starting RunningHub Proxy API Server Tests")
        self.log("=" * 60)
        
        try:
            # Start server
            if not self.start_server():
                return False
            
            # Run tests
            tests = [
                ("Basic Endpoints", self.test_basic_endpoints),
                ("API Structure", self.test_api_structure),
                ("Error Handling", self.test_error_handling),
            ]
            
            results = []
            for test_name, test_func in tests:
                self.log(f"Running {test_name} test...")
                result = test_func()
                results.append(result)
                
                if result:
                    self.log(f"✅ {test_name} test passed")
                else:
                    self.log(f"❌ {test_name} test failed", "ERROR")
            
            # Summary
            self.log("=" * 60)
            self.log("📊 Test Summary")
            self.log("=" * 60)
            
            passed = sum(results)
            total = len(results)
            
            self.log(f"Tests Passed: {passed}/{total}")
            self.log(f"Success Rate: {(passed/total)*100:.1f}%")
            
            if passed == total:
                self.log("🎉 All server tests passed!")
                return True
            else:
                self.log("⚠️  Some server tests failed")
                return False
                
        except KeyboardInterrupt:
            self.log("⚠️  Tests interrupted by user", "WARNING")
            return False
        except Exception as e:
            self.log(f"❌ Test suite failed: {e}", "ERROR")
            return False
        finally:
            self.stop_server()


def main():
    """Main test function."""
    print("🧪 RunningHub Proxy API Server Test Suite")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("server.py").exists():
        print("❌ server.py not found. Please run from project root directory.")
        return False
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    
    # Run tests
    tester = ServerTester()
    
    def signal_handler(signum, frame):
        print("\n⚠️  Test interrupted. Cleaning up...")
        tester.stop_server()
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        success = tester.run_all_tests()
        return success
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
