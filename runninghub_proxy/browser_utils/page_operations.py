"""Low-level page operations for browser automation."""

import asyncio
from typing import Optional, Union, List, Dict, Any
from playwright.async_api import Page, ElementHandle, Locator, TimeoutError as PlaywrightTimeoutError

from ..config.settings import settings
from ..config.selectors import RunningHubSelectors, SELECTOR_TIMEOUTS, WAIT_CONDITIONS
from ..models.exceptions import ElementNotFoundError, PageLoadError, BrowserAutomationError
from ..logging_utils.setup import get_logger, PerformanceLogger


class PageOperations:
    """Low-level page operations for browser automation."""
    
    def __init__(self, page: Page):
        self.page = page
        self.selectors = RunningHubSelectors()
        self.logger = get_logger(__name__)
    
    async def navigate_to(self, url: str, wait_until: str = "domcontentloaded") -> bool:
        """Navigate to URL with error handling and retries."""
        max_retries = 3
        retry_delay = 2.0

        self.logger.info(f"🚀 Starting navigation to: {url}")
        self.logger.debug(f"   Initial page URL: {self.page.url}")
        self.logger.debug(f"   Page load timeout: {SELECTOR_TIMEOUTS['page_load']}ms")

        for attempt in range(max_retries):
            try:
                with PerformanceLogger(f"navigate_to({url}) attempt {attempt + 1}"):
                    self.logger.info(f"🔄 Navigation attempt {attempt + 1}/{max_retries} to: {url}")

                    # Try different wait strategies based on attempt
                    if attempt == 0:
                        wait_strategy = "domcontentloaded"  # Fastest
                    elif attempt == 1:
                        wait_strategy = "load"  # Wait for all resources
                    else:
                        wait_strategy = "networkidle"  # Wait for network to be idle

                    self.logger.debug(f"   Using wait strategy: {wait_strategy}")
                    self.logger.debug(f"   Timeout: {SELECTOR_TIMEOUTS['page_load']}ms")

                    # Start navigation
                    self.logger.debug("   Calling page.goto()...")
                    response = await self.page.goto(
                        url,
                        wait_until=wait_strategy,
                        timeout=SELECTOR_TIMEOUTS["page_load"]
                    )
                    self.logger.debug("   page.goto() completed")

                    # Analyze response
                    if response:
                        self.logger.info(f"📡 Response received: {response.status} {response.status_text}")
                        self.logger.debug(f"   Response URL: {response.url}")
                        self.logger.debug(f"   Response headers: {dict(response.headers)}")

                        if response.status >= 400:
                            self.logger.error(f"❌ HTTP Error {response.status}: {response.status_text}")
                            if attempt < max_retries - 1:
                                self.logger.warning(f"🔄 Retrying due to HTTP {response.status}...")
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                raise PageLoadError(url, f"HTTP {response.status}: {response.status_text}")
                    else:
                        self.logger.warning("⚠️  No response object received")

                    # Verify page loaded by checking URL
                    current_url = self.page.url
                    self.logger.debug(f"   Current page URL: {current_url}")

                    if current_url == "about:blank":
                        self.logger.error("❌ Page stuck on about:blank")
                        if attempt < max_retries - 1:
                            self.logger.warning("🔄 Retrying due to about:blank...")
                            await asyncio.sleep(retry_delay)
                            continue
                        else:
                            raise PageLoadError(url, "Page stuck on about:blank")

                    if not current_url.startswith("http"):
                        self.logger.error(f"❌ Invalid URL scheme: {current_url}")
                        if attempt < max_retries - 1:
                            self.logger.warning(f"🔄 Retrying due to invalid URL: {current_url}")
                            await asyncio.sleep(retry_delay)
                            continue
                        else:
                            raise PageLoadError(url, f"Invalid URL: {current_url}")

                    # Additional page state checks
                    try:
                        page_title = await self.page.title()
                        self.logger.debug(f"   Page title: {page_title}")
                    except Exception as e:
                        self.logger.warning(f"⚠️  Could not get page title: {e}")

                    self.logger.info(f"✅ Successfully navigated to: {current_url}")
                    return True

            except PlaywrightTimeoutError as e:
                self.logger.error(f"⏰ Navigation timeout on attempt {attempt + 1}")
                self.logger.error(f"   Error details: {str(e)}")
                self.logger.error(f"   Current URL: {self.page.url}")
                self.logger.error(f"   Timeout was: {SELECTOR_TIMEOUTS['page_load']}ms")

                if attempt < max_retries - 1:
                    self.logger.warning(f"🔄 Retrying in {retry_delay}s due to timeout...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 1.5  # Exponential backoff
                    continue
                else:
                    error_msg = f"Page load timeout after {max_retries} attempts ({SELECTOR_TIMEOUTS['page_load']}ms each)"
                    self.logger.error(f"💥 Final timeout error: {error_msg}")
                    raise PageLoadError(url, error_msg)

            except Exception as e:
                self.logger.error(f"💥 Navigation error on attempt {attempt + 1}: {type(e).__name__}")
                self.logger.error(f"   Error message: {str(e)}")
                self.logger.error(f"   Current URL: {self.page.url}")

                if attempt < max_retries - 1:
                    self.logger.warning(f"🔄 Retrying in {retry_delay}s due to error...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    error_msg = f"Navigation failed after {max_retries} attempts: {str(e)}"
                    self.logger.error(f"💥 Final navigation error: {error_msg}")
                    raise PageLoadError(url, error_msg)

        return False
    
    async def wait_for_element(
        self, 
        selector: str, 
        timeout: Optional[int] = None,
        state: str = "visible"
    ) -> Locator:
        """Wait for element to appear with specified state."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            self.logger.debug(f"Waiting for element: {selector} (state: {state})")
            
            locator = self.page.locator(selector)
            await locator.wait_for(state=state, timeout=timeout)
            
            return locator
            
        except PlaywrightTimeoutError:
            raise ElementNotFoundError(selector)
        except Exception as e:
            raise BrowserAutomationError(f"Error waiting for element {selector}: {str(e)}")
    
    async def click_element(
        self, 
        selector: str, 
        timeout: Optional[int] = None,
        force: bool = False
    ) -> bool:
        """Click on element with retry logic."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            with PerformanceLogger(f"click_element({selector})"):
                self.logger.debug(f"Clicking element: {selector}")
                
                # Wait for element to be clickable
                locator = await self.wait_for_element(selector, timeout, "visible")
                
                # Scroll into view if needed
                await locator.scroll_into_view_if_needed()
                
                # Click with retry
                for attempt in range(settings.max_retries):
                    try:
                        await locator.click(force=force, timeout=5000)
                        self.logger.debug(f"Successfully clicked: {selector}")
                        return True
                    except Exception as e:
                        if attempt == settings.max_retries - 1:
                            raise
                        self.logger.warning(f"Click attempt {attempt + 1} failed: {e}")
                        await asyncio.sleep(settings.retry_delay)
                
                return False
                
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error clicking element {selector}: {str(e)}")
    
    async def type_text(
        self, 
        selector: str, 
        text: str, 
        clear_first: bool = True,
        timeout: Optional[int] = None
    ) -> bool:
        """Type text into input element."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            with PerformanceLogger(f"type_text({selector})"):
                self.logger.debug(f"Typing text into: {selector}")
                
                locator = await self.wait_for_element(selector, timeout, "visible")
                
                if clear_first:
                    await locator.clear()
                
                await locator.fill(text)
                
                # Trigger input event
                await locator.press("Tab")
                
                self.logger.debug(f"Successfully typed text into: {selector}")
                return True
                
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error typing text into {selector}: {str(e)}")
    
    async def get_element_text(self, selector: str, timeout: Optional[int] = None) -> str:
        """Get text content of element."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "visible")
            text = await locator.text_content()
            return text or ""
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error getting text from {selector}: {str(e)}")
    
    async def get_element_attribute(
        self, 
        selector: str, 
        attribute: str, 
        timeout: Optional[int] = None
    ) -> Optional[str]:
        """Get attribute value of element."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "attached")
            value = await locator.get_attribute(attribute)
            return value
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error getting attribute {attribute} from {selector}: {str(e)}")
    
    async def is_element_visible(self, selector: str, timeout: int = 1000) -> bool:
        """Check if element is visible."""
        try:
            locator = self.page.locator(selector)
            await locator.wait_for(state="visible", timeout=timeout)
            return True
        except:
            return False
    
    async def is_element_present(self, selector: str, timeout: int = 1000) -> bool:
        """Check if element is present in DOM."""
        try:
            locator = self.page.locator(selector)
            await locator.wait_for(state="attached", timeout=timeout)
            return True
        except:
            return False
    
    async def wait_for_page_load(self, timeout: Optional[int] = None):
        """Wait for page to fully load."""
        timeout = timeout or SELECTOR_TIMEOUTS["page_load"]
        
        try:
            await self.page.wait_for_load_state("domcontentloaded", timeout=timeout)
            self.logger.debug("Page load complete")
            
        except PlaywrightTimeoutError:
            raise PageLoadError(self.page.url, "Page load timeout")
    
    async def scroll_to_element(self, selector: str, timeout: Optional[int] = None):
        """Scroll element into view."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "attached")
            await locator.scroll_into_view_if_needed()
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error scrolling to element {selector}: {str(e)}")
    
    async def hover_element(self, selector: str, timeout: Optional[int] = None):
        """Hover over element."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "visible")
            await locator.hover()
            self.logger.debug(f"Hovered over element: {selector}")
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error hovering over element {selector}: {str(e)}")
    
    async def right_click_element(self, selector: str, timeout: Optional[int] = None):
        """Right-click on element to open context menu."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            with PerformanceLogger(f"right_click_element({selector})"):
                self.logger.debug(f"Right-clicking element: {selector}")
                
                locator = await self.wait_for_element(selector, timeout, "visible")
                await locator.scroll_into_view_if_needed()
                await locator.click(button="right")
                
                self.logger.debug(f"Successfully right-clicked: {selector}")
                
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error right-clicking element {selector}: {str(e)}")
    
    async def select_option(
        self, 
        selector: str, 
        value: Union[str, List[str]], 
        timeout: Optional[int] = None
    ):
        """Select option from dropdown."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "visible")
            
            if isinstance(value, str):
                await locator.select_option(value)
            else:
                await locator.select_option(value)
                
            self.logger.debug(f"Selected option {value} in: {selector}")
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error selecting option in {selector}: {str(e)}")
    
    async def upload_file(self, selector: str, file_path: str, timeout: Optional[int] = None):
        """Upload file to input element."""
        timeout = timeout or SELECTOR_TIMEOUTS["default"]
        
        try:
            locator = await self.wait_for_element(selector, timeout, "attached")
            await locator.set_input_files(file_path)
            
            self.logger.debug(f"Uploaded file {file_path} to: {selector}")
            
        except ElementNotFoundError:
            raise
        except Exception as e:
            raise BrowserAutomationError(f"Error uploading file to {selector}: {str(e)}")
    
    async def wait_for_download(self, timeout: Optional[int] = None) -> Optional[str]:
        """Wait for file download and return file path."""
        timeout = timeout or SELECTOR_TIMEOUTS["file_download"]
        
        try:
            with PerformanceLogger("wait_for_download"):
                self.logger.debug("Waiting for download...")
                
                async with self.page.expect_download(timeout=timeout) as download_info:
                    download = await download_info.value
                    
                    # Save to downloads directory
                    import os
                    file_path = os.path.join(settings.downloads_dir, download.suggested_filename)
                    await download.save_as(file_path)
                    
                    self.logger.info(f"Downloaded file: {file_path}")
                    return file_path
                    
        except PlaywrightTimeoutError:
            self.logger.warning("Download timeout")
            return None
        except Exception as e:
            raise BrowserAutomationError(f"Error during download: {str(e)}")
    
    async def execute_javascript(self, script: str, *args) -> Any:
        """Execute JavaScript in page context."""
        try:
            result = await self.page.evaluate(script, *args)
            return result
            
        except Exception as e:
            raise BrowserAutomationError(f"Error executing JavaScript: {str(e)}")
    
    async def take_screenshot(self, path: Optional[str] = None) -> bytes:
        """Take screenshot of current page."""
        try:
            if path:
                await self.page.screenshot(path=path, full_page=True)
                self.logger.debug(f"Screenshot saved to: {path}")
                return b""
            else:
                screenshot_bytes = await self.page.screenshot(full_page=True)
                return screenshot_bytes
                
        except Exception as e:
            raise BrowserAutomationError(f"Error taking screenshot: {str(e)}")
    
    async def get_page_title(self) -> str:
        """Get current page title."""
        try:
            return await self.page.title()
        except Exception as e:
            raise BrowserAutomationError(f"Error getting page title: {str(e)}")
    
    async def get_current_url(self) -> str:
        """Get current page URL."""
        return self.page.url
