"""Session management for RunningHub Proxy API."""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List
from playwright.async_api import BrowserContext

from ..config.settings import settings
from ..models.exceptions import AuthenticationError
from ..logging_utils.setup import get_logger


class SessionManager:
    """Manages user sessions and authentication state."""
    
    def __init__(self):
        # In-memory session storage (use Redis/database in production)
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.user_sessions: Dict[str, List[str]] = {}  # user_id -> [session_tokens]
        self.execution_status: Dict[str, Dict[str, Any]] = {}  # execution_id -> status
        self.pending_auth: Dict[str, Any] = {}  # session_token -> auth_handler
        self.logger = get_logger(__name__)
        self._lock = asyncio.Lock()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def create_session(
        self, 
        user_info: Optional[Dict[str, Any]] = None,
        browser_context: Optional[BrowserContext] = None
    ) -> Dict[str, Any]:
        """Create a new user session."""
        async with self._lock:
            session_token = str(uuid.uuid4())
            user_id = user_info.get("user_id") if user_info else str(uuid.uuid4())
            
            expires_at = datetime.utcnow() + timedelta(seconds=settings.session_timeout)
            
            session_info = {
                "session_token": session_token,
                "user_id": user_id,
                "user_info": user_info or {},
                "created_at": datetime.utcnow(),
                "expires_at": expires_at,
                "last_activity": datetime.utcnow(),
                "is_active": True,
                "browser_context_id": id(browser_context) if browser_context else None,
            }
            
            # Store session
            self.sessions[session_token] = session_info
            
            # Track user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = []
            self.user_sessions[user_id].append(session_token)
            
            self.logger.info(f"Created session for user: {user_id}")
            return session_info
    
    async def get_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Get session information."""
        async with self._lock:
            session_info = self.sessions.get(session_token)
            
            if session_info:
                # Update last activity
                session_info["last_activity"] = datetime.utcnow()
            
            return session_info
    
    async def is_session_valid(self, session_token: str) -> bool:
        """Check if session is valid and not expired."""
        session_info = await self.get_session(session_token)
        
        if not session_info or not session_info.get("is_active"):
            return False
        
        # Check expiration
        expires_at = session_info.get("expires_at")
        if expires_at and datetime.utcnow() > expires_at:
            await self.invalidate_session(session_token)
            return False
        
        return True
    
    async def invalidate_session(self, session_token: str):
        """Invalidate a specific session."""
        async with self._lock:
            session_info = self.sessions.get(session_token)
            
            if session_info:
                # Mark as inactive
                session_info["is_active"] = False
                
                # Remove from user sessions
                user_id = session_info.get("user_id")
                if user_id and user_id in self.user_sessions:
                    if session_token in self.user_sessions[user_id]:
                        self.user_sessions[user_id].remove(session_token)
                    
                    # Clean up empty user session list
                    if not self.user_sessions[user_id]:
                        del self.user_sessions[user_id]
                
                self.logger.info(f"Invalidated session: {session_token[:8]}...")
    
    async def clear_user_sessions(self, user_id: str) -> int:
        """Clear all sessions for a specific user."""
        async with self._lock:
            if user_id not in self.user_sessions:
                return 0
            
            session_tokens = self.user_sessions[user_id].copy()
            cleared_count = 0
            
            for session_token in session_tokens:
                if session_token in self.sessions:
                    self.sessions[session_token]["is_active"] = False
                    cleared_count += 1
            
            # Clear user session list
            del self.user_sessions[user_id]
            
            self.logger.info(f"Cleared {cleared_count} sessions for user: {user_id}")
            return cleared_count
    
    async def extend_session(self, session_token: str, extend_seconds: Optional[int] = None):
        """Extend session expiration time."""
        async with self._lock:
            session_info = self.sessions.get(session_token)
            
            if session_info and session_info.get("is_active"):
                extend_by = extend_seconds or settings.session_timeout
                new_expires_at = datetime.utcnow() + timedelta(seconds=extend_by)
                session_info["expires_at"] = new_expires_at
                session_info["last_activity"] = datetime.utcnow()
                
                self.logger.debug(f"Extended session: {session_token[:8]}...")
    
    async def store_pending_auth(self, auth_handler: Any, auth_request: Any):
        """Store pending authentication for manual completion."""
        pending_id = str(uuid.uuid4())
        
        self.pending_auth[pending_id] = {
            "auth_handler": auth_handler,
            "auth_request": auth_request,
            "created_at": datetime.utcnow(),
            "status": "pending"
        }
        
        return pending_id
    
    async def get_pending_auth(self, pending_id: str) -> Optional[Dict[str, Any]]:
        """Get pending authentication information."""
        return self.pending_auth.get(pending_id)
    
    async def complete_pending_auth(self, pending_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """Complete pending authentication and create session."""
        if pending_id not in self.pending_auth:
            raise AuthenticationError("Pending authentication not found")
        
        # Remove from pending
        del self.pending_auth[pending_id]
        
        # Create session
        return await self.create_session(user_info)
    
    async def store_execution_status(self, execution_id: str, status_data: Dict[str, Any]):
        """Store workflow execution status."""
        async with self._lock:
            self.execution_status[execution_id] = {
                **status_data,
                "updated_at": datetime.utcnow()
            }
    
    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow execution status."""
        return self.execution_status.get(execution_id)
    
    async def get_execution_history(
        self, 
        user_id: str, 
        workflow_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Get execution history for user."""
        # Filter executions for user
        user_executions = []
        
        for execution_id, status_data in self.execution_status.items():
            if status_data.get("user_id") == user_id:
                if workflow_id is None or status_data.get("workflow_id") == workflow_id:
                    user_executions.append({
                        "execution_id": execution_id,
                        **status_data
                    })
        
        # Sort by creation time (newest first)
        user_executions.sort(
            key=lambda x: x.get("started_at", datetime.min), 
            reverse=True
        )
        
        # Paginate
        total_count = len(user_executions)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_executions = user_executions[start_idx:end_idx]
        
        return {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "executions": paginated_executions
        }
    
    async def _cleanup_expired_sessions(self):
        """Background task to cleanup expired sessions."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                async with self._lock:
                    current_time = datetime.utcnow()
                    expired_sessions = []
                    
                    # Find expired sessions
                    for session_token, session_info in self.sessions.items():
                        expires_at = session_info.get("expires_at")
                        if expires_at and current_time > expires_at:
                            expired_sessions.append(session_token)
                    
                    # Remove expired sessions
                    for session_token in expired_sessions:
                        await self.invalidate_session(session_token)
                    
                    if expired_sessions:
                        self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                    
                    # Cleanup old execution status (older than 24 hours)
                    old_executions = []
                    cutoff_time = current_time - timedelta(hours=24)
                    
                    for execution_id, status_data in self.execution_status.items():
                        updated_at = status_data.get("updated_at", datetime.min)
                        if updated_at < cutoff_time:
                            old_executions.append(execution_id)
                    
                    for execution_id in old_executions:
                        del self.execution_status[execution_id]
                    
                    if old_executions:
                        self.logger.info(f"Cleaned up {len(old_executions)} old execution records")
            
            except Exception as e:
                self.logger.error(f"Error in session cleanup task: {e}")
    
    async def cleanup(self):
        """Cleanup session manager resources."""
        try:
            # Cancel cleanup task
            if hasattr(self, '_cleanup_task'):
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Session manager cleanup complete")
            
        except Exception as e:
            self.logger.error(f"Error during session manager cleanup: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get session manager statistics."""
        active_sessions = sum(1 for s in self.sessions.values() if s.get("is_active"))
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "unique_users": len(self.user_sessions),
            "pending_auth": len(self.pending_auth),
            "execution_records": len(self.execution_status)
        }
