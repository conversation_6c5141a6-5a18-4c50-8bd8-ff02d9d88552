"""Workflow handler for RunningHub.cn workflow operations."""

import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
from playwright.async_api import BrowserContext, Page

from ..config.settings import settings
from ..config.selectors import RunningHubSelectors, SELECTOR_TIMEOUTS
from ..config.constants import WorkflowStatus
from ..models.workflow import (
    WorkflowSchema, NodeInfo, WorkflowProgress, WorkflowResult,
    WorkflowExecutionRequest
)
from ..models.exceptions import (
    WorkflowNotFoundError, WorkflowExecutionError, SchemaExtractionError,
    FileDownloadError
)
from ..logging_utils.setup import get_logger, PerformanceLogger
from .page_operations import PageOperations


class WorkflowHandler:
    """Handles workflow operations for RunningHub.cn."""
    
    def __init__(self, browser_context: BrowserContext):
        self.context = browser_context
        self.page: Optional[Page] = None
        self.page_ops: Optional[PageOperations] = None
        self.selectors = RunningHubSelectors()
        self.logger = get_logger(__name__)
    
    async def extract_schema(self, workflow_id: str) -> WorkflowSchema:
        """Extract workflow schema from RunningHub.cn."""
        try:
            with PerformanceLogger(f"extract_schema({workflow_id})"):
                self.logger.info(f"Extracting schema for workflow: {workflow_id}")
                
                # Create new page for schema extraction
                self.page = await self.context.new_page()
                self.page_ops = PageOperations(self.page)
                
                # Navigate to workflow page
                workflow_url = f"{settings.runninghub_base_url}/post/{workflow_id}"
                await self.page_ops.navigate_to(workflow_url)
                
                # Check if workflow exists
                if await self._check_workflow_not_found():
                    raise WorkflowNotFoundError(workflow_id)
                
                # Extract basic workflow information
                workflow_info = await self._extract_workflow_info()
                
                # Click "运行工作流" to open execution interface
                await self.page_ops.click_element(self.selectors.RUN_WORKFLOW_BUTTON)
                
                # Wait for execution interface to load
                await self._wait_for_execution_interface()
                
                # Extract API schema using right-click context menu
                api_schema = await self._extract_api_schema()
                
                # Parse and transform the schema
                schema = await self._transform_schema(workflow_id, workflow_info, api_schema)
                
                self.logger.info(f"Successfully extracted schema for workflow: {workflow_id}")
                return schema
                
        except WorkflowNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Error extracting schema for workflow {workflow_id}: {e}")
            raise SchemaExtractionError(f"Failed to extract workflow schema: {str(e)}")
        
        finally:
            if self.page:
                await self.page.close()
                self.page = None
                self.page_ops = None
    
    async def _check_workflow_not_found(self) -> bool:
        """Check if workflow page shows not found error."""
        try:
            # Look for common "not found" indicators
            not_found_selectors = [
                "text=404",
                "text=Not Found",
                "text=页面不存在",
                "text=工作流不存在"
            ]
            
            for selector in not_found_selectors:
                if await self.page_ops.is_element_visible(selector, timeout=2000):
                    return True
            
            return False
            
        except Exception:
            return False
    
    async def _extract_workflow_info(self) -> Dict[str, Any]:
        """Extract basic workflow information from the page."""
        try:
            workflow_info = {}
            
            # Extract title
            try:
                title = await self.page_ops.get_element_text(self.selectors.WORKFLOW_TITLE)
                workflow_info["title"] = title.strip() if title else None
            except:
                workflow_info["title"] = None
            
            # Extract description
            try:
                description = await self.page_ops.get_element_text(self.selectors.WORKFLOW_DESCRIPTION)
                workflow_info["description"] = description.strip() if description else None
            except:
                workflow_info["description"] = None
            
            # Extract author
            try:
                author = await self.page_ops.get_element_text(self.selectors.WORKFLOW_AUTHOR)
                workflow_info["author"] = author.strip() if author else None
            except:
                workflow_info["author"] = None
            
            # Extract tags
            try:
                tag_elements = await self.page.locator(self.selectors.WORKFLOW_TAGS).all()
                tags = []
                for tag_element in tag_elements:
                    tag_text = await tag_element.text_content()
                    if tag_text:
                        tags.append(tag_text.strip())
                workflow_info["tags"] = tags if tags else None
            except:
                workflow_info["tags"] = None
            
            return workflow_info
            
        except Exception as e:
            self.logger.warning(f"Error extracting workflow info: {e}")
            return {}
    
    async def _wait_for_execution_interface(self):
        """Wait for workflow execution interface to load."""
        try:
            self.logger.debug("Waiting for execution interface to load...")
            
            # Wait for ComfyUI iframe to appear
            await self.page_ops.wait_for_element(
                self.selectors.COMFYUI_IFRAME,
                timeout=SELECTOR_TIMEOUTS["workflow_load"]
            )
            
            # Wait for iframe content to load
            await asyncio.sleep(3)
            
            self.logger.debug("Execution interface loaded successfully")
            
        except Exception as e:
            raise WorkflowExecutionError(f"Failed to load execution interface: {str(e)}")
    
    async def _extract_api_schema(self) -> Dict[str, Any]:
        """Extract API schema using right-click context menu."""
        try:
            self.logger.debug("Extracting API schema...")
            
            # Get iframe and switch to its context
            iframe_element = await self.page_ops.wait_for_element(self.selectors.COMFYUI_IFRAME)
            iframe = await iframe_element.content_frame()
            
            if not iframe:
                raise SchemaExtractionError("Could not access ComfyUI iframe")
            
            # Create page operations for iframe
            iframe_ops = PageOperations(iframe)
            
            # Right-click in the main workflow area
            await iframe_ops.right_click_element(self.selectors.COMFYUI_MAIN)
            
            # Wait for context menu to appear
            await asyncio.sleep(1)
            
            # Click "导出工作流API" option
            await iframe_ops.click_element(self.selectors.EXPORT_WORKFLOW_API_MENU)
            
            # Wait for download to complete
            downloaded_file = await self.page_ops.wait_for_download()
            
            if not downloaded_file:
                raise FileDownloadError("Failed to download workflow API schema")
            
            # Read and parse the downloaded JSON
            with open(downloaded_file, 'r', encoding='utf-8') as f:
                api_schema = json.load(f)
            
            self.logger.debug("API schema extracted successfully")
            return api_schema
            
        except Exception as e:
            raise SchemaExtractionError(f"Failed to extract API schema: {str(e)}")
    
    async def _transform_schema(
        self, 
        workflow_id: str, 
        workflow_info: Dict[str, Any], 
        api_schema: Dict[str, Any]
    ) -> WorkflowSchema:
        """Transform raw API schema to WorkflowSchema format."""
        try:
            self.logger.debug("Transforming schema to required format...")
            
            # Extract node information from API schema
            node_info_list = []
            
            # The API schema structure may vary, but typically contains node definitions
            # This is based on ComfyUI API format
            if "workflow" in api_schema:
                workflow_data = api_schema["workflow"]
                nodes = workflow_data.get("nodes", [])
                
                for node in nodes:
                    node_id = str(node.get("id", ""))
                    node_type = node.get("type", "")
                    
                    # Extract widgets/inputs as fields
                    widgets = node.get("widgets_values", [])
                    inputs = node.get("inputs", [])
                    
                    # Create node info for each configurable parameter
                    if widgets:
                        for i, widget_value in enumerate(widgets):
                            node_info = NodeInfo(
                                node_id=f"{node_id}_{i}",
                                field_name=f"widget_{i}",
                                field_value="[value placeholder]",
                                class_type=node_type,
                                meta={
                                    "title": node.get("title", node_type),
                                    "widget_index": i,
                                    "original_value": widget_value
                                }
                            )
                            node_info_list.append(node_info)
            
            # If no nodes found, try alternative schema structure
            if not node_info_list and isinstance(api_schema, dict):
                for key, value in api_schema.items():
                    if isinstance(value, dict) and "class_type" in value:
                        inputs = value.get("inputs", {})
                        class_type = value["class_type"]
                        
                        for input_name, input_value in inputs.items():
                            node_info = NodeInfo(
                                node_id=key,
                                field_name=input_name,
                                field_value="[value placeholder]",
                                class_type=class_type,
                                meta={
                                    "title": f"{class_type}_{input_name}",
                                    "original_value": input_value
                                }
                            )
                            node_info_list.append(node_info)
            
            # Create workflow schema
            schema = WorkflowSchema(
                workflow_id=workflow_id,
                title=workflow_info.get("title"),
                description=workflow_info.get("description"),
                author=workflow_info.get("author"),
                tags=workflow_info.get("tags"),
                node_info_list=node_info_list,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.logger.debug(f"Schema transformed successfully with {len(node_info_list)} nodes")
            return schema

        except Exception as e:
            raise SchemaExtractionError(f"Failed to transform schema: {str(e)}")

    async def execute_workflow(
        self,
        workflow_id: str,
        execution_id: str,
        node_info_list: List[NodeInfo],
        execution_options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[WorkflowProgress, None]:
        """Execute workflow with streaming progress updates."""
        try:
            with PerformanceLogger(f"execute_workflow({workflow_id})"):
                self.logger.info(f"Starting workflow execution: {execution_id}")

                # Create new page for execution
                self.page = await self.context.new_page()
                self.page_ops = PageOperations(self.page)

                # Navigate to workflow execution page
                execution_url = f"{settings.runninghub_base_url}/workflow/{execution_id}"

                # If execution URL doesn't exist, start from workflow page
                try:
                    await self.page_ops.navigate_to(execution_url)
                except:
                    # Navigate to workflow page and click run
                    workflow_url = f"{settings.runninghub_base_url}/post/{workflow_id}"
                    await self.page_ops.navigate_to(workflow_url)
                    await self.page_ops.click_element(self.selectors.RUN_WORKFLOW_BUTTON)

                # Wait for execution interface
                await self._wait_for_execution_interface()

                # Fill workflow inputs
                await self._fill_workflow_inputs(node_info_list)

                # Start execution
                await self._start_workflow_execution()

                # Stream progress updates
                async for progress in self._monitor_execution_progress(execution_id, workflow_id):
                    yield progress

                    # Break if execution is complete
                    if progress.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]:
                        break

        except Exception as e:
            self.logger.error(f"Error executing workflow {workflow_id}: {e}")

            # Yield error progress
            error_progress = WorkflowProgress(
                execution_id=execution_id,
                workflow_id=workflow_id,
                status=WorkflowStatus.FAILED,
                progress_percent=0.0,
                message=f"Execution failed: {str(e)}",
                started_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            yield error_progress

        finally:
            if self.page:
                await self.page.close()
                self.page = None
                self.page_ops = None

    async def _fill_workflow_inputs(self, node_info_list: List[NodeInfo]):
        """Fill workflow input fields based on node info list."""
        try:
            self.logger.debug("Filling workflow inputs...")

            # Get iframe context
            iframe_element = await self.page_ops.wait_for_element(self.selectors.COMFYUI_IFRAME)
            iframe = await iframe_element.content_frame()

            if not iframe:
                raise WorkflowExecutionError("Could not access ComfyUI iframe for input filling")

            iframe_ops = PageOperations(iframe)

            # Fill each node input
            for node_info in node_info_list:
                try:
                    await self._fill_node_input(iframe_ops, node_info)
                except Exception as e:
                    self.logger.warning(f"Failed to fill input for node {node_info.node_id}: {e}")
                    # Continue with other inputs

            self.logger.debug("Workflow inputs filled successfully")

        except Exception as e:
            raise WorkflowExecutionError(f"Failed to fill workflow inputs: {str(e)}")

    async def _fill_node_input(self, iframe_ops: PageOperations, node_info: NodeInfo):
        """Fill individual node input."""
        try:
            # This is a simplified approach - actual implementation would need
            # to understand the specific UI structure of ComfyUI nodes

            # Look for input elements related to this node
            node_selector = f"[data-node-id='{node_info.node_id}']"

            # Try different input types
            input_selectors = [
                f"{node_selector} input[type='text']",
                f"{node_selector} input[type='number']",
                f"{node_selector} textarea",
                f"{node_selector} select"
            ]

            for selector in input_selectors:
                if await iframe_ops.is_element_visible(selector, timeout=1000):
                    if isinstance(node_info.field_value, (str, int, float)):
                        await iframe_ops.type_text(selector, str(node_info.field_value))
                        self.logger.debug(f"Filled input {node_info.field_name} with value: {node_info.field_value}")
                        break

        except Exception as e:
            self.logger.warning(f"Error filling node input {node_info.node_id}: {e}")

    async def _start_workflow_execution(self):
        """Start workflow execution by clicking run button."""
        try:
            self.logger.debug("Starting workflow execution...")

            # Click the run button
            await self.page_ops.click_element(self.selectors.RUN_BUTTON)

            # Wait for execution to start
            await asyncio.sleep(2)

            self.logger.debug("Workflow execution started")

        except Exception as e:
            raise WorkflowExecutionError(f"Failed to start workflow execution: {str(e)}")

    async def _monitor_execution_progress(
        self,
        execution_id: str,
        workflow_id: str
    ) -> AsyncGenerator[WorkflowProgress, None]:
        """Monitor workflow execution progress and yield updates."""
        try:
            self.logger.debug("Monitoring execution progress...")

            start_time = datetime.utcnow()
            last_progress = 0.0

            # Initial progress
            yield WorkflowProgress(
                execution_id=execution_id,
                workflow_id=workflow_id,
                status=WorkflowStatus.RUNNING,
                progress_percent=0.0,
                message="Workflow execution started",
                started_at=start_time,
                updated_at=datetime.utcnow()
            )

            # Monitor progress
            while True:
                try:
                    # Check for completion indicators
                    if await self._check_execution_complete():
                        # Final progress
                        yield WorkflowProgress(
                            execution_id=execution_id,
                            workflow_id=workflow_id,
                            status=WorkflowStatus.COMPLETED,
                            progress_percent=100.0,
                            message="Workflow execution completed",
                            started_at=start_time,
                            updated_at=datetime.utcnow()
                        )
                        break

                    # Check for errors
                    error_message = await self._check_execution_error()
                    if error_message:
                        yield WorkflowProgress(
                            execution_id=execution_id,
                            workflow_id=workflow_id,
                            status=WorkflowStatus.FAILED,
                            progress_percent=last_progress,
                            message=f"Execution failed: {error_message}",
                            started_at=start_time,
                            updated_at=datetime.utcnow()
                        )
                        break

                    # Update progress
                    current_progress = await self._get_current_progress()
                    if current_progress > last_progress:
                        last_progress = current_progress

                        yield WorkflowProgress(
                            execution_id=execution_id,
                            workflow_id=workflow_id,
                            status=WorkflowStatus.RUNNING,
                            progress_percent=current_progress,
                            message="Workflow execution in progress",
                            started_at=start_time,
                            updated_at=datetime.utcnow()
                        )

                    # Wait before next check
                    await asyncio.sleep(2)

                    # Check timeout
                    if (datetime.utcnow() - start_time).total_seconds() > settings.workflow_execution_timeout / 1000:
                        yield WorkflowProgress(
                            execution_id=execution_id,
                            workflow_id=workflow_id,
                            status=WorkflowStatus.FAILED,
                            progress_percent=last_progress,
                            message="Execution timeout",
                            started_at=start_time,
                            updated_at=datetime.utcnow()
                        )
                        break

                except Exception as e:
                    self.logger.error(f"Error monitoring progress: {e}")
                    yield WorkflowProgress(
                        execution_id=execution_id,
                        workflow_id=workflow_id,
                        status=WorkflowStatus.FAILED,
                        progress_percent=last_progress,
                        message=f"Monitoring error: {str(e)}",
                        started_at=start_time,
                        updated_at=datetime.utcnow()
                    )
                    break

        except Exception as e:
            self.logger.error(f"Error in progress monitoring: {e}")

    async def _check_execution_complete(self) -> bool:
        """Check if workflow execution is complete."""
        try:
            # Look for completion indicators in task list
            completion_indicators = [
                "text=完成",
                "text=已完成",
                "text=成功",
                ".task-status:has-text('完成')",
                ".progress-bar[aria-valuenow='100']"
            ]

            for indicator in completion_indicators:
                if await self.page_ops.is_element_visible(indicator, timeout=1000):
                    return True

            return False

        except Exception:
            return False

    async def _check_execution_error(self) -> Optional[str]:
        """Check for execution errors and return error message."""
        try:
            error_selectors = [
                ".error-message",
                ".task-status:has-text('失败')",
                ".task-status:has-text('错误')",
                "text=执行失败"
            ]

            for selector in error_selectors:
                if await self.page_ops.is_element_visible(selector, timeout=1000):
                    try:
                        error_text = await self.page_ops.get_element_text(selector)
                        return error_text.strip() if error_text else "Unknown error"
                    except:
                        return "Execution error detected"

            return None

        except Exception:
            return None

    async def _get_current_progress(self) -> float:
        """Get current execution progress percentage."""
        try:
            # Look for progress indicators
            progress_selectors = [
                ".progress-bar",
                ".ant-progress-text",
                "[role='progressbar']"
            ]

            for selector in progress_selectors:
                if await self.page_ops.is_element_visible(selector, timeout=1000):
                    try:
                        # Try to get progress from aria-valuenow attribute
                        progress_value = await self.page_ops.get_element_attribute(selector, "aria-valuenow")
                        if progress_value:
                            return float(progress_value)

                        # Try to extract from text content
                        progress_text = await self.page_ops.get_element_text(selector)
                        if progress_text and "%" in progress_text:
                            import re
                            match = re.search(r'(\d+(?:\.\d+)?)%', progress_text)
                            if match:
                                return float(match.group(1))
                    except:
                        continue

            # Default progress estimation based on time
            return min(50.0, 10.0)  # Conservative progress estimate

        except Exception:
            return 0.0
