"""Authentication handler for RunningHub.cn login flow.

This module provides authentication functionality for RunningHub.cn using HTTP request monitoring
for reliable detection of authentication status. Instead of relying on UI element visibility,
it monitors the getUserInfo API endpoint (/uc/getUserInfo) to detect successful authentication.

Key features:
- HTTP request monitoring for authentication detection
- Support for multiple authentication methods (WeChat, SMS, Password)
- Robust error handling and timeout management
- User information extraction from API responses
- Fallback UI-based detection for error scenarios

Network Monitoring Approach:
- Listens for responses to https://www.runninghub.cn/uc/getUserInfo
- Parses JSON response to extract user data
- Handles various HTTP status codes and API errors
- Provides detailed error messages for failed authentication attempts
"""

import asyncio
from typing import Dict, Any, Optional
from playwright.async_api import BrowserContext, Page

from ..config.settings import settings
from ..config.selectors import (
    RunningHubSelectors, 
    SELECTOR_TIMEOUTS, 
    CAPTCHA_PATTERNS,
    LOGIN_SUCCESS_INDICATORS,
    ERROR_MESSAGE_PATTERNS
)
from ..config.constants import AuthStatus
from ..models.auth import AuthRequest, AuthResponse, AuthMethod
from ..models.exceptions import AuthenticationError, PageLoadError
from ..logging_utils.setup import get_logger, PerformanceLogger
from .page_operations import PageOperations


class AuthHandler:
    """Handles authentication flow for RunningHub.cn."""
    
    def __init__(self, browser_context: BrowserContext):
        self.max_login_retries = 3
        self.context = browser_context
        self.page: Optional[Page] = None
        self.page_ops: Optional[PageOperations] = None
        self.selectors = RunningHubSelectors()
        self.logger = get_logger(__name__)
        
        # Network monitoring attributes
        self.user_info_response: Optional[Dict[str, Any]] = None
        self.auth_request_detected: bool = False
        self.network_error: Optional[str] = None
    
    async def authenticate(self, auth_request: AuthRequest) -> AuthResponse:
        """Main authentication method that handles different auth types."""
        try:
            with PerformanceLogger(f"authenticate({auth_request.method})"):
                self.logger.info(f"Starting authentication with method: {auth_request.method}")
                
                # Create new page for authentication
                self.page = await self.context.new_page()
                self.page_ops = PageOperations(self.page)
                
                # Set up network monitoring for authentication detection
                await self._setup_network_monitoring()
                
                # Navigate to RunningHub.cn
                await self.page_ops.navigate_to(settings.runninghub_base_url)
                
                # Click login button to open modal
                await self._open_login_modal()
                
                # Handle different authentication methods
                if auth_request.method == AuthMethod.WECHAT:
                    return await self._handle_wechat_login(auth_request)
                elif auth_request.method == AuthMethod.PHONE_SMS:
                    return await self._handle_phone_sms_login(auth_request)
                elif auth_request.method == AuthMethod.PASSWORD:
                    return await self._handle_password_login(auth_request)
                else:
                    raise AuthenticationError(f"Unsupported authentication method: {auth_request.method}")
        
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError(f"Authentication process failed: {str(e)}")
        
        finally:
            # Keep page open for session persistence
            pass
    
    async def _open_login_modal(self):
        """Open the login modal by clicking the login button."""
        try:
            self.logger.debug("Opening login modal...")

            # Wait for page to load
            await self.page_ops.wait_for_page_load()

            # Click login button
            self.logger.debug("Clicking login button...")
            await self.page_ops.click_element(self.selectors.LOGIN_BUTTON)

            # Give modal time to animate in
            self.logger.debug("Waiting for modal animation...")
            # await asyncio.sleep(1.5)  # Allow time for modal animation

            # Wait for modal to appear with extended timeout
            self.logger.debug("Waiting for modal to appear...")
            await self.page_ops.wait_for_element(
                self.selectors.LOGIN_MODAL,
                timeout=SELECTOR_TIMEOUTS["login"]  # Use login timeout (60 seconds)
            )

            self.logger.debug("Login modal opened successfully")

        except Exception as e:
            # Add more detailed error information
            current_url = self.page.url if self.page else "unknown"
            self.logger.error(f"Failed to open login modal at URL: {current_url}")
            self.logger.error(f"Modal selector: {self.selectors.LOGIN_MODAL}")
            raise AuthenticationError(f"Failed to open login modal: {str(e)}")
    
    async def _handle_wechat_login(self, auth_request: AuthRequest) -> AuthResponse:
        """Handle WeChat QR code login."""
        try:
            self.logger.info("Handling WeChat login...")
            
            # Switch to WeChat login tab if needed
            if await self.page_ops.is_element_visible(self.selectors.WECHAT_LOGIN_TAB):
                await self.page_ops.click_element(self.selectors.WECHAT_LOGIN_TAB)
            
            # Wait for QR code to appear
            await asyncio.sleep(2)  # Give time for QR code to load
            
            # Take screenshot to capture QR code
            qr_screenshot = await self.page_ops.take_screenshot()
            
            # Save QR code screenshot
            import os
            import time
            qr_filename = f"wechat_qr_{int(time.time())}.png"
            qr_path = os.path.join(settings.temp_dir, qr_filename)
            
            os.makedirs(settings.temp_dir, exist_ok=True)
            with open(qr_path, "wb") as f:
                f.write(qr_screenshot)
            
            self.logger.info(f"QR code saved to: {qr_path}")
            
            # Return response indicating manual action required
            return AuthResponse(
                status=AuthStatus.AUTHENTICATING,
                message="Please scan the WeChat QR code to complete authentication",
                requires_manual_action=True,
                manual_action_type="qr_scan",
                qr_code_url=f"/temp/{qr_filename}"
            )
            
        except Exception as e:
            raise AuthenticationError(f"WeChat login failed: {str(e)}")
    
    async def _handle_phone_sms_login(self, auth_request: AuthRequest, retries: int = 0) -> AuthResponse:
        """Handle phone SMS login with user interaction support."""
        try:
            self.logger.info("Handling phone SMS login with user interaction...")
            
            # Switch to phone login tab if needed
            if await self.page_ops.is_element_visible(self.selectors.PHONE_LOGIN_TAB):
                await self.page_ops.click_element(self.selectors.PHONE_LOGIN_TAB)
                self.logger.info("Switched to phone SMS login tab")
            
            # Wait for the form to be ready
            await self.page_ops.wait_for_element(self.selectors.PHONE_INPUT)
            
            # If phone number is provided in request, pre-fill it
            if auth_request.phone:
                await self.page_ops.type_text(self.selectors.PHONE_INPUT, auth_request.phone)
                self.logger.info(f"Pre-filled phone number: {auth_request.phone}")

            if await self.page_ops.is_element_visible(self.selectors.SEND_SMS_BUTTON):
                await self.page_ops.click_element(self.selectors.SEND_SMS_BUTTON)
            
            # Return response indicating user should complete the form manually
            self.logger.info("Login modal is ready - yielding to user for manual completion")
            
            # Start the user interaction loop
            self.logger.info(f"Attempt {retries + 1}/{self.max_login_retries}: Waiting for user to complete login...")
            
            # Wait 90 seconds for user to interact with the login modal
            auth_result = await self._wait_for_user_login_completion(timeout_seconds=90)
            
            if auth_result.status != AuthStatus.AUTHENTICATED:
                raise AuthenticationError(
                    f"User did not complete login in time or login failed cuz {auth_result.message}",
                )
            
            # All attempts failed
            return auth_result
                
        except Exception as e:
            self.logger.error(f"Phone SMS login failed: {str(e)}")
            if retries >= self.max_login_retries:
                raise AuthenticationError(f"Authentication failed after {self.max_login_retries} attempts. Please try again.")
            return self._handle_phone_sms_login(auth_request, retries + 1)

    async def _wait_for_user_login_completion(self, timeout_seconds: int = 90) -> AuthResponse:
        """Wait for user to complete login manually using HTTP request monitoring."""
        try:
            self.logger.info(f"Waiting {timeout_seconds} seconds for user to complete login (monitoring HTTP requests)...")
            
            start_time = asyncio.get_event_loop().time()
            check_interval = 2  # Check every 2 seconds
            last_log_time = start_time
            
            while (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
                current_time = asyncio.get_event_loop().time()
                
                # Check if we have a successful getUserInfo response
                if self.user_info_response:
                    self.logger.info("Authentication success detected via getUserInfo API response")
                    return AuthResponse(
                        status=AuthStatus.AUTHENTICATED,
                        message="Authentication successful",
                        user_info=self.user_info_response
                    )
                
                # Check if there was a network error with the getUserInfo request
                if self.network_error:
                    self.logger.error(f"Authentication failed due to network error: {self.network_error}")
                    return AuthResponse(
                        status=AuthStatus.FAILED,
                        message=f"Login failed: {self.network_error}"
                    )
                
                # Check for CAPTCHA challenges (keep existing CAPTCHA detection as fallback)
                captcha_detected = False
                try:
                    for captcha_pattern in CAPTCHA_PATTERNS:
                        if await self.page_ops.is_element_visible(captcha_pattern, timeout=500):
                            self.logger.info(f"CAPTCHA challenge detected: {captcha_pattern}")
                            captcha_detected = True
                            break
                except Exception as e:
                    self.logger.debug(f"Error checking CAPTCHA patterns: {e}")
                
                # Check for obvious error messages in the modal as backup
                try:
                    for error_pattern in ERROR_MESSAGE_PATTERNS:
                        if await self.page_ops.is_element_visible(error_pattern, timeout=500):
                            try:
                                error_text = await self.page_ops.get_element_text(error_pattern)
                                self.logger.error(f"Login error detected in UI: {error_text}")
                                return AuthResponse(
                                    status=AuthStatus.FAILED,
                                    message=f"Login failed: {error_text}"
                                )
                            except Exception as e:
                                self.logger.warning(f"Could not extract error text: {e}")
                                return AuthResponse(
                                    status=AuthStatus.FAILED,
                                    message="Login error detected but could not extract message"
                                )
                except Exception as e:
                    self.logger.debug(f"Error checking error patterns: {e}")
                
                # Log progress every 15 seconds
                if current_time - last_log_time >= 15:
                    elapsed = current_time - start_time
                    remaining = timeout_seconds - elapsed
                    
                    # Status message based on what we've detected
                    if self.auth_request_detected:
                        status_msg = "getUserInfo request detected, waiting for response"
                    elif captcha_detected:
                        status_msg = "CAPTCHA challenge active"
                    else:
                        status_msg = "waiting for user input"
                    
                    self.logger.info(f"Still waiting for authentication ({status_msg})... {remaining:.0f} seconds remaining")
                    last_log_time = current_time
                
                await asyncio.sleep(check_interval)
            
            # Timeout reached
            self.logger.warning(f"User login timeout after {timeout_seconds} seconds")
            
            # Handle network timeout scenarios
            await self._handle_network_timeout(timeout_seconds)
            
            # Determine appropriate timeout message based on what we detected
            if self.user_info_response:
                # This shouldn't happen but handle just in case
                return AuthResponse(
                    status=AuthStatus.AUTHENTICATED,
                    message="Authentication successful",
                    user_info=self.user_info_response
                )
            elif self.network_error:
                return AuthResponse(
                    status=AuthStatus.FAILED,
                    message=f"Login failed: {self.network_error}"
                )
            elif self.auth_request_detected:
                timeout_msg = "Login attempt detected but authentication was not completed"
            else:
                timeout_msg = "No login attempt detected within timeout period"
                
            return AuthResponse(
                status=AuthStatus.TIMEOUT,
                message=f"Login timeout after {timeout_seconds} seconds - {timeout_msg}",
                requires_manual_action=True,
                manual_action_type="timeout_retry"
            )
            
        except Exception as e:
            self.logger.error(f"Error waiting for user login completion: {str(e)}")
            return AuthResponse(
                status=AuthStatus.FAILED,
                message=f"Error during login wait: {str(e)}"
            )
    
    async def _verify_login_success(self) -> bool:
        """Verify that login was actually successful by checking page indicators."""
        try:
            self.logger.debug("Verifying login success...")
            
            # Wait for page to load after login
            await self.page_ops.wait_for_page_load()
            
            # Check URL changes first (fastest indicator)
            current_url = self.page.url
            for url_pattern in ["/dashboard", "/workspace", "/profile", "/user", "/home"]:
                if url_pattern in current_url:
                    self.logger.debug(f"Login success verified by URL: {current_url}")
                    return True
            
            # Check for logged-in indicators with timeout
            for indicator in LOGIN_SUCCESS_INDICATORS:
                try:
                    if indicator.startswith('/'):
                        # URL check already done above
                        continue
                    else:
                        # Element check with short timeout
                        if await self.page_ops.is_element_visible(indicator, timeout=3000):
                            self.logger.debug(f"Login success verified by element: {indicator}")
                            return True
                except Exception as e:
                    self.logger.debug(f"Could not check indicator {indicator}: {e}")
                    continue
            
            # Additional check: see if login button is no longer present
            login_button_present = await self.page_ops.is_element_visible(
                self.selectors.LOGIN_BUTTON, 
                timeout=2000
            )
            if not login_button_present:
                self.logger.debug("Login success verified by absence of login button")
                return True
            
            # Check page title for logged-in indicators
            try:
                page_title = await self.page.title()
                logged_in_title_indicators = ["工作台", "dashboard", "workspace", "个人中心"]
                for indicator in logged_in_title_indicators:
                    if indicator.lower() in page_title.lower():
                        self.logger.debug(f"Login success verified by page title: {page_title}")
                        return True
            except Exception as e:
                self.logger.debug(f"Could not check page title: {e}")
            
            # Check for user-specific content in page
            try:
                page_content = await self.page.content()
                if any(indicator in page_content for indicator in ["个人中心", "我的工作流", "退出登录"]):
                    self.logger.debug("Login success verified by page content")
                    return True
            except Exception as e:
                self.logger.debug(f"Could not check page content: {e}")
            
            self.logger.debug("No login success indicators found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error verifying login success: {e}")
            return False
    
    async def _reset_login_form(self):
        """Reset the login form for retry attempts."""
        try:
            self.logger.info("Resetting login form for retry...")
            
            # Check if login modal is still open
            modal_visible = await self.page_ops.is_element_visible(
                self.selectors.LOGIN_MODAL, 
                timeout=SELECTOR_TIMEOUTS["quick_check"]
            )
            
            if not modal_visible:
                # Modal is closed, need to reopen it
                self.logger.info("Login modal closed, reopening...")
                await self._open_login_modal()
                # Wait for modal to be ready
                await asyncio.sleep(2)
            
            # Clear any error messages first
            try:
                for error_pattern in ERROR_MESSAGE_PATTERNS:
                    if await self.page_ops.is_element_visible(error_pattern, timeout=500):
                        # Try to dismiss error by clicking elsewhere or pressing escape
                        await self.page.keyboard.press("Escape")
                        await asyncio.sleep(1)
                        break
            except Exception as e:
                self.logger.debug(f"Could not clear error messages: {e}")
            
            # Switch to phone login tab if needed
            try:
                if await self.page_ops.is_element_visible(self.selectors.PHONE_LOGIN_TAB, timeout=2000):
                    await self.page_ops.click_element(self.selectors.PHONE_LOGIN_TAB)
                    await asyncio.sleep(1)  # Wait for tab switch
            except Exception as e:
                self.logger.debug(f"Could not switch to phone login tab: {e}")
            
            # Clear form fields with multiple attempts
            form_fields = [
                (self.selectors.PHONE_INPUT, "phone input"),
                (self.selectors.SMS_CODE_INPUT, "SMS code input"),
                (self.selectors.PASSWORD_INPUT, "password input")
            ]
            
            for selector, field_name in form_fields:
                try:
                    if await self.page_ops.is_element_visible(selector, timeout=2000):
                        # Try multiple clearing methods
                        locator = self.page.locator(selector)
                        
                        # Method 1: Select all and delete
                        await locator.click()
                        await self.page.keyboard.press("Control+a")
                        await self.page.keyboard.press("Delete")
                        
                        # Method 2: Clear using Playwright
                        await locator.clear()
                        
                        # Method 3: Fill with empty string
                        await locator.fill("")
                        
                        self.logger.debug(f"Cleared {field_name}")
                        
                except Exception as e:
                    self.logger.debug(f"Could not clear {field_name}: {e}")
                    continue
            
            # Refresh the page if form reset fails
            try:
                # Check if any field still has content
                fields_cleared = True
                for selector, _ in form_fields:
                    try:
                        if await self.page_ops.is_element_visible(selector, timeout=1000):
                            value = await self.page_ops.get_element_attribute(selector, "value")
                            if value and value.strip():
                                fields_cleared = False
                                break
                    except:
                        continue
                
                if not fields_cleared:
                    self.logger.warning("Form fields not cleared, refreshing page...")
                    await self.page.reload(wait_until="domcontentloaded")
                    await self._open_login_modal()
                    
            except Exception as e:
                self.logger.debug(f"Could not verify field clearing: {e}")
            
            self.logger.info("Login form reset completed")
            
        except Exception as e:
            self.logger.error(f"Error resetting login form: {e}")
            # Continue anyway, as this is not critical for the retry attempt
            # Continue anyway, as this is not critical
    
    async def _handle_password_login(self, auth_request: AuthRequest) -> AuthResponse:
        """Handle username/password login."""
        try:
            self.logger.info("Handling password login...")
            
            if not auth_request.username or not auth_request.password:
                raise AuthenticationError("Username and password are required for password login")
            
            # Enter username (might be phone number or email)
            await self.page_ops.type_text(self.selectors.PHONE_INPUT, auth_request.username)
            
            # Enter password
            await self.page_ops.type_text(self.selectors.PASSWORD_INPUT, auth_request.password)
            
            # Click login button
            await self.page_ops.click_element(self.selectors.LOGIN_SUBMIT)
            
            # Wait for authentication result
            return await self._wait_for_auth_completion()
            
        except Exception as e:
            raise AuthenticationError(f"Password login failed: {str(e)}")
    
    async def _wait_for_auth_completion(self, timeout: Optional[int] = None) -> AuthResponse:
        """Wait for authentication to complete and return result."""
        timeout = timeout or settings.runninghub_login_timeout
        
        try:
            self.logger.debug("Waiting for authentication completion...")
            
            # Wait for either success or error indicators
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) * 1000 < timeout:
                # Check if login modal is closed (success indicator)
                if not await self.page_ops.is_element_visible(self.selectors.LOGIN_MODAL, timeout=1000):
                    self.logger.info("Login modal closed - authentication successful")
                    
                    # Extract user information
                    user_info = await self._extract_user_info()
                    
                    return AuthResponse(
                        status=AuthStatus.AUTHENTICATED,
                        message="Authentication successful",
                        user_info=user_info
                    )
                
                # Check for error messages
                if await self.page_ops.is_element_visible(self.selectors.ERROR_MESSAGE, timeout=1000):
                    error_text = await self.page_ops.get_element_text(self.selectors.ERROR_MESSAGE)
                    raise AuthenticationError(f"Login failed: {error_text}")
                
                # Wait a bit before checking again
                await asyncio.sleep(1)
            
            # Timeout reached
            return AuthResponse(
                status=AuthStatus.AUTHENTICATING,
                message="Authentication timeout - please complete manually",
                requires_manual_action=True,
                manual_action_type="manual_completion"
            )
            
        except AuthenticationError:
            raise
        except Exception as e:
            raise AuthenticationError(f"Error waiting for authentication completion: {str(e)}")
    
    async def _extract_user_info(self) -> Dict[str, Any]:
        """Extract user information from HTTP response or page elements."""
        try:
            self.logger.debug("Extracting user information...")
            
            # If we have user info from HTTP response, use that (preferred method)
            if self.user_info_response:
                self.logger.debug("Using user info from getUserInfo API response")
                
                # Extract relevant fields from the API response
                user_info = {
                    "user_id": self.user_info_response.get("id"),
                    "mobile": self.user_info_response.get("mobile"),
                    "nickname": self.user_info_response.get("nickName"),
                    "head_icon": self.user_info_response.get("headIcon"),
                    "api_key": self.user_info_response.get("apiKey"),
                    "member_info": self.user_info_response.get("memberInfo", {}),
                    "virtual_coin": self.user_info_response.get("virtualCoin"),
                    "total_coin": self.user_info_response.get("totalCoin"),
                    "country": self.user_info_response.get("country"),
                    "region": self.user_info_response.get("region"),
                    "invite_code": self.user_info_response.get("inviteCode"),
                    "login_time": asyncio.get_event_loop().time(),
                    "source": "http_api"
                }
                
                self.logger.info(f"User info extracted: ID={user_info['user_id']}, Mobile={user_info['mobile']}")
                return user_info
            
            # Fallback: try to extract from page elements (legacy method)
            self.logger.debug("Falling back to page element extraction")
            await self.page_ops.wait_for_page_load()
            
            user_info = {
                "user_id": None,
                "username": None,
                "email": None,
                "avatar_url": None,
                "login_time": asyncio.get_event_loop().time(),
                "source": "page_elements"
            }
            
            # Try to extract user information from page elements
            try:
                # This would need to be customized based on RunningHub.cn's actual DOM structure
                # Look for user profile elements, avatar, etc.
                pass
                
            except Exception as e:
                self.logger.debug(f"Could not extract user info from page elements: {e}")
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"Error extracting user info: {e}")
            return {
                "user_id": None,
                "login_time": asyncio.get_event_loop().time(),
                "source": "error",
                "error": str(e)
            }
    
    async def check_auth_status(self) -> bool:
        """Check if user is currently authenticated."""
        try:
            if not self.page:
                return False
            
            # Navigate to a page that requires authentication
            await self.page_ops.navigate_to(settings.runninghub_base_url)
            
            # Check if login button is present (indicates not logged in)
            if await self.page_ops.is_element_visible(self.selectors.LOGIN_BUTTON, timeout=5000):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking auth status: {e}")
            return False
    
    async def logout(self):
        """Logout from RunningHub.cn."""
        try:
            if not self.page:
                return
            
            self.logger.info("Logging out...")
            
            # Clear cookies and local storage
            await self.context.clear_cookies()
            await self.page.evaluate("localStorage.clear(); sessionStorage.clear();")
            
            self.logger.info("Logout completed")
            
        except Exception as e:
            self.logger.error(f"Error during logout: {e}")
    
    async def cleanup(self):
        """Cleanup authentication handler resources."""
        try:
            # Clean up network monitoring
            self._cleanup_network_monitoring()
            
            if self.page:
                await self.page.close()
                self.page = None
                self.page_ops = None
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            self.logger.error(f"Error during auth handler cleanup: {e}")
    
    async def _setup_network_monitoring(self):
        """Set up network request monitoring for authentication endpoints."""
        if not self.page:
            raise AuthenticationError("Page not initialized")
            
        self.logger.debug("Setting up network request monitoring...")
        
        # Reset monitoring state
        self.user_info_response = None
        self.auth_request_detected = False
        self.network_error = None
        
        async def handle_response(response):
            """Handle network responses and filter for getUserInfo endpoint."""
            try:
                url = response.url
                if "/uc/getUserInfo" in url:
                    self.logger.debug(f"Detected getUserInfo request: {url}")
                    self.auth_request_detected = True
                    
                    if response.status == 200:
                        try:
                            response_data = await response.json()
                            self.logger.debug(f"getUserInfo response: {response_data}")
                            
                            # Check if response indicates successful authentication
                            if response_data.get("code") == 0 and response_data.get("data"):
                                self.user_info_response = response_data["data"]
                                self.logger.info("Authentication success detected via HTTP response")
                            else:
                                # Handle API-level errors
                                error_code = response_data.get("code", "unknown")
                                error_msg = response_data.get("msg", "Unknown error")
                                self.network_error = f"API error (code {error_code}): {error_msg}"
                                self.logger.warning(f"getUserInfo API error: {self.network_error}")
                                
                        except Exception as json_error:
                            # Handle JSON parsing errors
                            self.network_error = f"Failed to parse getUserInfo response: {str(json_error)}"
                            self.logger.error(f"{self.network_error}. Raw response: {await response.text()}")
                            
                    elif response.status == 401:
                        self.network_error = "Authentication required - user not logged in"
                        self.logger.warning(self.network_error)
                    elif response.status == 403:
                        self.network_error = "Access forbidden - insufficient permissions"
                        self.logger.warning(self.network_error)
                    elif response.status >= 500:
                        self.network_error = f"Server error (HTTP {response.status}): {response.status_text}"
                        self.logger.error(self.network_error)
                    else:
                        self.network_error = f"HTTP {response.status}: {response.status_text}"
                        self.logger.warning(f"getUserInfo request failed: {self.network_error}")
                        
            except Exception as e:
                error_msg = f"Error handling network response: {e}"
                self.logger.error(error_msg)
                self.network_error = error_msg
        
        async def handle_request_failed(request):
            """Handle failed network requests."""
            try:
                url = request.url
                if "/uc/getUserInfo" in url:
                    self.logger.error(f"getUserInfo request failed: {url}")
                    self.auth_request_detected = True
                    self.network_error = f"Network request failed: {request.failure}"
            except Exception as e:
                self.logger.error(f"Error handling failed request: {e}")
        
        # Set up event listeners
        self.page.on("response", handle_response)
        self.page.on("requestfailed", handle_request_failed)
        
        self.logger.debug("Network monitoring setup complete")
    
    def _cleanup_network_monitoring(self):
        """Clean up network event listeners."""
        try:
            if self.page:
                # Remove event listeners to prevent memory leaks
                self.page.remove_all_listeners("response")
                self.page.remove_all_listeners("requestfailed")
                self.logger.debug("Network monitoring cleanup complete")
        except Exception as e:
            self.logger.debug(f"Error cleaning up network monitoring: {e}")
    
    async def _handle_network_timeout(self, timeout_seconds: int) -> bool:
        """Handle cases where no network activity is detected within timeout."""
        try:
            # Wait a bit longer for any delayed network requests
            await asyncio.sleep(2)
            
            if not self.auth_request_detected:
                self.logger.warning(f"No getUserInfo request detected after {timeout_seconds} seconds")
                self.network_error = "No authentication request detected - user may not have attempted login"
                return False
            
            if self.auth_request_detected and not self.user_info_response and not self.network_error:
                self.logger.warning("Authentication request detected but no response received")
                self.network_error = "Authentication request timeout - no response received"
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error handling network timeout: {e}")
            return False
