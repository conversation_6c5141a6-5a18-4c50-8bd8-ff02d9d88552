"""Authentication handler for RunningHub.cn login flow."""

import asyncio
from typing import Dict, Any, Optional
from playwright.async_api import Browser<PERSON>ontext, Page

from ..config.settings import settings
from ..config.selectors import RunningHubSelectors, SELECTOR_TIMEOUTS
from ..config.constants import AuthStatus
from ..models.auth import AuthRequest, AuthResponse, AuthMethod
from ..models.exceptions import AuthenticationError, PageLoadError
from ..logging_utils.setup import get_logger, PerformanceLogger
from .page_operations import PageOperations


class AuthHandler:
    """Handles authentication flow for RunningHub.cn."""
    
    def __init__(self, browser_context: BrowserContext):
        self.context = browser_context
        self.page: Optional[Page] = None
        self.page_ops: Optional[PageOperations] = None
        self.selectors = RunningHubSelectors()
        self.logger = get_logger(__name__)
    
    async def authenticate(self, auth_request: AuthRequest) -> AuthResponse:
        """Main authentication method that handles different auth types."""
        try:
            with PerformanceLogger(f"authenticate({auth_request.method})"):
                self.logger.info(f"Starting authentication with method: {auth_request.method}")
                
                # Create new page for authentication
                self.page = await self.context.new_page()
                self.page_ops = PageOperations(self.page)
                
                # Navigate to RunningHub.cn
                await self.page_ops.navigate_to(settings.runninghub_base_url)
                
                # Click login button to open modal
                await self._open_login_modal()
                
                # Handle different authentication methods
                if auth_request.method == AuthMethod.WECHAT:
                    return await self._handle_wechat_login(auth_request)
                elif auth_request.method == AuthMethod.PHONE_SMS:
                    return await self._handle_phone_sms_login(auth_request)
                elif auth_request.method == AuthMethod.PASSWORD:
                    return await self._handle_password_login(auth_request)
                else:
                    raise AuthenticationError(f"Unsupported authentication method: {auth_request.method}")
        
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError(f"Authentication process failed: {str(e)}")
        
        finally:
            # Keep page open for session persistence
            pass
    
    async def _open_login_modal(self):
        """Open the login modal by clicking the login button."""
        try:
            self.logger.debug("Opening login modal...")
            
            # Wait for page to load
            await self.page_ops.wait_for_page_load()
            
            # Click login button
            await self.page_ops.click_element(self.selectors.LOGIN_BUTTON)
            
            # Wait for modal to appear
            await self.page_ops.wait_for_element(self.selectors.LOGIN_MODAL)
            
            self.logger.debug("Login modal opened successfully")
            
        except Exception as e:
            raise AuthenticationError(f"Failed to open login modal: {str(e)}")
    
    async def _handle_wechat_login(self, auth_request: AuthRequest) -> AuthResponse:
        """Handle WeChat QR code login."""
        try:
            self.logger.info("Handling WeChat login...")
            
            # Switch to WeChat login tab if needed
            if await self.page_ops.is_element_visible(self.selectors.WECHAT_LOGIN_TAB):
                await self.page_ops.click_element(self.selectors.WECHAT_LOGIN_TAB)
            
            # Wait for QR code to appear
            await asyncio.sleep(2)  # Give time for QR code to load
            
            # Take screenshot to capture QR code
            qr_screenshot = await self.page_ops.take_screenshot()
            
            # Save QR code screenshot
            import os
            import time
            qr_filename = f"wechat_qr_{int(time.time())}.png"
            qr_path = os.path.join(settings.temp_dir, qr_filename)
            
            os.makedirs(settings.temp_dir, exist_ok=True)
            with open(qr_path, "wb") as f:
                f.write(qr_screenshot)
            
            self.logger.info(f"QR code saved to: {qr_path}")
            
            # Return response indicating manual action required
            return AuthResponse(
                status=AuthStatus.AUTHENTICATING,
                message="Please scan the WeChat QR code to complete authentication",
                requires_manual_action=True,
                manual_action_type="qr_scan",
                qr_code_url=f"/temp/{qr_filename}"
            )
            
        except Exception as e:
            raise AuthenticationError(f"WeChat login failed: {str(e)}")
    
    async def _handle_phone_sms_login(self, auth_request: AuthRequest) -> AuthResponse:
        """Handle phone SMS login."""
        try:
            self.logger.info("Handling phone SMS login...")
            
            # Switch to phone login tab if needed
            if await self.page_ops.is_element_visible(self.selectors.PHONE_LOGIN_TAB):
                await self.page_ops.click_element(self.selectors.PHONE_LOGIN_TAB)
            
            # Enter phone number if provided
            if auth_request.phone:
                await self.page_ops.type_text(self.selectors.PHONE_INPUT, auth_request.phone)
                
                # Click send SMS button (usually appears after entering phone)
                # This might trigger SMS sending
                await asyncio.sleep(1)
                
                # If SMS code is provided, enter it
                if auth_request.sms_code:
                    await self.page_ops.type_text(self.selectors.SMS_CODE_INPUT, auth_request.sms_code)
                    
                    # Click login button
                    await self.page_ops.click_element(self.selectors.LOGIN_SUBMIT)
                    
                    # Wait for authentication result
                    return await self._wait_for_auth_completion()
                else:
                    # Return response indicating SMS input required
                    return AuthResponse(
                        status=AuthStatus.AUTHENTICATING,
                        message="Please enter the SMS verification code",
                        requires_manual_action=True,
                        manual_action_type="sms_input"
                    )
            else:
                # Return response indicating phone number required
                return AuthResponse(
                    status=AuthStatus.AUTHENTICATING,
                    message="Please enter your phone number",
                    requires_manual_action=True,
                    manual_action_type="phone_input"
                )
                
        except Exception as e:
            raise AuthenticationError(f"Phone SMS login failed: {str(e)}")
    
    async def _handle_password_login(self, auth_request: AuthRequest) -> AuthResponse:
        """Handle username/password login."""
        try:
            self.logger.info("Handling password login...")
            
            if not auth_request.username or not auth_request.password:
                raise AuthenticationError("Username and password are required for password login")
            
            # Enter username (might be phone number or email)
            await self.page_ops.type_text(self.selectors.PHONE_INPUT, auth_request.username)
            
            # Enter password
            await self.page_ops.type_text(self.selectors.PASSWORD_INPUT, auth_request.password)
            
            # Click login button
            await self.page_ops.click_element(self.selectors.LOGIN_SUBMIT)
            
            # Wait for authentication result
            return await self._wait_for_auth_completion()
            
        except Exception as e:
            raise AuthenticationError(f"Password login failed: {str(e)}")
    
    async def _wait_for_auth_completion(self, timeout: Optional[int] = None) -> AuthResponse:
        """Wait for authentication to complete and return result."""
        timeout = timeout or settings.runninghub_login_timeout
        
        try:
            self.logger.debug("Waiting for authentication completion...")
            
            # Wait for either success or error indicators
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) * 1000 < timeout:
                # Check if login modal is closed (success indicator)
                if not await self.page_ops.is_element_visible(self.selectors.LOGIN_MODAL, timeout=1000):
                    self.logger.info("Login modal closed - authentication successful")
                    
                    # Extract user information
                    user_info = await self._extract_user_info()
                    
                    return AuthResponse(
                        status=AuthStatus.AUTHENTICATED,
                        message="Authentication successful",
                        user_info=user_info
                    )
                
                # Check for error messages
                if await self.page_ops.is_element_visible(self.selectors.ERROR_MESSAGE, timeout=1000):
                    error_text = await self.page_ops.get_element_text(self.selectors.ERROR_MESSAGE)
                    raise AuthenticationError(f"Login failed: {error_text}")
                
                # Wait a bit before checking again
                await asyncio.sleep(1)
            
            # Timeout reached
            return AuthResponse(
                status=AuthStatus.AUTHENTICATING,
                message="Authentication timeout - please complete manually",
                requires_manual_action=True,
                manual_action_type="manual_completion"
            )
            
        except AuthenticationError:
            raise
        except Exception as e:
            raise AuthenticationError(f"Error waiting for authentication completion: {str(e)}")
    
    async def _extract_user_info(self) -> Dict[str, Any]:
        """Extract user information after successful login."""
        try:
            self.logger.debug("Extracting user information...")
            
            # Wait for page to load after login
            await self.page_ops.wait_for_page_load()
            
            user_info = {
                "user_id": None,
                "username": None,
                "email": None,
                "avatar_url": None,
                "login_time": asyncio.get_event_loop().time()
            }
            
            # Try to extract user information from page
            # This would need to be customized based on RunningHub.cn's actual structure
            try:
                # Example: Look for user avatar or profile elements
                # user_avatar = await self.page_ops.get_element_attribute(".user-avatar", "src")
                # user_info["avatar_url"] = user_avatar
                pass
            except:
                # User info extraction is optional
                pass
            
            # Generate a user ID based on session or page content
            import hashlib
            import time
            session_data = f"{self.page.url}_{time.time()}"
            user_info["user_id"] = hashlib.md5(session_data.encode()).hexdigest()[:16]
            
            self.logger.debug(f"Extracted user info: {user_info}")
            return user_info
            
        except Exception as e:
            self.logger.warning(f"Failed to extract user info: {e}")
            # Return minimal user info
            import hashlib
            import time
            session_data = f"{self.page.url}_{time.time()}"
            return {
                "user_id": hashlib.md5(session_data.encode()).hexdigest()[:16],
                "login_time": asyncio.get_event_loop().time()
            }
    
    async def check_auth_status(self) -> bool:
        """Check if user is currently authenticated."""
        try:
            if not self.page:
                return False
            
            # Navigate to a page that requires authentication
            await self.page_ops.navigate_to(settings.runninghub_base_url)
            
            # Check if login button is present (indicates not logged in)
            if await self.page_ops.is_element_visible(self.selectors.LOGIN_BUTTON, timeout=5000):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking auth status: {e}")
            return False
    
    async def logout(self):
        """Logout from RunningHub.cn."""
        try:
            if not self.page:
                return
            
            self.logger.info("Logging out...")
            
            # Clear cookies and local storage
            await self.context.clear_cookies()
            await self.page.evaluate("localStorage.clear(); sessionStorage.clear();")
            
            self.logger.info("Logout completed")
            
        except Exception as e:
            self.logger.error(f"Error during logout: {e}")
    
    async def cleanup(self):
        """Cleanup authentication handler resources."""
        try:
            if self.page:
                await self.page.close()
                self.page = None
                self.page_ops = None
            
        except Exception as e:
            self.logger.error(f"Error during auth handler cleanup: {e}")
