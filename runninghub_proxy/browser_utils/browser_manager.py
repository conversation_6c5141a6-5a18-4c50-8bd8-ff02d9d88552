"""Browser manager for Playwright automation."""

import asyncio
from typing import Dict, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright

from ..config.settings import settings
from ..config.constants import BROWSER_CONFIG
from ..models.exceptions import BrowserAutomationError
from ..logging_utils.setup import get_logger


class BrowserManager:
    """Manages Playwright browser instances and contexts."""
    
    def __init__(self):
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.contexts: Dict[str, BrowserContext] = {}
        self.logger = get_logger(__name__)
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize Playwright and browser."""
        try:
            self.logger.info("Initializing browser manager...")
            
            # Start Playwright
            self.playwright = await async_playwright().start()
            
            # Launch browser with improved options
            browser_args = [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-ipc-flooding-protection",
            ]

            if settings.browser_headless:
                browser_args.extend([
                    "--disable-extensions",
                    "--disable-plugins",
                ])
            else:
                browser_args.extend([
                    "--start-maximized",
                    "--disable-infobars",
                    "--disable-notifications",
                ])

            self.browser = await self.playwright.chromium.launch(
                headless=settings.browser_headless,
                args=browser_args,
                slow_mo=100 if not settings.browser_headless else 0,  # Slow down for visual debugging
            )
            
            self.logger.info("Browser manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize browser manager: {e}")
            raise BrowserAutomationError(f"Browser initialization failed: {str(e)}")
    
    async def create_context(self, **kwargs) -> BrowserContext:
        """Create a new browser context with smart resource blocking."""
        if not self.browser:
            raise BrowserAutomationError("Browser not initialized")

        try:
            # Default context options
            context_options = {
                "viewport": BROWSER_CONFIG["viewport"],
                "user_agent": settings.browser_user_agent or BROWSER_CONFIG["user_agent"],
                "locale": BROWSER_CONFIG["locale"],
                "timezone_id": BROWSER_CONFIG["timezone_id"],
                "accept_downloads": True,
                "ignore_https_errors": True,
                **kwargs
            }

            context = await self.browser.new_context(**context_options)

            # Set up comprehensive logging and debugging
            await self._setup_debug_logging(context)

            # Set up smart resource blocking
            await self._setup_resource_blocking(context)
            
            # Set additional context properties
            await context.add_init_script("""
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // Mock chrome property
                window.chrome = {
                    runtime: {},
                };
                
                // Mock permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)
            
            self.logger.info("Created new browser context with smart resource blocking")
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to create browser context: {e}")
            raise BrowserAutomationError(f"Context creation failed: {str(e)}")

    async def _setup_debug_logging(self, context: BrowserContext):
        """Set up comprehensive debug logging for troubleshooting."""

        if settings.network_logging:
            # Log all network requests and responses
            async def log_request(request):
                self.logger.debug(f"🌐 REQUEST: {request.method} {request.url}")
                self.logger.debug(f"   Headers: {dict(request.headers)}")
                if request.post_data:
                    self.logger.debug(f"   Body: {request.post_data[:500]}...")

            async def log_response(response):
                self.logger.debug(f"📡 RESPONSE: {response.status} {response.url}")
                self.logger.debug(f"   Headers: {dict(response.headers)}")
                if response.status >= 400:
                    self.logger.warning(f"❌ HTTP Error {response.status}: {response.url}")

            async def log_request_failed(request):
                self.logger.error(f"💥 REQUEST FAILED: {request.url}")
                self.logger.error(f"   Method: {request.method}")
                self.logger.error(f"   Failure: {request.failure}")

            context.on("request", log_request)
            context.on("response", log_response)
            context.on("requestfailed", log_request_failed)

        if settings.console_logging:
            # Log browser console messages
            async def log_console(msg):
                level = msg.type
                text = msg.text
                location = f"{msg.location['url']}:{msg.location['lineNumber']}" if msg.location else "unknown"

                # Check if we should suppress this console error
                if level == "error" and settings.suppress_console_error_logs:
                    # Check if this error matches any suppression patterns
                    for pattern in settings.suppress_failed_resource_patterns:
                        if pattern in text:
                            return  # Suppress this log

                if level == "error":
                    self.logger.error(f"🔴 CONSOLE ERROR: {text} ({location})")
                elif level == "warning":
                    self.logger.warning(f"🟡 CONSOLE WARNING: {text} ({location})")
                elif level == "info":
                    self.logger.info(f"🔵 CONSOLE INFO: {text} ({location})")
                else:
                    self.logger.debug(f"⚪ CONSOLE {level.upper()}: {text} ({location})")

            context.on("console", log_console)

        # Log page events
        async def log_page_created(page):
            self.logger.info(f"📄 PAGE CREATED: {page.url}")

            # Set up page-level event logging
            async def log_load(page):
                self.logger.info(f"✅ PAGE LOADED: {page.url}")

            async def log_domcontentloaded(page):
                self.logger.info(f"🏗️  DOM CONTENT LOADED: {page.url}")

            async def log_crash(page):
                self.logger.error(f"💥 PAGE CRASHED: {page.url}")

            async def log_pageerror(error):
                self.logger.error(f"🚨 PAGE ERROR: {error}")

            page.on("load", lambda: log_load(page))
            page.on("domcontentloaded", lambda: log_domcontentloaded(page))
            page.on("crash", lambda: log_crash(page))
            page.on("pageerror", log_pageerror)

        context.on("page", log_page_created)

        self.logger.info("🔍 Debug logging enabled (network, console, page events)")

    async def _setup_resource_blocking(self, context: BrowserContext):
        """Set up smart resource blocking to save data and improve speed."""

        async def handle_route(route, request):
            """Handle resource requests with smart blocking."""
            url = request.url
            resource_type = request.resource_type

            # Check if we're on a workflow page (allow all resources)
            page_url = route.request.frame.page.url if route.request.frame and route.request.frame.page else ""
            is_workflow_page = (
                "workflow" in page_url.lower() or
                "comfyui" in page_url.lower() or
                "/app" in page_url.lower() or
                "editor" in page_url.lower()
            )

            # Smart blocking for non-workflow pages
            if not is_workflow_page:
                # Define essential UI resources that should NEVER be blocked
                essential_ui_patterns = [
                    "_nuxt/",  # Nuxt.js framework assets
                    "login",   # Login-related resources
                    "auth",    # Authentication resources
                    "button",  # Button-related resources
                    "icon",    # Icon resources
                    "logo",    # Logo resources
                    "nav",     # Navigation resources
                    "header",  # Header resources
                    "modal",   # Modal resources
                    "ui",      # General UI resources
                ]

                # Check if this is an essential UI resource
                is_essential_ui = any(pattern in url.lower() for pattern in essential_ui_patterns)

                # Block images and videos with exceptions for essential UI
                if settings.browser_block_images and resource_type == "image" and not is_essential_ui:
                    # Only log if suppression is disabled
                    if not settings.suppress_blocked_resource_logs:
                        self.logger.debug(f"🚫 Blocked non-essential {resource_type}: {url[:100]}...")
                    await route.abort()
                    return
                elif settings.browser_block_videos and resource_type == "media":
                    # Always block videos (they're rarely essential for UI)
                    if not settings.suppress_blocked_resource_logs:
                        self.logger.debug(f"🚫 Blocked {resource_type}: {url[:100]}...")
                    await route.abort()
                    return
                elif resource_type == "font" and not is_essential_ui:
                    # Block non-essential fonts but allow UI fonts
                    if not settings.suppress_blocked_resource_logs:
                        self.logger.debug(f"🚫 Blocked non-essential {resource_type}: {url[:100]}...")
                    await route.abort()
                    return

            # Block ads and tracking scripts if enabled
            if settings.browser_block_ads:
                blocked_patterns = [
                    "google-analytics",
                    "googletagmanager",
                    "facebook.com/tr",
                    "doubleclick.net",
                    "googlesyndication",
                    "amazon-adsystem",
                    "adsystem.amazon",
                    "ads.yahoo.com",
                    "analytics",
                    "tracking",
                    "metrics",
                    "telemetry",
                    ".gif",
                    "beacon",
                    "pixel",
                ]

                # Check for blocked patterns
                for pattern in blocked_patterns:
                    if pattern in url.lower():
                        # Only log if suppression is disabled
                        if not settings.suppress_blocked_resource_logs:
                            self.logger.debug(f"🚫 Blocked tracking/ads: {url[:100]}...")
                        await route.abort()
                        return

            # Allow the request
            await route.continue_()

        # Set up route handler for all resource types
        await context.route("**/*", handle_route)
        self.logger.info("📡 Smart resource blocking enabled (images/videos blocked except on workflow pages)")

    async def get_or_create_session_context(self, session_token: str) -> BrowserContext:
        """Get existing context for session or create new one."""
        async with self._lock:
            if session_token in self.contexts:
                # Check if context is still valid
                try:
                    # Try to create a page to test context validity
                    test_page = await self.contexts[session_token].new_page()
                    await test_page.close()
                    return self.contexts[session_token]
                except Exception:
                    # Context is invalid, remove it
                    try:
                        await self.contexts[session_token].close()
                    except Exception:
                        pass
                    del self.contexts[session_token]
            
            # Create new context for session
            context = await self.create_context()
            self.contexts[session_token] = context
            
            self.logger.info(f"Created context for session: {session_token[:8]}...")
            return context
    
    async def close_session_context(self, session_token: str):
        """Close browser context for specific session."""
        async with self._lock:
            if session_token in self.contexts:
                try:
                    await self.contexts[session_token].close()
                    self.logger.info(f"Closed context for session: {session_token[:8]}...")
                except Exception as e:
                    self.logger.error(f"Error closing context for session {session_token[:8]}: {e}")
                finally:
                    del self.contexts[session_token]
    
    async def create_page(self, context: BrowserContext) -> Page:
        """Create a new page in the given context."""
        try:
            page = await context.new_page()
            
            # Set default timeout
            page.set_default_timeout(settings.browser_timeout)
            
            # Set up page event handlers
            page.on("console", self._handle_console_message)
            page.on("pageerror", self._handle_page_error)
            page.on("requestfailed", self._handle_request_failed)
            
            return page
            
        except Exception as e:
            self.logger.error(f"Failed to create page: {e}")
            raise BrowserAutomationError(f"Page creation failed: {str(e)}")
    
    def _handle_console_message(self, msg):
        """Handle console messages from browser."""
        if msg.type in ["error", "warning"]:
            self.logger.warning(f"Browser console {msg.type}: {msg.text}")
        elif settings.debug:
            self.logger.debug(f"Browser console {msg.type}: {msg.text}")
    
    def _handle_page_error(self, error):
        """Handle page errors."""
        self.logger.error(f"Browser page error: {error}")
    
    def _handle_request_failed(self, request):
        """Handle failed requests."""
        self.logger.warning(f"Browser request failed: {request.url} - {request.failure}")
    
    async def cleanup(self):
        """Cleanup browser resources."""
        try:
            self.logger.info("Cleaning up browser manager...")
            
            # Close all contexts
            for session_token in list(self.contexts.keys()):
                await self.close_session_context(session_token)
            
            # Close browser
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            # Stop Playwright
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.logger.info("Browser manager cleanup complete")
            
        except Exception as e:
            self.logger.error(f"Error during browser cleanup: {e}")
    
    async def get_browser_info(self) -> Dict[str, Any]:
        """Get browser information."""
        if not self.browser:
            return {"status": "not_initialized"}
        
        try:
            version = await self.browser.version()
            return {
                "status": "initialized",
                "version": version,
                "contexts": len(self.contexts),
                "headless": settings.browser_headless,
            }
        except Exception as e:
            self.logger.error(f"Error getting browser info: {e}")
            return {"status": "error", "error": str(e)}
    
    def __del__(self):
        """Destructor to ensure cleanup."""
        if self.browser or self.playwright:
            self.logger.warning("BrowserManager not properly cleaned up")
            # Note: Can't call async cleanup from __del__
