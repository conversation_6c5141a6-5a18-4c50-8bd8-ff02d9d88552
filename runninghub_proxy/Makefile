# RunningHub Proxy API - Development Makefile

.PHONY: help install dev test lint format clean run docker build

# Default target
help:
	@echo "RunningHub Proxy API - Development Commands"
	@echo "==========================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install     Install dependencies with uv"
	@echo "  dev         Install development dependencies"
	@echo ""
	@echo "Development Commands:"
	@echo "  run         Start the development server"
	@echo "  test        Run all tests"
	@echo "  test-unit   Run unit tests only"
	@echo "  test-e2e    Run end-to-end tests"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint        Run linting (ruff, mypy)"
	@echo "  format      Format code (black, isort)"
	@echo "  check       Run all quality checks"
	@echo ""
	@echo "Deployment:"
	@echo "  docker      Build Docker image"
	@echo "  build       Build distribution packages"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean       Clean temporary files"
	@echo "  update      Update dependencies"

# Installation
install:
	@echo "📦 Installing dependencies with uv..."
	uv sync
	uv run playwright install chromium

dev:
	@echo "🔧 Installing development dependencies..."
	uv sync --extra dev
	uv run playwright install chromium
	uv run pre-commit install

# Development
run:
	@echo "🚀 Starting development server..."
	uv run python server.py

test:
	@echo "🧪 Running all tests..."
	uv run pytest tests/ -v

test-unit:
	@echo "🧪 Running unit tests..."
	uv run pytest tests/test_basic.py -v

test-e2e:
	@echo "🧪 Running end-to-end tests..."
	uv run python test_server.py

# Code Quality
lint:
	@echo "🔍 Running linting..."
	uv run ruff check .
	uv run mypy runninghub_proxy/

format:
	@echo "✨ Formatting code..."
	uv run black .
	uv run isort .
	uv run ruff check --fix .

check: lint test
	@echo "✅ All quality checks passed!"

# Deployment
docker:
	@echo "🐳 Building Docker image..."
	docker build -t runninghub-proxy:latest .

build:
	@echo "📦 Building distribution packages..."
	uv build

# Maintenance
clean:
	@echo "🧹 Cleaning temporary files..."
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/ .pytest_cache/ .coverage htmlcov/
	rm -rf logs/ temp/ downloads/

update:
	@echo "🔄 Updating dependencies..."
	uv sync --upgrade

# Development shortcuts
server: run
start: run
tests: test
fmt: format

# Environment setup
setup: install
	@echo "📁 Creating directories..."
	mkdir -p logs temp downloads
	@echo "📝 Creating .env file..."
	cp .env.example .env
	@echo ""
	@echo "✅ Setup complete!"
	@echo "📋 Next steps:"
	@echo "1. Edit .env file with your configuration"
	@echo "2. Run 'make run' to start the server"
	@echo "3. Visit http://localhost:8000/docs for API documentation"

# Quick development setup
quick-start: setup run

# Production deployment helpers
prod-check:
	@echo "🔍 Running production readiness checks..."
	uv run python -c "from runninghub_proxy.config.settings import settings; print('✅ Configuration valid')"
	uv run pytest tests/test_basic.py -q
	@echo "✅ Production checks passed!"

# Documentation
docs:
	@echo "📚 Generating documentation..."
	@echo "Design: docs/DESIGN.md"
	@echo "Deployment: docs/DEPLOYMENT.md" 
	@echo "API Reference: docs/API_REFERENCE.md"
	@echo "Project Validation: PROJECT_VALIDATION.md"

# Show project status
status:
	@echo "📊 Project Status"
	@echo "================"
	@echo "Python: $(shell python --version)"
	@echo "uv: $(shell uv --version 2>/dev/null || echo 'Not installed')"
	@echo "Project: $(shell grep '^version' pyproject.toml | cut -d'"' -f2)"
	@echo "Dependencies: $(shell uv pip list 2>/dev/null | wc -l || echo 'Unknown') packages"
	@echo ""
	@echo "📁 Project Structure:"
	@echo "  Source: runninghub_proxy/"
	@echo "  Tests: tests/"
	@echo "  Docs: docs/"
	@echo "  Config: pyproject.toml, .env.example"
