#!/usr/bin/env python3
"""One-command installation for RunningHub Proxy API."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_cmd(cmd, description="", check=True):
    """Run a command and handle errors."""
    if description:
        print(f"🔄 {description}...")
    
    print(f"   $ {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"   {result.stdout.strip()}")
        if description:
            print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed")
        if e.stdout:
            print(f"   STDOUT: {e.stdout}")
        if e.stderr:
            print(f"   STDERR: {e.stderr}")
        return False


def main():
    """Main installation function."""
    print("🚀 RunningHub Proxy API - One-Command Installation")
    print("=" * 60)

    # Check Python version
    if sys.version_info < (3, 9):
        print(f"❌ Python 3.9+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        print("Please upgrade Python to 3.9 or higher")
        return False

    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ pyproject.toml not found. Please run from the runninghub_proxy directory.")
        print("Current directory contents:")
        for item in Path(".").iterdir():
            print(f"  {item.name}")
        return False

    print("✅ Found pyproject.toml")

    # Check if uv.lock exists (indicates project is already set up for uv)
    if Path("uv.lock").exists():
        print("✅ Found uv.lock - project is configured for uv")
    
    # Install uv if not present
    if not shutil.which("uv"):
        print("📦 Installing uv...")
        if not run_cmd("curl -LsSf https://astral.sh/uv/install.sh | sh", "Installing uv"):
            print("❌ Failed to install uv. Please install manually:")
            print("   curl -LsSf https://astral.sh/uv/install.sh | sh")
            return False
        
        # Add uv to PATH for current session
        os.environ["PATH"] = f"{os.path.expanduser('~')}/.cargo/bin:{os.environ['PATH']}"
    else:
        print("✅ uv already installed")
    
    # Install dependencies
    if not run_cmd("uv sync", "Installing dependencies"):
        return False
    
    # Install Playwright browsers
    if not run_cmd("uv run playwright install chromium", "Installing Playwright browsers"):
        return False
    
    # Create directories
    print("📁 Creating directories...")
    for directory in ["logs", "temp", "downloads"]:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/")
    
    # Setup environment
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✅ Created .env from .env.example")
        else:
            print("⚠️  No .env.example found")
    else:
        print("✅ .env already exists")
    
    # Test installation
    print("\n🧪 Testing installation...")
    
    tests = [
        ("uv run python -c 'import runninghub_proxy'", "Module import"),
        ("uv run python -c 'from runninghub_proxy.config.settings import settings'", "Configuration"),
        ("uv run python -c 'from playwright.async_api import async_playwright'", "Playwright"),
    ]
    
    all_passed = True
    for cmd, desc in tests:
        if run_cmd(cmd, f"Testing {desc}", check=False):
            print(f"✅ {desc} test passed")
        else:
            print(f"❌ {desc} test failed")
            all_passed = False
    
    # Final message
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file if needed:")
        print("   nano .env")
        print("\n2. Start the server:")
        print("   uv run python server.py")
        print("\n3. Visit API documentation:")
        print("   http://localhost:8000/docs")
        print("\n4. Run tests:")
        print("   uv run pytest tests/")
        print("\n💡 Useful commands:")
        print("   uv run python server.py          # Start server")
        print("   uv run python verify_installation.py  # Verify setup")
        print("   uv run pytest tests/             # Run tests")
    else:
        print("⚠️  Installation completed with some test failures.")
        print("Run 'uv run python verify_installation.py' for detailed diagnostics.")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Installation interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)
