#!/usr/bin/env python3
"""Debug script to investigate duplicate login button elements."""

import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from runninghub_proxy.browser_utils.browser_manager import <PERSON><PERSON><PERSON><PERSON>ana<PERSON>
from runninghub_proxy.config.settings import settings


async def debug_login_elements():
    """Debug login button elements to understand the duplication issue."""
    print("🔍 Debugging Login Button Elements")
    print("=" * 50)
    
    browser_manager = BrowserManager()
    
    try:
        # Initialize browser
        await browser_manager.initialize()
        print("✅ Browser initialized")
        
        # Create context
        context = await browser_manager.create_context()
        print("✅ Browser context created")
        
        # Create page
        page = await context.new_page()
        print("✅ Page created")
        
        # Navigate to RunningHub
        print("🌐 Navigating to RunningHub.cn...")
        await page.goto("https://www.runninghub.cn", wait_until="domcontentloaded")
        print("✅ Page loaded")
        
        # Wait for page to settle
        await asyncio.sleep(3)
        
        # Check all possible login button selectors
        selectors_to_test = [
            "button:has-text('登 录')",
            "button:has-text('登录')",
            ".login-btn",
            "button.login-btn",
            "[class*='login']",
            "button[class*='login']",
            "button:contains('登')",
        ]
        
        print("\n🔍 Testing Login Button Selectors:")
        print("-" * 40)
        
        for selector in selectors_to_test:
            try:
                elements = await page.locator(selector).all()
                count = len(elements)
                
                if count > 0:
                    print(f"✅ {selector}: Found {count} element(s)")
                    
                    # Get details for each element
                    for i, element in enumerate(elements):
                        try:
                            text = await element.text_content()
                            classes = await element.get_attribute("class")
                            visible = await element.is_visible()
                            print(f"   [{i+1}] Text: '{text}' | Visible: {visible}")
                            print(f"       Classes: {classes}")
                            
                            # Get bounding box
                            box = await element.bounding_box()
                            if box:
                                print(f"       Position: x={box['x']}, y={box['y']}, w={box['width']}, h={box['height']}")
                            else:
                                print(f"       Position: Not visible/no bounding box")
                            print()
                        except Exception as e:
                            print(f"   [{i+1}] Error getting element details: {e}")
                else:
                    print(f"❌ {selector}: Not found")
            except Exception as e:
                print(f"❌ {selector}: Error - {e}")
        
        # Test the current selector from the code
        print("\n🎯 Testing Current LOGIN_BUTTON Selector:")
        print("-" * 40)
        current_selector = "button:has-text('登 录'):visible, button:has-text('登录'):visible, .login-btn:visible"
        
        try:
            elements = await page.locator(current_selector).all()
            count = len(elements)
            print(f"Current selector: {current_selector}")
            print(f"Found {count} element(s)")
            
            if count > 1:
                print("⚠️  STRICT MODE VIOLATION: Multiple elements found!")
                print("Analyzing differences...")
                
                for i, element in enumerate(elements):
                    try:
                        text = await element.text_content()
                        classes = await element.get_attribute("class")
                        visible = await element.is_visible()
                        box = await element.bounding_box()
                        
                        print(f"\n   Element {i+1}:")
                        print(f"   - Text: '{text}'")
                        print(f"   - Classes: {classes}")
                        print(f"   - Visible: {visible}")
                        if box:
                            print(f"   - Position: x={box['x']}, y={box['y']}, w={box['width']}, h={box['height']}")
                        else:
                            print(f"   - Position: Not visible")
                            
                        # Check parent elements
                        parent = await element.locator("..").first
                        parent_classes = await parent.get_attribute("class")
                        print(f"   - Parent classes: {parent_classes}")
                        
                    except Exception as e:
                        print(f"   Element {i+1}: Error - {e}")
        except Exception as e:
            print(f"Error testing current selector: {e}")
        
        # Suggest fixes
        print("\n💡 Suggested Fixes:")
        print("-" * 20)
        
        # Test specific selectors that might work better
        better_selectors = [
            "button:has-text('登 录'):visible",
            "button:has-text('登 录').first",
            "button:has-text('登 录'):nth(0)",
            ".login-btn:visible",
            ".login-btn.first",
            "header button:has-text('登 录')",
            "nav button:has-text('登 录')",
            "[class*='header'] button:has-text('登 录')",
        ]
        
        for selector in better_selectors:
            try:
                elements = await page.locator(selector).all()
                count = len(elements)
                if count == 1:
                    element = elements[0]
                    visible = await element.is_visible()
                    if visible:
                        print(f"✅ RECOMMENDED: {selector} (1 visible element)")
                elif count > 1:
                    print(f"⚠️  {selector} (still {count} elements)")
                else:
                    print(f"❌ {selector} (not found)")
            except Exception as e:
                print(f"❌ {selector} (error: {e})")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            await browser_manager.cleanup()
            print("\n✅ Browser cleanup complete")
        except Exception as e:
            print(f"⚠️  Cleanup error: {e}")


async def main():
    """Main function."""
    try:
        await debug_login_elements()
        return True
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
