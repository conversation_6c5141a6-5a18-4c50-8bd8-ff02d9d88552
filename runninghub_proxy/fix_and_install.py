#!/usr/bin/env python3
"""Fix build configuration and install RunningHub Proxy API."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_cmd(cmd, description="", check=True):
    """Run a command and handle errors."""
    if description:
        print(f"🔄 {description}...")
    
    print(f"   $ {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"   {result.stdout.strip()}")
        if description:
            print(f"✅ {description} completed")
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed")
        if e.stdout:
            print(f"   STDOUT: {e.stdout}")
        if e.stderr:
            print(f"   STDERR: {e.stderr}")
        return False, e.stdout, e.stderr


def main():
    """Main fix and installation function."""
    print("🔧 RunningHub Proxy API - Fix Build Configuration & Install")
    print("=" * 70)
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ pyproject.toml not found. Please run from the runninghub_proxy directory.")
        return False
    
    print("✅ Found pyproject.toml")
    
    # Check if the fix is already applied
    with open("pyproject.toml", "r") as f:
        content = f.read()
    
    if "[tool.hatch.build.targets.wheel]" in content:
        print("✅ Build configuration already fixed")
    else:
        print("❌ Build configuration needs fixing")
        print("The pyproject.toml has been updated with the correct Hatchling configuration.")
        print("Please run this script again.")
        return False
    
    # Clean any previous build artifacts
    print("\n🧹 Cleaning previous build artifacts...")
    for path in ["build", "dist", "*.egg-info", ".venv"]:
        if Path(path).exists():
            if Path(path).is_dir():
                shutil.rmtree(path)
                print(f"   Removed directory: {path}")
            else:
                Path(path).unlink()
                print(f"   Removed file: {path}")
    
    # Try installation with different approaches
    print("\n📦 Installing dependencies...")
    
    # Method 1: Try uv sync with clean cache
    print("\n🔄 Method 1: UV sync with clean cache...")
    success, stdout, stderr = run_cmd("uv cache clean", "Cleaning UV cache", check=False)
    success, stdout, stderr = run_cmd("uv sync", "Installing with UV sync", check=False)
    
    if success:
        print("✅ UV sync successful!")
    else:
        print("❌ UV sync failed, trying alternative method...")
        
        # Method 2: Try pip install in editable mode
        print("\n🔄 Method 2: Pip install in editable mode...")
        
        # Create virtual environment first
        success, stdout, stderr = run_cmd("python -m venv .venv", "Creating virtual environment", check=False)
        
        if success:
            # Activate virtual environment and install
            if os.name == 'nt':  # Windows
                activate_cmd = ".venv\\Scripts\\activate"
                pip_cmd = ".venv\\Scripts\\pip"
            else:  # Unix/Linux/macOS
                activate_cmd = "source .venv/bin/activate"
                pip_cmd = ".venv/bin/pip"
            
            # Install in editable mode
            success, stdout, stderr = run_cmd(f"{pip_cmd} install -e .", "Installing with pip", check=False)
            
            if success:
                print("✅ Pip install successful!")
            else:
                print("❌ Pip install failed, trying method 3...")
                
                # Method 3: Install dependencies directly
                print("\n🔄 Method 3: Installing dependencies directly...")
                
                # Read dependencies from pyproject.toml
                dependencies = [
                    "fastapi>=0.104.1",
                    "uvicorn[standard]>=0.24.0",
                    "playwright>=1.40.0",
                    "pydantic>=2.5.0",
                    "pydantic-settings>=2.1.0",
                    "httpx>=0.25.2",
                    "websockets>=12.0",
                    "python-multipart>=0.0.6",
                    "sse-starlette>=1.6.5",
                    "aiofiles>=23.2.1",
                    "python-dotenv>=1.0.0",
                    "loguru>=0.7.2",
                ]
                
                # Install each dependency
                for dep in dependencies:
                    success, stdout, stderr = run_cmd(f"{pip_cmd} install '{dep}'", f"Installing {dep.split('>=')[0]}", check=False)
                    if not success:
                        print(f"⚠️  Failed to install {dep}, continuing...")
    
    # Install Playwright browsers
    print("\n🎭 Installing Playwright browsers...")
    
    # Try with uv first
    success, stdout, stderr = run_cmd("uv run playwright install chromium", "Installing Playwright with UV", check=False)
    
    if not success:
        # Try with direct playwright command
        success, stdout, stderr = run_cmd("playwright install chromium", "Installing Playwright directly", check=False)
        
        if not success:
            # Try with pip installed playwright
            if os.name == 'nt':
                playwright_cmd = ".venv\\Scripts\\playwright"
            else:
                playwright_cmd = ".venv/bin/playwright"
            
            success, stdout, stderr = run_cmd(f"{playwright_cmd} install chromium", "Installing Playwright from venv", check=False)
    
    # Create directories
    print("\n📁 Creating necessary directories...")
    for directory in ["logs", "temp", "downloads"]:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/")
    
    # Setup environment
    print("\n⚙️  Setting up environment...")
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✅ Created .env from .env.example")
        else:
            print("⚠️  No .env.example found")
    else:
        print("✅ .env already exists")
    
    # Test installation
    print("\n🧪 Testing installation...")
    
    # Test with uv first
    success, stdout, stderr = run_cmd("uv run python -c 'import runninghub_proxy; print(\"✅ Module import successful\")'", "Testing module import with UV", check=False)
    
    if not success:
        # Test with venv python
        if os.name == 'nt':
            python_cmd = ".venv\\Scripts\\python"
        else:
            python_cmd = ".venv/bin/python"
        
        success, stdout, stderr = run_cmd(f"{python_cmd} -c 'import runninghub_proxy; print(\"✅ Module import successful\")'", "Testing module import with venv", check=False)
    
    if success:
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start the server:")
        print("   uv run python server.py")
        print("   OR")
        print("   .venv/bin/python server.py  # Unix/Linux/macOS")
        print("   .venv\\Scripts\\python server.py  # Windows")
        print("\n2. Visit API documentation:")
        print("   http://localhost:8000/docs")
        print("\n3. Test the health endpoint:")
        print("   curl http://localhost:8000/health")
        return True
    else:
        print("\n⚠️  Installation completed but module import failed.")
        print("Please check the error messages above and try manual installation.")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Installation interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)
