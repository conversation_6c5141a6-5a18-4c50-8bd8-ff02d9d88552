"""Main server entry point for RunningHub Proxy API."""

import asyncio
import signal
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from runninghub_proxy.api_utils.app import run_server
from runninghub_proxy.config.settings import settings
from runninghub_proxy.logging_utils.setup import setup_logging, get_logger


def handle_shutdown(signum, frame):
    """Handle shutdown signals gracefully."""
    logger = get_logger(__name__)
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main entry point for the RunningHub Proxy API server."""
    # Setup logging first
    setup_logging()
    logger = get_logger(__name__)

    try:
        logger.info("Starting RunningHub Proxy API Server...")
        logger.info(f"Configuration: Host={settings.host}, Port={settings.port}, Debug={settings.debug}")

        # Register signal handlers
        signal.signal(signal.SIGINT, handle_shutdown)
        signal.signal(signal.SIGTERM, handle_shutdown)

        # Run the server
        run_server()

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
