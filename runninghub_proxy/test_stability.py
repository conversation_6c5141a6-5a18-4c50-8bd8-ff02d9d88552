#!/usr/bin/env python3
"""Stability test for RunningHub Proxy API with uv package manager."""

import os
import sys
import json
import time
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Tuple


class StabilityTester:
    """Test stability and performance of the RunningHub Proxy API."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results: Dict[str, bool] = {}
        self.performance_metrics: Dict[str, float] = {}
        
    def log(self, message: str, level: str = "INFO"):
        """Log messages with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def run_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """Run a command and return success, stdout, stderr."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Command timed out"
        except Exception as e:
            return False, "", str(e)
    
    def test_uv_installation(self) -> bool:
        """Test uv package manager installation and functionality."""
        self.log("🔍 Testing uv package manager...")
        
        # Check if uv is installed
        if not shutil.which("uv"):
            self.log("❌ uv not found. Installing uv...", "WARNING")
            success, stdout, stderr = self.run_command(
                "curl -LsSf https://astral.sh/uv/install.sh | sh"
            )
            if not success:
                self.log(f"❌ Failed to install uv: {stderr}", "ERROR")
                return False
        
        # Test uv version
        success, stdout, stderr = self.run_command("uv --version")
        if not success:
            self.log(f"❌ uv version check failed: {stderr}", "ERROR")
            return False
        
        self.log(f"✅ uv version: {stdout.strip()}")
        return True
    
    def test_project_structure(self) -> bool:
        """Test project structure and configuration files."""
        self.log("🧪 Testing project structure...")
        
        required_files = [
            "pyproject.toml",
            "server.py",
            "Makefile",
            ".pre-commit-config.yaml",
            "runninghub_proxy/__init__.py",
            "docs/DESIGN.md",
            "docs/DEPLOYMENT.md",
            "docs/API_REFERENCE.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.log(f"❌ Missing files: {missing_files}", "ERROR")
            return False
        
        # Test pyproject.toml structure
        try:
            import tomllib
        except ImportError:
            try:
                import tomli as tomllib
            except ImportError:
                self.log("⚠️  Cannot validate pyproject.toml - tomllib not available", "WARNING")
                return True
        
        try:
            with open(self.project_root / "pyproject.toml", "rb") as f:
                pyproject_data = tomllib.load(f)
            
            required_sections = ["project", "build-system", "tool.black", "tool.ruff"]
            for section in required_sections:
                if section not in pyproject_data:
                    self.log(f"❌ Missing section in pyproject.toml: {section}", "ERROR")
                    return False
            
            self.log("✅ Project structure is valid")
            return True
            
        except Exception as e:
            self.log(f"❌ Error validating pyproject.toml: {e}", "ERROR")
            return False
    
    def test_dependency_installation(self) -> bool:
        """Test dependency installation with uv."""
        self.log("📦 Testing dependency installation...")
        
        start_time = time.time()
        
        # Test uv sync
        success, stdout, stderr = self.run_command("uv sync", timeout=120)
        if not success:
            self.log(f"❌ uv sync failed: {stderr}", "ERROR")
            return False
        
        install_time = time.time() - start_time
        self.performance_metrics["dependency_install_time"] = install_time
        self.log(f"✅ Dependencies installed in {install_time:.2f}s")
        
        # Test that we can import the main module
        success, stdout, stderr = self.run_command(
            "uv run python -c 'import runninghub_proxy; print(\"Import successful\")'"
        )
        if not success:
            self.log(f"❌ Module import failed: {stderr}", "ERROR")
            return False
        
        self.log("✅ Module imports successfully")
        return True
    
    def test_code_quality_tools(self) -> bool:
        """Test code quality tools."""
        self.log("🔍 Testing code quality tools...")
        
        # Test black formatting
        success, stdout, stderr = self.run_command("uv run black --check .")
        if not success:
            self.log("⚠️  Code formatting issues found", "WARNING")
            # Auto-fix formatting
            self.run_command("uv run black .")
        
        # Test ruff linting
        success, stdout, stderr = self.run_command("uv run ruff check .")
        if not success:
            self.log("⚠️  Linting issues found", "WARNING")
            # Try to auto-fix
            self.run_command("uv run ruff check --fix .")
        
        # Test mypy type checking (optional)
        success, stdout, stderr = self.run_command("uv run mypy runninghub_proxy/ --ignore-missing-imports")
        if not success:
            self.log("⚠️  Type checking issues found", "WARNING")
        
        self.log("✅ Code quality tools tested")
        return True
    
    def test_server_startup(self) -> bool:
        """Test server startup and basic functionality."""
        self.log("🚀 Testing server startup...")
        
        # Create .env file if it doesn't exist
        env_file = self.project_root / ".env"
        if not env_file.exists():
            example_env = self.project_root / ".env.example"
            if example_env.exists():
                shutil.copy(example_env, env_file)
        
        # Test that server can start (dry run)
        success, stdout, stderr = self.run_command(
            "uv run python -c 'from runninghub_proxy.api_utils.app import create_app; app = create_app(); print(\"App created successfully\")'"
        )
        if not success:
            self.log(f"❌ Server startup test failed: {stderr}", "ERROR")
            return False
        
        self.log("✅ Server startup test passed")
        return True
    
    def test_playwright_installation(self) -> bool:
        """Test Playwright browser installation."""
        self.log("🎭 Testing Playwright installation...")
        
        start_time = time.time()
        
        # Install Playwright browsers
        success, stdout, stderr = self.run_command(
            "uv run playwright install chromium", timeout=180
        )
        if not success:
            self.log(f"❌ Playwright installation failed: {stderr}", "ERROR")
            return False
        
        playwright_time = time.time() - start_time
        self.performance_metrics["playwright_install_time"] = playwright_time
        self.log(f"✅ Playwright installed in {playwright_time:.2f}s")
        
        # Test Playwright functionality
        success, stdout, stderr = self.run_command(
            "uv run python -c 'from playwright.sync_api import sync_playwright; print(\"Playwright import successful\")'"
        )
        if not success:
            self.log(f"❌ Playwright import failed: {stderr}", "ERROR")
            return False
        
        self.log("✅ Playwright functionality verified")
        return True
    
    def test_makefile_commands(self) -> bool:
        """Test Makefile commands."""
        self.log("🔧 Testing Makefile commands...")
        
        # Test make help
        success, stdout, stderr = self.run_command("make help")
        if not success:
            self.log("⚠️  Makefile help command failed", "WARNING")
            return True  # Not critical
        
        # Test make status
        success, stdout, stderr = self.run_command("make status")
        if success:
            self.log("✅ Makefile commands working")
        else:
            self.log("⚠️  Some Makefile commands failed", "WARNING")
        
        return True
    
    def run_performance_tests(self) -> bool:
        """Run performance tests."""
        self.log("⚡ Running performance tests...")
        
        # Test import time
        start_time = time.time()
        success, stdout, stderr = self.run_command(
            "uv run python -c 'import runninghub_proxy'"
        )
        import_time = time.time() - start_time
        self.performance_metrics["import_time"] = import_time
        
        if import_time > 5.0:
            self.log(f"⚠️  Slow import time: {import_time:.2f}s", "WARNING")
        else:
            self.log(f"✅ Fast import time: {import_time:.2f}s")
        
        return True
    
    def run_all_tests(self) -> bool:
        """Run all stability tests."""
        self.log("🧪 Starting Stability Test Suite")
        self.log("=" * 60)
        
        tests = [
            ("UV Installation", self.test_uv_installation),
            ("Project Structure", self.test_project_structure),
            ("Dependency Installation", self.test_dependency_installation),
            ("Code Quality Tools", self.test_code_quality_tools),
            ("Server Startup", self.test_server_startup),
            ("Playwright Installation", self.test_playwright_installation),
            ("Makefile Commands", self.test_makefile_commands),
            ("Performance Tests", self.run_performance_tests),
        ]
        
        results = []
        for test_name, test_func in tests:
            self.log(f"\n📋 Running {test_name}...")
            try:
                result = test_func()
                results.append(result)
                self.test_results[test_name] = result
                
                if result:
                    self.log(f"✅ {test_name} passed")
                else:
                    self.log(f"❌ {test_name} failed", "ERROR")
            except Exception as e:
                self.log(f"❌ {test_name} failed with exception: {e}", "ERROR")
                results.append(False)
                self.test_results[test_name] = False
        
        # Print summary
        self.print_summary(results, tests)
        
        return all(results)
    
    def print_summary(self, results: List[bool], tests: List[Tuple[str, callable]]):
        """Print test summary and performance metrics."""
        self.log("\n" + "=" * 60)
        self.log("📊 Stability Test Results")
        self.log("=" * 60)
        
        passed = sum(results)
        total = len(results)
        
        for i, (test_name, _) in enumerate(tests):
            status = "✅ PASS" if results[i] else "❌ FAIL"
            self.log(f"{test_name}: {status}")
        
        self.log("-" * 60)
        self.log(f"Total Tests: {total}")
        self.log(f"Passed: {passed}")
        self.log(f"Failed: {total - passed}")
        self.log(f"Success Rate: {(passed/total)*100:.1f}%")
        
        # Performance metrics
        if self.performance_metrics:
            self.log("\n⚡ Performance Metrics:")
            for metric, value in self.performance_metrics.items():
                self.log(f"  {metric}: {value:.2f}s")
        
        if passed == total:
            self.log("\n🎉 All stability tests passed!")
            self.log("✅ Project is stable and ready for use")
        else:
            self.log("\n⚠️  Some stability tests failed")
            self.log("🔧 Please review the errors above")


def main():
    """Main test function."""
    print("🧪 RunningHub Proxy API - Stability Test Suite")
    print("=" * 60)
    
    tester = StabilityTester()
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
