#!/usr/bin/env python3
"""Fix debug configuration issue."""

import os
import sys
from pathlib import Path


def fix_debug_config():
    """Fix the debug configuration conflict."""
    print("🔧 Fixing Debug Configuration Issue")
    print("=" * 50)
    
    # Check current .env file
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    print("📝 Reading current .env file...")
    with open(env_file, "r") as f:
        lines = f.readlines()
    
    # Fix the configuration
    fixed_lines = []
    debug_found = False
    app_debug_found = False
    
    for line in lines:
        if line.startswith("DEBUG="):
            # Keep the Playwright debug setting
            fixed_lines.append(line)
            debug_found = True
            print(f"✅ Kept Playwright debug: {line.strip()}")
        elif line.startswith("APP_DEBUG="):
            # Keep the app debug setting
            fixed_lines.append(line)
            app_debug_found = True
            print(f"✅ Found app debug: {line.strip()}")
        else:
            fixed_lines.append(line)
    
    # Add APP_DEBUG if not found
    if not app_debug_found:
        # Find the right place to insert it (after PORT)
        insert_index = 0
        for i, line in enumerate(fixed_lines):
            if line.startswith("PORT="):
                insert_index = i + 1
                break
        
        fixed_lines.insert(insert_index, "APP_DEBUG=false\n")
        print("✅ Added APP_DEBUG=false")
    
    # Write back the fixed configuration
    with open(env_file, "w") as f:
        f.writelines(fixed_lines)
    
    print("✅ .env file fixed!")
    
    # Test the configuration
    print("\n🧪 Testing configuration...")
    try:
        # Set environment variables
        os.environ["APP_DEBUG"] = "false"
        os.environ["DEBUG"] = "pw:api,pw:browser*"
        os.environ["PWDEBUG"] = "1"
        
        # Try to import settings
        sys.path.insert(0, str(Path.cwd()))
        from runninghub_proxy.config.settings import settings
        
        print("✅ Settings imported successfully!")
        print(f"   App debug: {settings.debug}")
        print(f"   Playwright debug: {settings.playwright_debug}")
        print(f"   Debug mode: {settings.playwright_debug_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """Main function."""
    print("🔧 RunningHub Proxy - Debug Configuration Fix")
    print("=" * 60)
    
    success = fix_debug_config()
    
    if success:
        print("\n🎉 Configuration fixed successfully!")
        print("\n📋 Next steps:")
        print("1. Start the server:")
        print("   uv run python server.py")
        print("\n2. Run diagnostics:")
        print("   python diagnose_page_loading.py")
        print("\n3. Test authentication:")
        print("   python test_auth_with_api_key.py")
    else:
        print("\n❌ Configuration fix failed")
        print("Please check the error messages above")
    
    return success


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
