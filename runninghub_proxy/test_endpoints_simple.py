#!/usr/bin/env python3
"""Simple synchronous API endpoint testing."""

import requests
import json
import sys
from datetime import datetime


def test_endpoint(method, url, data=None, expected_status=None):
    """Test a single endpoint."""
    try:
        print(f"🔄 Testing {method} {url}")
        
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        print(f"   Status Code: {response.status_code}")
        
        # Try to parse JSON response
        try:
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)[:200]}...")
        except:
            print(f"   Response: {response.text[:200]}...")
        
        # Check if status code is expected
        if expected_status:
            success = response.status_code in expected_status if isinstance(expected_status, list) else response.status_code == expected_status
        else:
            success = 200 <= response.status_code < 300
        
        status_emoji = "✅" if success else "❌"
        print(f"   {status_emoji} {'Success' if success else 'Failed'}")
        print()
        
        return success
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        print()
        return False


def main():
    """Main test function."""
    base_url = "http://localhost:8973"

    # Test if server is running first
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ Server is running on {base_url}")
    except Exception as e:
        print(f"❌ Server is not accessible at {base_url}: {e}")
        print("Please make sure the server is running with: uv run python server.py")
        return False
    
    print("🚀 RunningHub Proxy API - Simple Endpoint Testing")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Test Time: {datetime.now().isoformat()}")
    print()
    
    tests = []
    
    # Health endpoint
    print("🏥 Testing Health Endpoint")
    print("-" * 30)
    tests.append(test_endpoint("GET", f"{base_url}/health", expected_status=200))
    
    # Documentation endpoints
    print("📚 Testing Documentation Endpoints")
    print("-" * 30)
    tests.append(test_endpoint("GET", f"{base_url}/docs", expected_status=200))
    tests.append(test_endpoint("GET", f"{base_url}/redoc", expected_status=200))
    tests.append(test_endpoint("GET", f"{base_url}/openapi.json", expected_status=200))
    
    # Authentication endpoints
    print("🔐 Testing Authentication Endpoints")
    print("-" * 30)
    tests.append(test_endpoint("GET", f"{base_url}/auth/status", expected_status=[200, 401]))
    
    # Test login with invalid data
    login_data = {
        "method": "phone_sms",
        "phone": "invalid_phone"
    }
    tests.append(test_endpoint("POST", f"{base_url}/auth/login", data=login_data, expected_status=[400, 422, 500]))
    
    # Test logout (should require auth)
    tests.append(test_endpoint("POST", f"{base_url}/auth/logout", expected_status=[401, 403]))
    
    # Workflow endpoints
    print("⚙️  Testing Workflow Endpoints")
    print("-" * 30)
    workflow_id = "test_workflow_123"
    tests.append(test_endpoint("GET", f"{base_url}/workflows/{workflow_id}/schema", expected_status=[200, 401, 404, 500]))
    
    # Test workflow execution
    execution_data = {
        "workflow_id": workflow_id,
        "nodeInfo_list": [
            {
                "node_id": "test_node",
                "field_name": "test_field", 
                "field_value": "test_value",
                "class_type": "test_type"
            }
        ]
    }
    tests.append(test_endpoint("POST", f"{base_url}/workflows/{workflow_id}/execute", data=execution_data, expected_status=[200, 401, 422, 500]))
    
    # Test workflow history
    tests.append(test_endpoint("GET", f"{base_url}/workflows/history", expected_status=[200, 401, 500]))
    
    # Error handling
    print("🚫 Testing Error Handling")
    print("-" * 30)
    tests.append(test_endpoint("GET", f"{base_url}/nonexistent", expected_status=404))
    
    # Summary
    print("=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(tests)
    successful_tests = sum(tests)
    failed_tests = total_tests - successful_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if failed_tests == 0:
        print("\n🎉 All tests passed!")
        return True
    else:
        print(f"\n⚠️  {failed_tests} tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
