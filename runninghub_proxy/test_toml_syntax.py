#!/usr/bin/env python3
"""Test TOML syntax of pyproject.toml"""

import sys
from pathlib import Path

def test_toml_syntax():
    """Test if pyproject.toml has valid syntax."""
    try:
        # Try to import tomllib (Python 3.11+) or tomli
        try:
            import tomllib
        except ImportError:
            try:
                import tomli as tomllib
            except ImportError:
                print("⚠️  TOML library not available, installing tomli...")
                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", "tomli"])
                import tomli as tomllib
        
        # Read and parse pyproject.toml
        with open("pyproject.toml", "rb") as f:
            data = tomllib.load(f)
        
        print("✅ pyproject.toml syntax is valid!")
        
        # Check for required sections
        required_sections = ["project", "build-system"]
        for section in required_sections:
            if section in data:
                print(f"✅ Found required section: [{section}]")
            else:
                print(f"❌ Missing required section: [{section}]")
        
        # Check for duplicate keys in hatch build targets
        if "tool" in data and "hatch" in data["tool"] and "build" in data["tool"]["hatch"]:
            targets = data["tool"]["hatch"]["build"].get("targets", {})
            if "wheel" in targets:
                print("✅ Hatch wheel target configuration found")
            if "sdist" in targets:
                print("✅ Hatch sdist target configuration found")
        
        return True
        
    except Exception as e:
        print(f"❌ pyproject.toml syntax error: {e}")
        return False

if __name__ == "__main__":
    success = test_toml_syntax()
    if success:
        print("\n🎉 TOML syntax is correct! You can now run:")
        print("   uv sync")
        print("   python install.py")
    else:
        print("\n❌ Please fix the TOML syntax errors above")
    
    sys.exit(0 if success else 1)
