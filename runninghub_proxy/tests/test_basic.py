"""Basic tests for RunningHub Proxy API."""

import pytest
import async<PERSON>
from unittest.mock import Mock, AsyncMock

from runninghub_proxy.config.settings import settings
from runninghub_proxy.models.auth import AuthRequest, AuthMethod
from runninghub_proxy.models.workflow import NodeInfo, WorkflowSchema
from runninghub_proxy.browser_utils.browser_manager import BrowserManager
from runninghub_proxy.browser_utils.session_manager import SessionManager


class TestConfiguration:
    """Test configuration and settings."""
    
    def test_settings_load(self):
        """Test that settings load correctly."""
        assert settings.host is not None
        assert settings.port > 0
        assert settings.api_title is not None
    
    def test_runninghub_base_url(self):
        """Test RunningHub base URL configuration."""
        assert settings.runninghub_base_url.startswith("https://")
        assert "runninghub.cn" in settings.runninghub_base_url


class TestDataModels:
    """Test data model validation."""
    
    def test_auth_request_validation(self):
        """Test AuthRequest model validation."""
        # Valid phone SMS request
        auth_request = AuthRequest(
            method=AuthMethod.PHONE_SMS,
            phone="13800138000",
            sms_code="123456"
        )
        assert auth_request.method == AuthMethod.PHONE_SMS
        assert auth_request.phone == "13800138000"
        assert auth_request.sms_code == "123456"
    
    def test_node_info_validation(self):
        """Test NodeInfo model validation."""
        node_info = NodeInfo(
            node_id="test_node_1",
            field_name="prompt",
            field_value="test prompt",
            class_type="TextInput",
            meta={"title": "Test Node"}
        )
        assert node_info.node_id == "test_node_1"
        assert node_info.field_name == "prompt"
        assert node_info.class_type == "TextInput"
    
    def test_workflow_schema_validation(self):
        """Test WorkflowSchema model validation."""
        node_info_list = [
            NodeInfo(
                node_id="1",
                field_name="prompt",
                field_value="test",
                class_type="TextInput"
            )
        ]
        
        schema = WorkflowSchema(
            workflow_id="test_workflow_123",
            title="Test Workflow",
            node_info_list=node_info_list
        )
        assert schema.workflow_id == "test_workflow_123"
        assert schema.title == "Test Workflow"
        assert len(schema.node_info_list) == 1


class TestBrowserManager:
    """Test browser manager functionality."""
    
    @pytest.mark.asyncio
    async def test_browser_manager_creation(self):
        """Test browser manager can be created."""
        browser_manager = BrowserManager()
        assert browser_manager is not None
        assert browser_manager.browser is None  # Not initialized yet
    
    @pytest.mark.asyncio
    async def test_browser_manager_info(self):
        """Test browser manager info when not initialized."""
        browser_manager = BrowserManager()
        info = await browser_manager.get_browser_info()
        assert info["status"] == "not_initialized"


class TestSessionManager:
    """Test session manager functionality."""
    
    @pytest.mark.asyncio
    async def test_session_manager_creation(self):
        """Test session manager can be created."""
        session_manager = SessionManager()
        assert session_manager is not None
        assert len(session_manager.sessions) == 0
    
    @pytest.mark.asyncio
    async def test_create_session(self):
        """Test session creation."""
        session_manager = SessionManager()
        
        user_info = {
            "user_id": "test_user_123",
            "username": "testuser"
        }
        
        session_info = await session_manager.create_session(user_info=user_info)
        
        assert session_info["user_id"] == "test_user_123"
        assert session_info["session_token"] is not None
        assert session_info["is_active"] is True
    
    @pytest.mark.asyncio
    async def test_session_validation(self):
        """Test session validation."""
        session_manager = SessionManager()
        
        # Create session
        session_info = await session_manager.create_session()
        session_token = session_info["session_token"]
        
        # Validate session
        is_valid = await session_manager.is_session_valid(session_token)
        assert is_valid is True
        
        # Invalid session
        is_valid = await session_manager.is_session_valid("invalid_token")
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_session_stats(self):
        """Test session manager statistics."""
        session_manager = SessionManager()
        
        # Initial stats
        stats = session_manager.get_stats()
        assert stats["total_sessions"] == 0
        assert stats["active_sessions"] == 0
        
        # Create session
        await session_manager.create_session()
        
        # Updated stats
        stats = session_manager.get_stats()
        assert stats["total_sessions"] == 1
        assert stats["active_sessions"] == 1


class TestIntegration:
    """Integration tests."""
    
    def test_import_all_modules(self):
        """Test that all modules can be imported without errors."""
        # Test config imports
        from runninghub_proxy.config import settings
        from runninghub_proxy.config.constants import WorkflowStatus
        from runninghub_proxy.config.selectors import RunningHubSelectors
        
        # Test model imports
        from runninghub_proxy.models.auth import AuthRequest
        from runninghub_proxy.models.workflow import WorkflowSchema
        from runninghub_proxy.models.exceptions import AuthenticationError
        
        # Test browser utils imports
        from runninghub_proxy.browser_utils import BrowserManager, SessionManager
        
        # Test API utils imports
        from runninghub_proxy.api_utils.app import create_app
        
        assert True  # If we get here, all imports succeeded
    
    def test_fastapi_app_creation(self):
        """Test FastAPI app can be created."""
        from runninghub_proxy.api_utils.app import create_app
        
        app = create_app()
        assert app is not None
        assert app.title == settings.api_title


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])
