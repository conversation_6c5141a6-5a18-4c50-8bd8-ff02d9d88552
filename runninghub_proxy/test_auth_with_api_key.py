#!/usr/bin/env python3
"""Test authentication with proper API key."""

import requests
import json
import sys
from datetime import datetime


def test_login_with_api_key():
    """Test login endpoint with proper API key authentication."""
    base_url = "http://localhost:8973"
    api_key = "kala"  # From your .env file
    
    print("🔐 Testing RunningHub Proxy API Authentication")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"API Key: {api_key}")
    print(f"Test Time: {datetime.now().isoformat()}")
    print()
    
    # Test 1: Health check (no auth required)
    print("🏥 Testing Health Endpoint (no auth required)")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            print("✅ Health check successful")
        else:
            print("❌ Health check failed")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    print()
    
    # Test 2: Auth status (no auth required)
    print("🔍 Testing Auth Status (no auth required)")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/auth/status", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.headers.get("content-type", "").startswith("application/json"):
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Response: {response.text[:200]}...")
        print("✅ Auth status check completed")
    except Exception as e:
        print(f"❌ Auth status error: {e}")
    print()
    
    # Test 3: Login with API key (Bearer token)
    print("🔑 Testing Login with API Key (Bearer Token)")
    print("-" * 40)
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    login_data = {
        "method": "wechat",  # Try WeChat login to see the QR code
        "timeout": 300
    }
    
    try:
        print(f"Request Headers: {headers}")
        print(f"Request Data: {json.dumps(login_data, indent=2)}")
        
        response = requests.post(
            f"{base_url}/auth/login", 
            json=login_data, 
            headers=headers,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.headers.get("content-type", "").startswith("application/json"):
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if response.status_code == 200:
                print("✅ Login request successful!")
                if data.get("requires_manual_action"):
                    print("📱 Manual action required (e.g., scan QR code)")
                    if data.get("qr_code_url"):
                        print(f"QR Code URL: {data['qr_code_url']}")
            else:
                print(f"⚠️  Login returned status {response.status_code}")
        else:
            print(f"Response: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Login error: {e}")
    print()
    
    # Test 4: Login with API key (X-API-Key header)
    print("🔑 Testing Login with API Key (X-API-Key Header)")
    print("-" * 40)
    
    headers_alt = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    login_data_phone = {
        "method": "phone_sms",
        "phone": "13800138000",  # Test phone number
        "timeout": 300
    }
    
    try:
        print(f"Request Headers: {headers_alt}")
        print(f"Request Data: {json.dumps(login_data_phone, indent=2)}")
        
        response = requests.post(
            f"{base_url}/auth/login", 
            json=login_data_phone, 
            headers=headers_alt,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.headers.get("content-type", "").startswith("application/json"):
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if response.status_code == 200:
                print("✅ Login request successful!")
                if data.get("requires_manual_action"):
                    print("📱 Manual action required (e.g., enter SMS code)")
            else:
                print(f"⚠️  Login returned status {response.status_code}")
        else:
            print(f"Response: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Login error: {e}")
    print()
    
    # Test 5: Login without API key (should fail)
    print("🚫 Testing Login without API Key (should fail with 401)")
    print("-" * 40)
    
    try:
        response = requests.post(
            f"{base_url}/auth/login", 
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.headers.get("content-type", "").startswith("application/json"):
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Response: {response.text[:200]}...")
            
        if response.status_code == 401:
            print("✅ Correctly rejected request without API key")
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("=" * 60)
    print("To test authentication with the browser visible:")
    print("1. ✅ Set BROWSER_HEADLESS=false in .env (DONE)")
    print("2. 🔄 Restart the server to apply the change")
    print("3. 🔑 Use API key 'kala' in Authorization header")
    print("4. 📱 Try WeChat login to see QR code in browser")
    print("5. 📞 Try phone SMS login to see the flow")
    print()
    print("Example curl command with API key:")
    print(f'curl -X POST -H "Authorization: Bearer {api_key}" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"method": "wechat", "timeout": 300}\' \\')
    print(f'     {base_url}/auth/login')
    print()
    print("🔄 RESTART THE SERVER to see the browser window!")


if __name__ == "__main__":
    test_login_with_api_key()
