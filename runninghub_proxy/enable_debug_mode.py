#!/usr/bin/env python3
"""Enable debug mode for RunningHub Proxy API."""

import os
import sys
from pathlib import Path


def enable_debug_mode():
    """Enable comprehensive debug mode."""
    print("🔍 Enabling Debug Mode for RunningHub Proxy API")
    print("=" * 60)
    
    # Set environment variables for current session
    debug_vars = {
        "DEBUG": "pw:api,pw:browser*",
        "PWDEBUG": "1",
        "APP_DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "BROWSER_VERBOSE_LOGGING": "true",
        "NETWORK_LOGGING": "true",
        "CONSOLE_LOGGING": "true",
    }
    
    print("🔧 Setting debug environment variables:")
    for key, value in debug_vars.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    # Update .env file
    env_file = Path(".env")
    if env_file.exists():
        print(f"\n📝 Updating {env_file}...")
        
        # Read current content
        with open(env_file, "r") as f:
            lines = f.readlines()
        
        # Update or add debug settings
        updated_lines = []
        debug_section_found = False
        
        for line in lines:
            if "# Playwright Debug Configuration" in line:
                debug_section_found = True
            
            # Update existing debug variables
            if any(line.startswith(f"{var}=") for var in debug_vars):
                var_name = line.split("=")[0]
                if var_name in debug_vars:
                    updated_lines.append(f"{var_name}={debug_vars[var_name]}\n")
                    print(f"   Updated: {var_name}={debug_vars[var_name]}")
                else:
                    updated_lines.append(line)
            else:
                updated_lines.append(line)
        
        # Write back to file
        with open(env_file, "w") as f:
            f.writelines(updated_lines)
        
        print("✅ .env file updated")
    else:
        print("⚠️  .env file not found")
    
    print("\n🎯 Debug mode enabled! Features:")
    print("   🔍 Playwright API debugging (DEBUG=pw:api)")
    print("   🌐 Network request/response logging")
    print("   🔵 Browser console message logging")
    print("   📄 Page event logging (load, crash, etc.)")
    print("   ⏰ Extended timeouts for debugging")
    print("   🐛 Verbose error messages and stack traces")
    
    print("\n📋 Next steps:")
    print("1. Restart the server to apply debug settings:")
    print("   uv run python server.py")
    print("\n2. Run diagnostics to identify issues:")
    print("   python diagnose_page_loading.py")
    print("\n3. Test authentication with verbose logging:")
    print("   python test_auth_with_api_key.py")
    
    print("\n💡 Debug output will be much more verbose!")
    print("   Look for detailed network, console, and page events")
    print("   Check server logs for step-by-step navigation details")
    
    return True


def disable_debug_mode():
    """Disable debug mode."""
    print("🔇 Disabling Debug Mode")
    print("-" * 30)
    
    # Remove debug environment variables
    debug_vars = ["DEBUG", "PWDEBUG", "APP_DEBUG", "LOG_LEVEL", "BROWSER_VERBOSE_LOGGING", "NETWORK_LOGGING", "CONSOLE_LOGGING"]
    
    for var in debug_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"   Removed: {var}")
    
    # Update .env file to disable debug settings
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, "r") as f:
            lines = f.readlines()
        
        updated_lines = []
        for line in lines:
            if any(line.startswith(f"{var}=") for var in debug_vars):
                var_name = line.split("=")[0]
                if var_name in ["BROWSER_VERBOSE_LOGGING", "NETWORK_LOGGING", "CONSOLE_LOGGING", "APP_DEBUG"]:
                    updated_lines.append(f"{var_name}=false\n")
                elif var_name == "LOG_LEVEL":
                    updated_lines.append(f"{var_name}=INFO\n")
                # Skip DEBUG and PWDEBUG lines
            else:
                updated_lines.append(line)
        
        with open(env_file, "w") as f:
            f.writelines(updated_lines)
        
        print("✅ Debug mode disabled in .env")
    
    print("🔄 Restart server to apply changes")


def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == "disable":
        disable_debug_mode()
    else:
        enable_debug_mode()


if __name__ == "__main__":
    main()
