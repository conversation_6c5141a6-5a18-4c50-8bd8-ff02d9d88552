#!/usr/bin/env python3
"""Comprehensive API endpoint testing for RunningHub Proxy API."""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
import httpx
import websockets
from datetime import datetime


class APITester:
    """Comprehensive API endpoint tester."""
    
    def __init__(self, base_url: str = "http://localhost:8973"):
        self.base_url = base_url
        self.session_token: Optional[str] = None
        self.test_results: List[Dict[str, Any]] = []
        
    def log_test_result(self, endpoint: str, method: str, status_code: int, 
                       response_data: Any, success: bool, error: str = None):
        """Log test result."""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "response_data": response_data,
            "error": error
        }
        self.test_results.append(result)
        
        status_emoji = "✅" if success else "❌"
        print(f"{status_emoji} {method} {endpoint} - Status: {status_code}")
        if error:
            print(f"   Error: {error}")
        if response_data and isinstance(response_data, dict):
            print(f"   Response: {json.dumps(response_data, indent=2)[:200]}...")
        print()
    
    async def test_health_endpoint(self):
        """Test health endpoint."""
        print("🏥 Testing Health Endpoint")
        print("-" * 40)
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                
                success = response.status_code == 200
                self.log_test_result("/health", "GET", response.status_code, data, success)
                
                # Validate response structure
                if success and isinstance(data, dict):
                    required_fields = ["status", "timestamp"]
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        print(f"⚠️  Missing required fields: {missing_fields}")
                
            except Exception as e:
                self.log_test_result("/health", "GET", 0, None, False, str(e))
    
    async def test_docs_endpoints(self):
        """Test documentation endpoints."""
        print("📚 Testing Documentation Endpoints")
        print("-" * 40)
        
        endpoints = [
            "/docs",
            "/redoc", 
            "/openapi.json"
        ]
        
        async with httpx.AsyncClient() as client:
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{self.base_url}{endpoint}")
                    
                    if endpoint == "/openapi.json":
                        data = response.json() if response.status_code == 200 else response.text
                    else:
                        data = "HTML content" if response.headers.get("content-type", "").startswith("text/html") else response.text
                    
                    success = response.status_code == 200
                    self.log_test_result(endpoint, "GET", response.status_code, data, success)
                    
                except Exception as e:
                    self.log_test_result(endpoint, "GET", 0, None, False, str(e))
    
    async def test_auth_endpoints(self):
        """Test authentication endpoints."""
        print("🔐 Testing Authentication Endpoints")
        print("-" * 40)
        
        async with httpx.AsyncClient() as client:
            # Test auth status (should work without authentication)
            try:
                response = await client.get(f"{self.base_url}/auth/status")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [200, 401]  # Both are valid responses
                self.log_test_result("/auth/status", "GET", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/auth/status", "GET", 0, None, False, str(e))
            
            # Test login endpoint with invalid data (should return 422 or 400)
            try:
                login_data = {
                    "method": "phone_sms",
                    "phone": "invalid_phone"
                }
                response = await client.post(f"{self.base_url}/auth/login", json=login_data)
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [400, 422, 500]  # Expected error responses
                self.log_test_result("/auth/login", "POST", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/auth/login", "POST", 0, None, False, str(e))
            
            # Test logout endpoint (should require authentication)
            try:
                response = await client.post(f"{self.base_url}/auth/logout")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [401, 403]  # Should require auth
                self.log_test_result("/auth/logout", "POST", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/auth/logout", "POST", 0, None, False, str(e))
    
    async def test_workflow_endpoints(self):
        """Test workflow endpoints."""
        print("⚙️  Testing Workflow Endpoints")
        print("-" * 40)
        
        async with httpx.AsyncClient() as client:
            # Test workflow schema endpoint
            test_workflow_id = "test_workflow_123"
            
            try:
                response = await client.get(f"{self.base_url}/workflows/{test_workflow_id}/schema")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [200, 401, 404, 500]  # Various valid responses
                self.log_test_result(f"/workflows/{test_workflow_id}/schema", "GET", response.status_code, data, success)
            except Exception as e:
                self.log_test_result(f"/workflows/{test_workflow_id}/schema", "GET", 0, None, False, str(e))
            
            # Test workflow execution endpoint
            try:
                execution_data = {
                    "workflow_id": test_workflow_id,
                    "nodeInfo_list": [
                        {
                            "node_id": "test_node",
                            "field_name": "test_field",
                            "field_value": "test_value",
                            "class_type": "test_type"
                        }
                    ]
                }
                response = await client.post(f"{self.base_url}/workflows/{test_workflow_id}/execute", json=execution_data)
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [200, 401, 422, 500]  # Various valid responses
                self.log_test_result(f"/workflows/{test_workflow_id}/execute", "POST", response.status_code, data, success)
            except Exception as e:
                self.log_test_result(f"/workflows/{test_workflow_id}/execute", "POST", 0, None, False, str(e))
            
            # Test workflow history endpoint
            try:
                response = await client.get(f"{self.base_url}/workflows/history")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code in [200, 401, 500]  # Various valid responses
                self.log_test_result("/workflows/history", "GET", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/workflows/history", "GET", 0, None, False, str(e))
    
    async def test_websocket_endpoint(self):
        """Test WebSocket endpoint."""
        print("🔌 Testing WebSocket Endpoint")
        print("-" * 40)
        
        try:
            # Test WebSocket connection
            ws_url = f"ws://localhost:8973/ws/logs"
            
            async with websockets.connect(ws_url, timeout=5) as websocket:
                # Send a test message
                await websocket.send("test message")
                
                # Try to receive a response (with timeout)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    self.log_test_result("/ws/logs", "WebSocket", 200, {"message": response}, True)
                except asyncio.TimeoutError:
                    self.log_test_result("/ws/logs", "WebSocket", 200, {"message": "Connection established, no immediate response"}, True)
                    
        except Exception as e:
            self.log_test_result("/ws/logs", "WebSocket", 0, None, False, str(e))
    
    async def test_error_handling(self):
        """Test error handling for invalid endpoints."""
        print("🚫 Testing Error Handling")
        print("-" * 40)
        
        async with httpx.AsyncClient() as client:
            # Test non-existent endpoint
            try:
                response = await client.get(f"{self.base_url}/nonexistent")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code == 404
                self.log_test_result("/nonexistent", "GET", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/nonexistent", "GET", 0, None, False, str(e))
            
            # Test invalid method
            try:
                response = await client.patch(f"{self.base_url}/health")
                data = response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                success = response.status_code == 405  # Method not allowed
                self.log_test_result("/health", "PATCH", response.status_code, data, success)
            except Exception as e:
                self.log_test_result("/health", "PATCH", 0, None, False, str(e))
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 API ENDPOINT TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        print("\n📋 Test Results by Category:")
        
        # Group by endpoint category
        categories = {}
        for result in self.test_results:
            endpoint = result["endpoint"]
            if endpoint.startswith("/auth"):
                category = "Authentication"
            elif endpoint.startswith("/workflows"):
                category = "Workflows"
            elif endpoint.startswith("/ws"):
                category = "WebSocket"
            elif endpoint in ["/docs", "/redoc", "/openapi.json"]:
                category = "Documentation"
            elif endpoint == "/health":
                category = "Health"
            else:
                category = "Other"
            
            if category not in categories:
                categories[category] = {"total": 0, "success": 0}
            
            categories[category]["total"] += 1
            if result["success"]:
                categories[category]["success"] += 1
        
        for category, stats in categories.items():
            success_rate = (stats["success"] / stats["total"]) * 100
            status_emoji = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
            print(f"  {status_emoji} {category}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        print("\n🔍 Failed Tests:")
        failed_results = [result for result in self.test_results if not result["success"]]
        if failed_results:
            for result in failed_results:
                print(f"  ❌ {result['method']} {result['endpoint']} - {result['error'] or f'Status {result['status_code']}'}")
        else:
            print("  🎉 No failed tests!")
        
        print("\n💡 Recommendations:")
        if failed_tests == 0:
            print("  ✅ All endpoints are working correctly!")
        else:
            print("  🔧 Review failed endpoints and fix implementation issues")
            print("  🔍 Check server logs for detailed error information")
            print("  🧪 Consider adding more comprehensive error handling")
    
    async def run_all_tests(self):
        """Run all API endpoint tests."""
        print("🚀 Starting Comprehensive API Endpoint Testing")
        print("=" * 60)
        print(f"Base URL: {self.base_url}")
        print(f"Test Start Time: {datetime.now().isoformat()}")
        print()
        
        # Run all test categories
        await self.test_health_endpoint()
        await self.test_docs_endpoints()
        await self.test_auth_endpoints()
        await self.test_workflow_endpoints()
        await self.test_websocket_endpoint()
        await self.test_error_handling()
        
        # Print summary
        self.print_summary()


async def main():
    """Main test function."""
    tester = APITester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
