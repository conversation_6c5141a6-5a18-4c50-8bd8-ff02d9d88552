"""WebSocket routes for real-time communication."""

import async<PERSON>
import j<PERSON>
from typing import Dict, Set, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends, Query
from websockets.exceptions import ConnectionClosed

from ...config.constants import WEBSOCKET_EVENTS
from ...config.settings import settings
from ...models.exceptions import AuthenticationError
from ..dependencies import get_session_manager
from ...browser_utils.session_manager import Session<PERSON>anager
from ...logging_utils.setup import get_logger


router = APIRouter()


class WebSocketManager:
    """Manage WebSocket connections and broadcasting."""
    
    def __init__(self):
        # Active connections by session token
        self.connections: Dict[str, Set[WebSocket]] = {}
        # Anonymous connections (no authentication)
        self.anonymous_connections: Set[WebSocket] = set()
        self.logger = get_logger(__name__)
    
    async def connect(self, websocket: WebSocket, session_token: Optional[str] = None):
        """Accept WebSocket connection."""
        await websocket.accept()
        
        if session_token:
            if session_token not in self.connections:
                self.connections[session_token] = set()
            self.connections[session_token].add(websocket)
            self.logger.info(f"WebSocket connected for session: {session_token[:8]}...")
        else:
            self.anonymous_connections.add(websocket)
            self.logger.info("Anonymous WebSocket connected")
    
    def disconnect(self, websocket: WebSocket, session_token: Optional[str] = None):
        """Remove WebSocket connection."""
        if session_token and session_token in self.connections:
            self.connections[session_token].discard(websocket)
            if not self.connections[session_token]:
                del self.connections[session_token]
            self.logger.info(f"WebSocket disconnected for session: {session_token[:8]}...")
        else:
            self.anonymous_connections.discard(websocket)
            self.logger.info("Anonymous WebSocket disconnected")
    
    async def send_to_session(self, session_token: str, message: dict):
        """Send message to all connections for a specific session."""
        if session_token not in self.connections:
            return
        
        disconnected = set()
        for websocket in self.connections[session_token]:
            try:
                await websocket.send_text(json.dumps(message))
            except (WebSocketDisconnect, ConnectionClosed):
                disconnected.add(websocket)
            except Exception as e:
                self.logger.error(f"Error sending WebSocket message: {e}")
                disconnected.add(websocket)
        
        # Remove disconnected connections
        for websocket in disconnected:
            self.connections[session_token].discard(websocket)
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast message to all connected clients."""
        # Send to authenticated connections
        for session_token in list(self.connections.keys()):
            await self.send_to_session(session_token, message)
        
        # Send to anonymous connections
        disconnected = set()
        for websocket in self.anonymous_connections:
            try:
                await websocket.send_text(json.dumps(message))
            except (WebSocketDisconnect, ConnectionClosed):
                disconnected.add(websocket)
            except Exception as e:
                self.logger.error(f"Error broadcasting WebSocket message: {e}")
                disconnected.add(websocket)
        
        # Remove disconnected anonymous connections
        for websocket in disconnected:
            self.anonymous_connections.discard(websocket)
    
    async def send_log_message(self, session_token: Optional[str], level: str, message: str, metadata: Optional[dict] = None):
        """Send log message via WebSocket."""
        log_event = {
            "event": WEBSOCKET_EVENTS["LOG"],
            "data": {
                "level": level,
                "message": message,
                "timestamp": asyncio.get_event_loop().time(),
                "metadata": metadata or {}
            }
        }
        
        if session_token:
            await self.send_to_session(session_token, log_event)
        else:
            await self.broadcast_to_all(log_event)
    
    async def send_auth_status(self, session_token: str, status: str, message: str):
        """Send authentication status update."""
        auth_event = {
            "event": WEBSOCKET_EVENTS["AUTH_STATUS"],
            "data": {
                "status": status,
                "message": message,
                "timestamp": asyncio.get_event_loop().time()
            }
        }
        
        await self.send_to_session(session_token, auth_event)
    
    async def send_workflow_progress(self, session_token: str, progress_data: dict):
        """Send workflow progress update."""
        progress_event = {
            "event": WEBSOCKET_EVENTS["WORKFLOW_PROGRESS"],
            "data": progress_data
        }
        
        await self.send_to_session(session_token, progress_event)
    
    async def send_workflow_result(self, session_token: str, result_data: dict):
        """Send workflow execution result."""
        result_event = {
            "event": WEBSOCKET_EVENTS["WORKFLOW_RESULT"],
            "data": result_data
        }
        
        await self.send_to_session(session_token, result_event)
    
    async def send_error(self, session_token: Optional[str], error_message: str, error_code: Optional[str] = None):
        """Send error message."""
        error_event = {
            "event": WEBSOCKET_EVENTS["ERROR"],
            "data": {
                "message": error_message,
                "code": error_code,
                "timestamp": asyncio.get_event_loop().time()
            }
        }
        
        if session_token:
            await self.send_to_session(session_token, error_event)
        else:
            await self.broadcast_to_all(error_event)
    
    def get_connection_count(self) -> dict:
        """Get connection statistics."""
        authenticated_count = sum(len(connections) for connections in self.connections.values())
        anonymous_count = len(self.anonymous_connections)
        
        return {
            "authenticated": authenticated_count,
            "anonymous": anonymous_count,
            "total": authenticated_count + anonymous_count,
            "sessions": len(self.connections)
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


@router.websocket("/logs")
async def websocket_logs(
    websocket: WebSocket,
    session_token: Optional[str] = Query(None),
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    WebSocket endpoint for real-time log streaming.
    
    Supports both authenticated and anonymous connections.
    Authenticated connections receive session-specific logs.
    Anonymous connections receive general system logs.
    """
    
    logger = get_logger(__name__)
    validated_session_token = None
    
    try:
        # Validate session token if provided
        if session_token:
            session_info = await session_manager.get_session(session_token)
            if session_info and await session_manager.is_session_valid(session_token):
                validated_session_token = session_token
            else:
                logger.warning(f"Invalid session token for WebSocket: {session_token[:8]}...")
        
        # Connect WebSocket
        await websocket_manager.connect(websocket, validated_session_token)
        
        # Send welcome message
        welcome_message = {
            "event": "connected",
            "data": {
                "message": "WebSocket connected successfully",
                "authenticated": validated_session_token is not None,
                "timestamp": asyncio.get_event_loop().time()
            }
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await handle_client_message(websocket, client_message, validated_session_token)
                except json.JSONDecodeError:
                    await websocket.send_text(json.dumps({
                        "event": "error",
                        "data": {"message": "Invalid JSON format"}
                    }))
                
            except WebSocketDisconnect:
                break
            except ConnectionClosed:
                break
            except Exception as e:
                logger.error(f"Error in WebSocket loop: {e}")
                await websocket_manager.send_error(
                    validated_session_token,
                    f"WebSocket error: {str(e)}"
                )
    
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    
    finally:
        # Disconnect WebSocket
        websocket_manager.disconnect(websocket, validated_session_token)


async def handle_client_message(websocket: WebSocket, message: dict, session_token: Optional[str]):
    """Handle incoming client messages."""
    logger = get_logger(__name__)
    
    try:
        message_type = message.get("type")
        
        if message_type == "ping":
            # Respond to ping with pong
            await websocket.send_text(json.dumps({
                "event": "pong",
                "data": {"timestamp": asyncio.get_event_loop().time()}
            }))
        
        elif message_type == "subscribe":
            # Handle subscription requests
            events = message.get("events", [])
            logger.info(f"Client subscribed to events: {events}")
            
            await websocket.send_text(json.dumps({
                "event": "subscribed",
                "data": {"events": events}
            }))
        
        elif message_type == "unsubscribe":
            # Handle unsubscription requests
            events = message.get("events", [])
            logger.info(f"Client unsubscribed from events: {events}")
            
            await websocket.send_text(json.dumps({
                "event": "unsubscribed",
                "data": {"events": events}
            }))
        
        else:
            logger.warning(f"Unknown message type: {message_type}")
            await websocket.send_text(json.dumps({
                "event": "error",
                "data": {"message": f"Unknown message type: {message_type}"}
            }))
    
    except Exception as e:
        logger.error(f"Error handling client message: {e}")
        await websocket.send_text(json.dumps({
            "event": "error",
            "data": {"message": f"Error processing message: {str(e)}"}
        }))


@router.get("/connections")
async def get_websocket_connections():
    """Get WebSocket connection statistics."""
    return websocket_manager.get_connection_count()


# Export websocket manager for use in other modules
__all__ = ["router", "websocket_manager"]
