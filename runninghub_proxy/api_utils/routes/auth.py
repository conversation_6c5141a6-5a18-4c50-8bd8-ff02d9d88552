"""Authentication routes for RunningHub Proxy API."""

from typing import Optional
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

from ...models.auth import (
    AuthRequest, AuthResponse, AuthStatus, SessionInfo,
    LogoutRequest, LogoutResponse
)
from ...models.exceptions import AuthenticationError
from ...config.constants import AuthStatus as AuthStatusEnum
from ...browser_utils.auth_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ...browser_utils.session_manager import SessionManager
from ..dependencies import (
    get_browser_context, get_session_manager,
    get_current_session, verify_api_key, get_logger_dependency,
    require_authentication
)
from ...logging_utils.setup import get_logger


router = APIRouter()


@router.post("/login", response_model=AuthResponse)
async def login(
    auth_request: AuthRequest,
    background_tasks: BackgroundTasks,
    browser_context = Depends(get_browser_context),
    session_manager: SessionManager = Depends(get_session_manager),
    api_key: Optional[str] = Depends(verify_api_key),
    logger = Depends(get_logger_dependency)
):
    """
    Initiate authentication flow with RunningHub.cn.
    
    This endpoint handles different authentication methods:
    - phone_sms: Phone number + SMS verification
    - wechat: WeChat QR code login
    - password: Username + password login
    
    For manual authentication methods (WeChat QR, SMS), the response will
    indicate that manual action is required, and the client should poll
    the status endpoint to check for completion.
    """
    
    try:
        logger.info(f"Starting authentication with method: {auth_request.method}")
        
        # Create auth handler
        auth_handler = AuthHandler(browser_context)
        
        # Start authentication process
        auth_result = await auth_handler.authenticate(auth_request)
        
        if auth_result.status == AuthStatusEnum.AUTHENTICATED:
            # Create session
            session_info = await session_manager.create_session(
                user_info=auth_result.user_info,
                browser_context=browser_context
            )
            
            logger.info(f"Authentication successful for user: {session_info.get('user_id', 'unknown')}")
            
            return AuthResponse(
                status=auth_result.status,
                session_token=session_info["session_token"],
                expires_at=session_info["expires_at"],
                user_info=auth_result.user_info,
                message="Authentication successful"
            )
        
        elif auth_result.status == AuthStatusEnum.AUTHENTICATING:
            # Manual action required
            logger.info("Manual authentication action required")
            
            # Store pending authentication state
            await session_manager.store_pending_auth(
                auth_handler=auth_handler,
                auth_request=auth_request
            )
            
            return AuthResponse(
                status=auth_result.status,
                message="Manual authentication required",
                requires_manual_action=True,
                manual_action_type=auth_result.manual_action_type,
                qr_code_url=auth_result.qr_code_url
            )
        
        else:
            # Authentication failed
            logger.warning(f"Authentication failed: {auth_result.message}")
            
            return AuthResponse(
                status=auth_result.status,
                message=auth_result.message or "Authentication failed"
            )
    
    except AuthenticationError as e:
        logger.error(f"Authentication error: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    
    except Exception as e:
        logger.error(f"Unexpected error during authentication: {e}")
        raise HTTPException(status_code=500, detail="Authentication process failed")


@router.get("/status", response_model=AuthStatus)
async def get_auth_status(
    session_info: Optional[dict] = Depends(get_current_session),
    session_manager: SessionManager = Depends(get_session_manager),
    logger = Depends(get_logger_dependency)
):
    """
    Get current authentication status.
    
    Returns information about the current session including:
    - Whether user is authenticated
    - Session validity
    - User information
    - Time until session expires
    """
    
    try:
        if not session_info:
            return AuthStatus(
                is_authenticated=False,
                session_valid=False,
                user_info=None,
                session_expires_at=None,
                time_until_expiry=None
            )
        
        # Check session validity
        session_token = session_info["session_token"]
        is_valid = await session_manager.is_session_valid(session_token)
        
        if not is_valid:
            return AuthStatus(
                is_authenticated=False,
                session_valid=False,
                user_info=None,
                session_expires_at=None,
                time_until_expiry=None
            )
        
        # Calculate time until expiry
        import datetime
        expires_at = session_info.get("expires_at")
        time_until_expiry = None
        if expires_at:
            if isinstance(expires_at, str):
                expires_at = datetime.datetime.fromisoformat(expires_at)
            time_until_expiry = int((expires_at - datetime.datetime.utcnow()).total_seconds())
            time_until_expiry = max(0, time_until_expiry)
        
        return AuthStatus(
            is_authenticated=True,
            session_valid=True,
            user_info=session_info.get("user_info"),
            session_expires_at=expires_at,
            time_until_expiry=time_until_expiry
        )
    
    except Exception as e:
        logger.error(f"Error getting auth status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get authentication status")


@router.post("/logout", response_model=LogoutResponse)
async def logout(
    logout_request: LogoutRequest = LogoutRequest(),
    session_info: Optional[dict] = Depends(get_current_session),
    session_manager: SessionManager = Depends(get_session_manager),
    logger = Depends(get_logger_dependency)
):
    """
    Logout and invalidate session.
    
    Can logout current session or all sessions for the user.
    """
    
    try:
        if not session_info:
            return LogoutResponse(
                success=True,
                message="No active session to logout",
                sessions_cleared=0
            )
        
        session_token = logout_request.session_token or session_info["session_token"]
        
        if logout_request.logout_all_sessions:
            # Logout all sessions for user
            user_id = session_info.get("user_id")
            if user_id:
                sessions_cleared = await session_manager.clear_user_sessions(user_id)
                logger.info(f"Cleared {sessions_cleared} sessions for user: {user_id}")
            else:
                sessions_cleared = 1
                await session_manager.invalidate_session(session_token)
        else:
            # Logout specific session
            await session_manager.invalidate_session(session_token)
            sessions_cleared = 1
            logger.info(f"Logged out session: {session_token[:8]}...")
        
        return LogoutResponse(
            success=True,
            message="Logout successful",
            sessions_cleared=sessions_cleared
        )
    
    except Exception as e:
        logger.error(f"Error during logout: {e}")
        raise HTTPException(status_code=500, detail="Logout failed")


@router.get("/session", response_model=SessionInfo)
async def get_session_info(
    session_info: dict = Depends(require_authentication),
    logger = Depends(get_logger_dependency)
):
    """
    Get detailed session information.
    
    Requires valid authentication.
    """
    
    try:
        return SessionInfo(**session_info)
    
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session information")
