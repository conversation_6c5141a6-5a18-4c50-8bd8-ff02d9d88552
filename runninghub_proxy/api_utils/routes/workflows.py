"""Workflow routes for RunningHub Proxy API."""

import asyncio
from datetime import datetime
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from ...models.workflow import (
    WorkflowSchema, WorkflowExecutionRequest, WorkflowProgress,
    WorkflowResult, WorkflowHistory
)
from ...models.exceptions import WorkflowNotFoundError, WorkflowExecutionError
from ...browser_utils.workflow_handler import WorkflowHandler
from ...browser_utils.session_manager import SessionManager
from ..dependencies import (
    get_browser_context, get_session_manager, require_authentication,
    check_concurrency_limit, get_concurrency_limiter, get_logger_dependency
)
from ...logging_utils.setup import get_logger


router = APIRouter()


@router.get("/{workflow_id}/schema", response_model=WorkflowSchema)
async def get_workflow_schema(
    workflow_id: str,
    browser_context = Depends(get_browser_context),
    session_info: dict = Depends(require_authentication),
    logger = Depends(get_logger_dependency)
):
    """
    Extract workflow schema from RunningHub.cn.
    
    Process:
    1. Navigate to workflow page: /post/{workflow_id}
    2. Click "运行工作流" button
    3. Wait for execution interface to load
    4. Right-click in ComfyUI iframe main area
    5. Click "导出工作流API" from context menu
    6. Parse downloaded API JSON and transform to required format
    """
    
    try:
        logger.info(f"Extracting schema for workflow: {workflow_id}")
        
        # Create workflow handler
        workflow_handler = WorkflowHandler(browser_context)
        
        # Extract workflow schema
        schema = await workflow_handler.extract_schema(workflow_id)
        
        logger.info(f"Successfully extracted schema for workflow: {workflow_id}")
        return schema
    
    except WorkflowNotFoundError:
        logger.warning(f"Workflow not found: {workflow_id}")
        raise
    
    except Exception as e:
        logger.error(f"Error extracting schema for workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to extract workflow schema: {str(e)}"
        )


@router.post("/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str,
    execution_request: WorkflowExecutionRequest,
    background_tasks: BackgroundTasks,
    browser_context = Depends(get_browser_context),
    session_info: dict = Depends(require_authentication),
    limiter = Depends(check_concurrency_limit),
    logger = Depends(get_logger_dependency)
):
    """
    Execute workflow with streaming progress updates.
    
    Returns Server-Sent Events (SSE) stream with progress updates.
    
    Process:
    1. Navigate to workflow execution page
    2. Fill form inputs based on nodeInfo_list
    3. Click "运行" button
    4. Monitor execution progress
    5. Stream progress updates as SSE events
    6. Return final results when complete
    """
    
    try:
        logger.info(f"Starting execution for workflow: {workflow_id}")
        
        # Generate execution ID
        import uuid
        execution_id = str(uuid.uuid4())
        
        # Acquire concurrency limit
        if not await limiter.acquire(execution_id):
            raise HTTPException(
                status_code=503,
                detail="Server is at maximum capacity. Please try again later."
            )
        
        # Create workflow handler
        workflow_handler = WorkflowHandler(browser_context)
        
        async def generate_progress_events():
            """Generate SSE events for workflow execution progress."""
            try:
                # Start workflow execution
                async for progress in workflow_handler.execute_workflow(
                    workflow_id=workflow_id,
                    execution_id=execution_id,
                    node_info_list=execution_request.node_info_list,
                    execution_options=execution_request.execution_options
                ):
                    # Yield progress as SSE event
                    yield {
                        "event": "progress",
                        "data": progress.json()
                    }
                    
                    # If execution is complete, break
                    if progress.status in ["completed", "failed", "cancelled"]:
                        break
                
                logger.info(f"Workflow execution completed: {execution_id}")
                
            except Exception as e:
                logger.error(f"Error during workflow execution {execution_id}: {e}")
                
                # Send error event
                error_progress = WorkflowProgress(
                    execution_id=execution_id,
                    workflow_id=workflow_id,
                    status="failed",
                    progress_percent=0.0,
                    message=f"Execution failed: {str(e)}",
                    started_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                yield {
                    "event": "error",
                    "data": error_progress.json()
                }
            
            finally:
                # Release concurrency limit
                limiter.release(execution_id)
        
        # Return SSE stream
        return EventSourceResponse(generate_progress_events())
    
    except Exception as e:
        logger.error(f"Error starting workflow execution {workflow_id}: {e}")
        # Make sure to release the limiter if we acquired it
        if 'execution_id' in locals():
            limiter.release(execution_id)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start workflow execution: {str(e)}"
        )


@router.get("/{workflow_id}/executions/{execution_id}/status", response_model=WorkflowProgress)
async def get_execution_status(
    workflow_id: str,
    execution_id: str,
    session_info: dict = Depends(require_authentication),
    session_manager: SessionManager = Depends(get_session_manager),
    logger = Depends(get_logger_dependency)
):
    """
    Get current status of workflow execution.
    
    Returns the current progress and status of a specific execution.
    """
    
    try:
        # Get execution status from session manager
        execution_status = await session_manager.get_execution_status(execution_id)
        
        if not execution_status:
            raise HTTPException(
                status_code=404,
                detail=f"Execution {execution_id} not found"
            )
        
        return WorkflowProgress(**execution_status)
    
    except Exception as e:
        logger.error(f"Error getting execution status {execution_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get execution status: {str(e)}"
        )


@router.delete("/{workflow_id}/executions/{execution_id}")
async def cancel_execution(
    workflow_id: str,
    execution_id: str,
    session_info: dict = Depends(require_authentication),
    limiter = Depends(get_concurrency_limiter),
    logger = Depends(get_logger_dependency)
):
    """
    Cancel running workflow execution.
    """
    
    try:
        logger.info(f"Cancelling execution: {execution_id}")
        
        # Cancel execution
        cancelled = await limiter.cancel_execution(execution_id)
        
        if not cancelled:
            raise HTTPException(
                status_code=404,
                detail=f"Execution {execution_id} not found or already completed"
            )
        
        return {"message": f"Execution {execution_id} cancelled successfully"}
    
    except Exception as e:
        logger.error(f"Error cancelling execution {execution_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel execution: {str(e)}"
        )


@router.get("/history", response_model=WorkflowHistory)
async def get_workflow_history(
    page: int = 1,
    page_size: int = 20,
    workflow_id: Optional[str] = None,
    session_info: dict = Depends(require_authentication),
    session_manager: SessionManager = Depends(get_session_manager),
    logger = Depends(get_logger_dependency)
):
    """
    Get workflow execution history.
    
    Returns paginated list of workflow executions for the current user.
    """
    
    try:
        user_id = session_info.get("user_id")
        
        # Get execution history
        history = await session_manager.get_execution_history(
            user_id=user_id,
            workflow_id=workflow_id,
            page=page,
            page_size=page_size
        )
        
        return WorkflowHistory(**history)
    
    except Exception as e:
        logger.error(f"Error getting workflow history: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get workflow history: {str(e)}"
        )
