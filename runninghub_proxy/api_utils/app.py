"""FastAPI application setup and configuration."""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ..config.settings import settings
from ..models.exceptions import RunningHubProxyException
from ..logging_utils.setup import setup_logging, get_logger
from .dependencies import get_browser_manager, get_session_manager
from .routes import auth_router, workflow_router, websocket_router


# Global state for application lifecycle
app_state: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger = get_logger(__name__)
    
    try:
        # Startup
        logger.info("Starting RunningHub Proxy API...")
        
        # Initialize logging
        setup_logging()
        
        # Initialize browser manager
        browser_manager = await get_browser_manager()
        app_state["browser_manager"] = browser_manager
        
        # Initialize session manager
        session_manager = get_session_manager()
        app_state["session_manager"] = session_manager
        
        # Create necessary directories
        import os
        os.makedirs(settings.temp_dir, exist_ok=True)
        os.makedirs(settings.downloads_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        logger.info("RunningHub Proxy API started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down RunningHub Proxy API...")
        
        # Cleanup browser manager
        if "browser_manager" in app_state:
            await app_state["browser_manager"].cleanup()
        
        # Cleanup session manager
        if "session_manager" in app_state:
            await app_state["session_manager"].cleanup()
        
        logger.info("RunningHub Proxy API shutdown complete")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title=settings.api_title,
        version=settings.api_version,
        description=settings.api_description,
        lifespan=lifespan,
        # docs_url="/docs" if settings.debug else None,
        # redoc_url="/redoc" if settings.debug else None,
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Add custom middleware
    @app.middleware("http")
    async def logging_middleware(request: Request, call_next):
        """Log HTTP requests and responses."""
        logger = get_logger(__name__)
        
        start_time = asyncio.get_event_loop().time()
        
        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = asyncio.get_event_loop().time() - start_time
            
            # Log response
            logger.info(
                f"Response: {response.status_code} "
                f"({process_time:.3f}s)"
            )
            
            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Request failed: {e} ({process_time:.3f}s)")
            raise
    
    # Add exception handlers
    @app.exception_handler(RunningHubProxyException)
    async def proxy_exception_handler(request: Request, exc: RunningHubProxyException):
        """Handle custom proxy exceptions."""
        logger = get_logger(__name__)
        logger.error(f"Proxy exception: {exc.message} - {exc.details}")
        
        return JSONResponse(
            status_code=500,
            content={
                "error": exc.error_code or "PROXY_ERROR",
                "message": exc.message,
                "details": exc.details,
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger = get_logger(__name__)
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "INTERNAL_SERVER_ERROR",
                "message": "An internal server error occurred",
                "details": {"type": type(exc).__name__} if settings.debug else {},
            }
        )
    
    # Include routers
    app.include_router(auth_router, prefix="/auth", tags=["Authentication"])
    app.include_router(workflow_router, prefix="/workflows", tags=["Workflows"])
    app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "version": settings.api_version,
            "timestamp": asyncio.get_event_loop().time(),
        }
    
    return app


# Create the application instance
app = create_app()


def run_server():
    """Run the FastAPI server."""
    uvicorn.run(
        "runninghub_proxy.api_utils.app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    run_server()
