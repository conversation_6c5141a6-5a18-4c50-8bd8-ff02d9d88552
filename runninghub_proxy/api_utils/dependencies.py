"""FastAPI dependencies for shared resources."""

import async<PERSON>
from typing import Op<PERSON>, Dict, Any
from fastapi import Depends, HTT<PERSON>Ex<PERSON>, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..config.settings import settings
from ..models.exceptions import <PERSON>thenti<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SessionExpiredError
from ..browser_utils.browser_manager import BrowserManager
from ..browser_utils.session_manager import SessionManager
from ..logging_utils.setup import get_logger


# Global instances
_browser_manager: Optional[BrowserManager] = None
_session_manager: Optional[SessionManager] = None

# Security scheme
security = HTTPBearer(auto_error=False)


async def get_browser_manager() -> BrowserManager:
    """Get or create browser manager instance."""
    global _browser_manager
    
    if _browser_manager is None:
        _browser_manager = BrowserManager()
        await _browser_manager.initialize()
    
    return _browser_manager


def get_session_manager() -> SessionManager:
    """Get or create session manager instance."""
    global _session_manager
    
    if _session_manager is None:
        _session_manager = SessionManager()
    
    return _session_manager


async def get_logger_dependency():
    """Get logger instance for dependency injection."""
    return get_logger(__name__)


async def verify_api_key(
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(security),
    x_api_key: Optional[str] = Header(None)
) -> Optional[str]:
    """Verify API key from Authorization header or X-API-Key header."""
    
    # Check if API key is configured
    if not settings.api_key:
        return None  # No API key required
    
    # Extract API key from headers
    api_key = None
    if authorization:
        api_key = authorization.credentials
    elif x_api_key:
        api_key = x_api_key
    
    # Verify API key
    if not api_key or api_key != settings.api_key:
        raise AuthenticationRequiredError("Invalid or missing API key")
    
    return api_key


async def get_current_session(
    session_manager: SessionManager = Depends(get_session_manager),
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """Get current session information."""
    
    if not authorization:
        return None
    
    session_token = authorization.credentials
    session_info = await session_manager.get_session(session_token)
    
    if not session_info:
        return None
    
    # Check if session is expired
    if not await session_manager.is_session_valid(session_token):
        raise SessionExpiredError("Session has expired")
    
    return session_info


async def require_authentication(
    session_info: Optional[Dict[str, Any]] = Depends(get_current_session)
) -> Dict[str, Any]:
    """Require valid authentication session."""
    
    if not session_info:
        raise AuthenticationRequiredError("Authentication required")
    
    return session_info


async def get_browser_context(
    browser_manager: BrowserManager = Depends(get_browser_manager),
    session_info: Optional[Dict[str, Any]] = Depends(get_current_session)
):
    """Get browser context for the current session."""
    
    if not session_info:
        # Create anonymous browser context
        return await browser_manager.create_context()
    
    # Get or create session-specific browser context
    session_token = session_info.get("session_token")
    return await browser_manager.get_or_create_session_context(session_token)


class ConcurrencyLimiter:
    """Concurrency limiter for workflow executions."""
    
    def __init__(self, max_concurrent: int = 5):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_executions: Dict[str, asyncio.Task] = {}
    
    async def acquire(self, execution_id: str) -> bool:
        """Acquire semaphore for execution."""
        try:
            await self.semaphore.acquire()
            return True
        except Exception:
            return False
    
    def release(self, execution_id: str):
        """Release semaphore for execution."""
        if execution_id in self.active_executions:
            del self.active_executions[execution_id]
        self.semaphore.release()
    
    def add_execution(self, execution_id: str, task: asyncio.Task):
        """Add active execution task."""
        self.active_executions[execution_id] = task
    
    def get_active_count(self) -> int:
        """Get number of active executions."""
        return len(self.active_executions)
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel specific execution."""
        if execution_id in self.active_executions:
            task = self.active_executions[execution_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            self.release(execution_id)
            return True
        return False


# Global concurrency limiter
_concurrency_limiter: Optional[ConcurrencyLimiter] = None


def get_concurrency_limiter() -> ConcurrencyLimiter:
    """Get or create concurrency limiter instance."""
    global _concurrency_limiter
    
    if _concurrency_limiter is None:
        _concurrency_limiter = ConcurrencyLimiter(settings.max_concurrent_workflows)
    
    return _concurrency_limiter


async def check_concurrency_limit(
    limiter: ConcurrencyLimiter = Depends(get_concurrency_limiter)
) -> ConcurrencyLimiter:
    """Check if concurrency limit allows new execution."""
    from ..models.exceptions import ConcurrencyLimitError
    
    if limiter.get_active_count() >= settings.max_concurrent_workflows:
        raise ConcurrencyLimitError(settings.max_concurrent_workflows)
    
    return limiter


# Request context for tracking
class RequestContext:
    """Request context for tracking and logging."""
    
    def __init__(self):
        self.request_id: Optional[str] = None
        self.user_id: Optional[str] = None
        self.session_token: Optional[str] = None
        self.start_time: Optional[float] = None
        self.metadata: Dict[str, Any] = {}
    
    def set_request_id(self, request_id: str):
        """Set request ID."""
        self.request_id = request_id
    
    def set_user_info(self, session_info: Dict[str, Any]):
        """Set user information from session."""
        self.user_id = session_info.get("user_id")
        self.session_token = session_info.get("session_token")
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to context."""
        self.metadata[key] = value


async def get_request_context() -> RequestContext:
    """Get request context for current request."""
    return RequestContext()
