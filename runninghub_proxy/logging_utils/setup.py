"""Logging setup and configuration for RunningHub Proxy API."""

import sys
import logging
from pathlib import Path
from typing import Optional
from loguru import logger

from ..config.settings import settings


class InterceptHandler(logging.Handler):
    """Intercept standard logging and redirect to loguru."""
    
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging():
    """Setup logging configuration."""
    
    # Remove default loguru handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # Add file handler if log file is specified
    if settings.log_file:
        log_path = Path(settings.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            settings.log_file,
            level=settings.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message}",
            rotation=settings.log_rotation,
            retention=settings.log_retention,
            compression="gz",
            backtrace=True,
            diagnose=True,
        )
    
    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # Set specific loggers
    for logger_name in ["uvicorn", "uvicorn.access", "fastapi"]:
        logging_logger = logging.getLogger(logger_name)
        logging_logger.handlers = [InterceptHandler()]
        logging_logger.setLevel(logging.INFO)
    
    # Reduce noise from some loggers
    logging.getLogger("playwright").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    logger.info("Logging setup complete")


def get_logger(name: Optional[str] = None):
    """Get logger instance."""
    if name:
        return logger.bind(name=name)
    return logger


class LogContext:
    """Context manager for adding context to logs."""
    
    def __init__(self, **context):
        self.context = context
        self.token = None
    
    def __enter__(self):
        self.token = logger.contextualize(**self.context)
        return self.token
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.token:
            self.token.__exit__(exc_type, exc_val, exc_tb)


def log_function_call(func_name: str, args: tuple = (), kwargs: dict = None):
    """Log function call with parameters."""
    kwargs = kwargs or {}
    
    # Sanitize sensitive data
    sanitized_kwargs = {}
    sensitive_keys = ["password", "token", "key", "secret", "auth"]
    
    for key, value in kwargs.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized_kwargs[key] = "***REDACTED***"
        else:
            sanitized_kwargs[key] = value
    
    logger.debug(f"Calling {func_name} with args={args}, kwargs={sanitized_kwargs}")


def log_execution_time(func_name: str, execution_time: float):
    """Log function execution time."""
    logger.info(f"{func_name} completed in {execution_time:.3f}s")


class WebSocketLogHandler:
    """Handler for streaming logs via WebSocket."""
    
    def __init__(self, websocket_manager=None):
        self.websocket_manager = websocket_manager
        self.enabled = websocket_manager is not None
    
    def emit(self, record):
        """Emit log record via WebSocket."""
        if not self.enabled or not self.websocket_manager:
            return
        
        try:
            # Format log message
            log_data = {
                "timestamp": record["time"].isoformat(),
                "level": record["level"].name,
                "logger": record["name"],
                "message": record["message"],
                "function": record["function"],
                "line": record["line"],
            }
            
            # Add exception info if present
            if record["exception"]:
                log_data["exception"] = str(record["exception"])
            
            # Broadcast to all WebSocket connections
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(
                    self.websocket_manager.broadcast_to_all({
                        "event": "log",
                        "data": log_data
                    })
                )
            except RuntimeError:
                # No event loop running
                pass
                
        except Exception as e:
            # Don't let logging errors break the application
            print(f"Error in WebSocket log handler: {e}")


def setup_websocket_logging(websocket_manager):
    """Setup WebSocket log streaming."""
    websocket_handler = WebSocketLogHandler(websocket_manager)
    
    # Add WebSocket handler to loguru
    logger.add(
        websocket_handler.emit,
        level="INFO",
        format="{message}",
        filter=lambda record: record["level"].no >= logger.level("INFO").no
    )
    
    logger.info("WebSocket logging setup complete")


# Performance logging utilities
class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.logger = get_logger("performance")
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        self.logger.debug(f"Starting {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            import time
            execution_time = time.time() - self.start_time
            
            if exc_type:
                self.logger.warning(
                    f"{self.operation_name} failed after {execution_time:.3f}s: {exc_val}"
                )
            else:
                self.logger.info(f"{self.operation_name} completed in {execution_time:.3f}s")
    
    def checkpoint(self, checkpoint_name: str):
        """Log a checkpoint during operation."""
        if self.start_time:
            import time
            elapsed = time.time() - self.start_time
            self.logger.debug(f"{self.operation_name} - {checkpoint_name}: {elapsed:.3f}s")


def performance_log(operation_name: str):
    """Decorator for logging function performance."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceLogger(f"{func.__name__}({operation_name})"):
                return func(*args, **kwargs)
        return wrapper
    return decorator


async def async_performance_log(operation_name: str):
    """Decorator for logging async function performance."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            with PerformanceLogger(f"{func.__name__}({operation_name})"):
                return await func(*args, **kwargs)
        return wrapper
    return decorator
