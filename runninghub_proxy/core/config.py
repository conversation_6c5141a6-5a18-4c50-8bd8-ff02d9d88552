from pydantic import BaseSettings, Field, validator
from typing import Optional

class Settings(BaseSettings):
    app_name: str = Field("RunningHub Proxy", env="APP_NAME")
    debug: bool = Field(False, env="DEBUG")
    secret_key: str = Field("your-secret-key", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    allowed_hosts: Optional[str] = Field(None, env="ALLOWED_HOSTS")
    log_level: str = Field("info", env="LOG_LEVEL")

    # Database settings (optional, for future use)
    db_url: Optional[str] = Field(None, env="DATABASE_URL")

    @validator('debug', pre=True)
    def parse_debug(cls, v):
        if isinstance(v, str):
            # Return False for any string that's not explicitly a boolean true value
            return v.lower() in ('true', '1', 'yes', 'on')
        return bool(v)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
