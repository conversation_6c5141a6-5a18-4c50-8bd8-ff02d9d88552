#!/usr/bin/env python3
"""Verify RunningHub Proxy API installation."""

import sys
import subprocess
import importlib
from pathlib import Path


def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version >= (3, 9):
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python 3.9+ required, found {version.major}.{version.minor}.{version.micro}")
        return False


def check_uv_installation():
    """Check if uv is installed."""
    print("\n📦 Checking uv installation...")
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ uv {result.stdout.strip()}")
            return True
        else:
            print("❌ uv not working properly")
            return False
    except FileNotFoundError:
        print("❌ uv not found")
        print("Install with: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False


def check_project_structure():
    """Check project structure."""
    print("\n📁 Checking project structure...")
    
    required_files = [
        "pyproject.toml",
        "server.py",
        ".env.example",
        "runninghub_proxy/__init__.py",
        "runninghub_proxy/config/settings.py",
        "runninghub_proxy/api_utils/app.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required files present")
        return True


def check_dependencies():
    """Check if dependencies are installed."""
    print("\n📚 Checking dependencies...")
    
    # Check if virtual environment exists
    venv_path = Path(".venv")
    if not venv_path.exists():
        print("❌ Virtual environment not found (.venv)")
        print("Run: uv sync")
        return False
    
    print("✅ Virtual environment found")
    
    # Test key imports
    test_imports = [
        ("fastapi", "FastAPI framework"),
        ("playwright", "Playwright browser automation"),
        ("pydantic", "Pydantic data validation"),
        ("uvicorn", "Uvicorn ASGI server"),
        ("loguru", "Loguru logging"),
    ]
    
    failed_imports = []
    for module, description in test_imports:
        try:
            result = subprocess.run(
                ["uv", "run", "python", "-c", f"import {module}"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - {result.stderr.strip()}")
                failed_imports.append(module)
        except Exception as e:
            print(f"❌ {description} - {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0


def check_runninghub_proxy_import():
    """Check if runninghub_proxy module can be imported."""
    print("\n🔧 Checking runninghub_proxy module...")
    
    try:
        result = subprocess.run(
            ["uv", "run", "python", "-c", "import runninghub_proxy; print('Import successful')"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ runninghub_proxy module import successful")
            return True
        else:
            print(f"❌ runninghub_proxy import failed: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Error testing import: {e}")
        return False


def check_configuration():
    """Check configuration loading."""
    print("\n⚙️  Checking configuration...")
    
    try:
        result = subprocess.run(
            ["uv", "run", "python", "-c", "from runninghub_proxy.config.settings import settings; print(f'Host: {settings.host}, Port: {settings.port}')"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print(f"✅ Configuration loaded: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Configuration loading failed: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False


def check_playwright():
    """Check Playwright installation."""
    print("\n🎭 Checking Playwright...")
    
    try:
        # Check if Playwright can be imported
        result = subprocess.run(
            ["uv", "run", "python", "-c", "from playwright.async_api import async_playwright; print('Playwright import successful')"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ Playwright import successful")
        else:
            print(f"❌ Playwright import failed: {result.stderr.strip()}")
            return False
        
        # Check if browsers are installed
        result = subprocess.run(
            ["uv", "run", "playwright", "install", "--dry-run", "chromium"],
            capture_output=True,
            text=True
        )
        if "is already installed" in result.stdout or result.returncode == 0:
            print("✅ Playwright browsers available")
            return True
        else:
            print("⚠️  Playwright browsers may not be installed")
            print("Run: uv run playwright install chromium")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Playwright: {e}")
        return False


def check_environment_file():
    """Check environment configuration."""
    print("\n🌍 Checking environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file exists")
        return True
    elif env_example.exists():
        print("⚠️  .env file missing, but .env.example exists")
        print("Run: cp .env.example .env")
        return False
    else:
        print("❌ No environment configuration files found")
        return False


def check_directories():
    """Check required directories."""
    print("\n📂 Checking directories...")
    
    required_dirs = ["logs", "temp", "downloads"]
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ {dir_name}/ directory exists")
        else:
            print(f"⚠️  {dir_name}/ directory missing")
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"Run: mkdir -p {' '.join(missing_dirs)}")
        return False
    
    return True


def main():
    """Main verification function."""
    print("🔍 RunningHub Proxy API - Installation Verification")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("UV Installation", check_uv_installation),
        ("Project Structure", check_project_structure),
        ("Dependencies", check_dependencies),
        ("Module Import", check_runninghub_proxy_import),
        ("Configuration", check_configuration),
        ("Playwright", check_playwright),
        ("Environment File", check_environment_file),
        ("Directories", check_directories),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Verification Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (check_name, _) in enumerate(checks):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{check_name}: {status}")
    
    print(f"\nPassed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All checks passed! Installation is complete.")
        print("\n🚀 Ready to start:")
        print("   uv run python server.py")
        print("   Then visit: http://localhost:8000/docs")
    else:
        print(f"\n⚠️  {total - passed} checks failed. Please fix the issues above.")
        print("\n🔧 Common fixes:")
        print("   uv sync                          # Install dependencies")
        print("   uv run playwright install chromium  # Install browsers")
        print("   cp .env.example .env             # Create environment file")
        print("   mkdir -p logs temp downloads     # Create directories")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
