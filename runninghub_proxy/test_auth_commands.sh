#!/bin/bash
# Test authentication commands with proper API key

echo "🔐 RunningHub Proxy API - Authentication Test Commands"
echo "======================================================"
echo "API Key: kala (from .env file)"
echo "Server: http://localhost:8973"
echo ""

echo "🏥 1. Health Check (no auth required)"
echo "curl -s http://localhost:8973/health | jq"
curl -s http://localhost:8973/health | jq
echo ""

echo "🔍 2. Auth Status (no auth required)"
echo "curl -s http://localhost:8973/auth/status | jq"
curl -s http://localhost:8973/auth/status | jq
echo ""

echo "🔑 3. WeChat Login with API Key (Bearer Token)"
echo "curl -X POST -H \"Authorization: Bearer kala\" -H \"Content-Type: application/json\" -d '{\"method\": \"wechat\", \"timeout\": 300}' http://localhost:8973/auth/login | jq"
curl -X POST -H "Authorization: Bearer kala" -H "Content-Type: application/json" -d '{"method": "wechat", "timeout": 300}' http://localhost:8973/auth/login | jq
echo ""

echo "📱 4. Phone SMS Login with API Key (X-API-Key Header)"
echo "curl -X POST -H \"X-API-Key: kala\" -H \"Content-Type: application/json\" -d '{\"method\": \"phone_sms\", \"phone\": \"13800138000\", \"timeout\": 300}' http://localhost:8973/auth/login | jq"
curl -X POST -H "X-API-Key: kala" -H "Content-Type: application/json" -d '{"method": "phone_sms", "phone": "13800138000", "timeout": 300}' http://localhost:8973/auth/login | jq
echo ""

echo "🚫 5. Login without API Key (should return 401)"
echo "curl -X POST -H \"Content-Type: application/json\" -d '{\"method\": \"wechat\", \"timeout\": 300}' http://localhost:8973/auth/login | jq"
curl -X POST -H "Content-Type: application/json" -d '{"method": "wechat", "timeout": 300}' http://localhost:8973/auth/login | jq
echo ""

echo "======================================================"
echo "🎯 IMPORTANT NOTES:"
echo "1. ✅ BROWSER_HEADLESS=false is now set in .env"
echo "2. 🔄 RESTART THE SERVER to see the browser window"
echo "3. 🔑 API key 'kala' is required for authentication"
echo "4. 📱 WeChat login will show QR code in browser"
echo "5. 📞 Phone SMS login will show input fields"
echo ""
echo "🔄 To restart server:"
echo "   Stop current server (Ctrl+C)"
echo "   Run: uv run python server.py"
echo "   Then run this script again"
