#!/usr/bin/env python3
"""Setup script for RunningHub Proxy API."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, description):
    """Run a shell command with error handling."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def check_uv():
    """Check if uv is available."""
    if shutil.which("uv"):
        print("✅ uv detected")
        return True
    print("⚠️  uv not found, will use pip instead")
    return False


def setup_with_uv():
    """Setup project using uv."""
    print("📦 Setting up with uv (fast Python package manager)...")

    commands = [
        ("uv sync", "Installing dependencies with uv"),
        ("uv run playwright install chromium", "Installing Playwright browsers"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            return False

    return True


def setup_with_pip():
    """Setup project using pip (fallback)."""
    print("📦 Setting up with pip (fallback)...")

    commands = [
        ("pip install -e .", "Installing project with pip"),
        ("playwright install chromium", "Installing Playwright browsers"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            return False

    return True


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "temp", 
        "downloads",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def create_env_file():
    """Create .env file from example if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from example...")
        shutil.copy(env_example, env_file)
        print("✅ .env file created")
        print("⚠️  Please edit .env file to configure your settings")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")
    
    return True


def run_tests():
    """Run basic tests to verify installation."""
    print("🧪 Running basic tests...")

    if shutil.which("uv"):
        command = "uv run python -m pytest tests/test_basic.py -v"
    else:
        command = "python -m pytest tests/test_basic.py -v"

    return run_command(command, "Running tests")


def main():
    """Main setup function."""
    print("🚀 RunningHub Proxy API Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        sys.exit(1)
    
    # Install dependencies
    use_uv = check_uv()

    if use_uv:
        success = setup_with_uv()
    else:
        success = setup_with_pip()

    if not success:
        print("❌ Setup failed during dependency installation")
        sys.exit(1)

    # Run tests (optional)
    print("\n🧪 Would you like to run basic tests? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            run_tests()
    except KeyboardInterrupt:
        print("\n⏭️  Skipping tests")

    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file to configure your settings")
    print("2. Run the server:")

    if use_uv:
        print("   uv run python server.py")
    else:
        print("   python server.py")

    print("3. Visit http://localhost:8000/docs for API documentation")
    print("\n📚 For more information, see README.md")
    print("\n💡 Tip: Use 'uv' for faster package management!")
    print("   Install uv: curl -LsSf https://astral.sh/uv/install.sh | sh")


if __name__ == "__main__":
    main()
