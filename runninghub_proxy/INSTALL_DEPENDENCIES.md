# Install Dependencies for RunningHub Proxy

## Quick Installation Commands

### Option 1: Using uv (Recommended - Fastest)

```bash
# 1. Install uv if you don't have it
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. Reload your shell
source ~/.bashrc  # or restart terminal

# 3. Navigate to project directory
cd runninghub_proxy

# 4. Install dependencies
uv sync

# 5. Install Playwright browsers
uv run playwright install chromium

# 6. Create necessary directories
mkdir -p logs temp downloads

# 7. Setup environment
cp .env.example .env

# 8. Test installation
uv run python -c "import runninghub_proxy; print('✅ Success!')"

# 9. Start the server
uv run python server.py
```

### Option 2: Using pip (Fallback)

```bash
# 1. Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 2. Install project in editable mode
pip install -e .

# 3. Install Playwright browsers
playwright install chromium

# 4. Create directories and setup
mkdir -p logs temp downloads
cp .env.example .env

# 5. Test installation
python -c "import runninghub_proxy; print('✅ Success!')"

# 6. Start the server
python server.py
```

### Option 3: Using the automated script

```bash
# Run the installation script
python install_with_uv.py
```

## Troubleshooting Common Issues

### Issue 1: uv not found
```bash
# Install uv manually
curl -LsSf https://astral.sh/uv/install.sh | sh

# Add to PATH (add this to ~/.bashrc or ~/.zshrc)
export PATH="$HOME/.cargo/bin:$PATH"

# Reload shell
source ~/.bashrc
```

### Issue 2: Permission denied
```bash
# Make scripts executable
chmod +x install_with_uv.py
chmod +x setup.py

# Or run with python
python install_with_uv.py
```

### Issue 3: Playwright installation fails
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2

# For macOS with Homebrew
brew install --cask playwright

# Then retry
uv run playwright install chromium
```

### Issue 4: Import errors
```bash
# Make sure you're in the right directory
cd runninghub_proxy

# Check if __init__.py files exist
ls -la */__init__.py

# Reinstall in development mode
uv sync --reinstall
```

### Issue 5: Port already in use
```bash
# Find what's using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or change port in .env
echo "PORT=8001" >> .env
```

## Verification Steps

After installation, verify everything works:

```bash
# 1. Check uv version
uv --version

# 2. Check Python can import the module
uv run python -c "import runninghub_proxy; print('Module import: ✅')"

# 3. Check configuration loads
uv run python -c "from runninghub_proxy.config.settings import settings; print('Settings: ✅')"

# 4. Check Playwright works
uv run python -c "from playwright.async_api import async_playwright; print('Playwright: ✅')"

# 5. Start server (should start without errors)
uv run python server.py &
sleep 5

# 6. Test health endpoint
curl http://localhost:8000/health

# 7. Stop server
pkill -f "python server.py"
```

## Development Commands

Once installed, use these commands for development:

```bash
# Start development server
uv run python server.py

# Run tests
uv run pytest tests/

# Run stability tests
uv run python test_stability.py

# Format code
uv run black .

# Lint code
uv run ruff check .

# Type check
uv run mypy runninghub_proxy/

# Or use Makefile shortcuts
make install  # Install dependencies
make run      # Start server
make test     # Run tests
make check    # Run all quality checks
```

## Environment Configuration

Edit the `.env` file to configure the application:

```bash
# Copy example and edit
cp .env.example .env
nano .env  # or use your preferred editor
```

Key settings to configure:
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Debug mode (default: false)
- `API_KEY`: Optional API key for authentication
- `BROWSER_HEADLESS`: Run browser in headless mode (default: true)

## Next Steps

1. **Configure settings**: Edit `.env` file
2. **Start server**: `uv run python server.py`
3. **View API docs**: Visit `http://localhost:8000/docs`
4. **Run tests**: `uv run pytest tests/`
5. **Read documentation**: Check `docs/` directory

## Getting Help

If you encounter issues:

1. Check the installation logs
2. Run `uv run python test_stability.py` for diagnostics
3. Check `logs/runninghub_proxy.log` for application logs
4. Refer to `docs/DEPLOYMENT.md` for detailed setup instructions
5. Use `make status` to check project status
