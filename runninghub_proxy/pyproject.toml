[project]
name = "runninghub-proxy"
version = "0.1.0"
description = "A web automation proxy that converts RunningHub.cn's workflow interface into a programmatic REST API"
authors = [
    {name = "RunningHub Proxy Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["automation", "proxy", "api", "workflow", "runninghub", "playwright"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Networking",
]

dependencies = [
    # Core Framework
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",

    # Browser Automation
    "playwright>=1.40.0",

    # Data Validation & Settings
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",

    # HTTP & WebSocket
    "httpx>=0.25.2",
    "websockets>=12.0",
    "python-multipart>=0.0.6",
    "sse-starlette>=1.6.5",

    # File Handling
    "aiofiles>=23.2.1",

    # Environment & Configuration
    "python-dotenv>=1.0.0",

    # Logging
    "loguru>=0.7.2",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-playwright>=0.4.3",
    "pytest-cov>=4.1.0",

    # Code Quality
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "ruff>=0.1.6",

    # Development Tools
    "pre-commit>=3.5.0",
    "ipython>=8.17.2",
]

test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-playwright>=0.4.3",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.2",
]

docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocstrings[python]>=0.24.0",
]

[project.urls]
Homepage = "https://github.com/runninghub-proxy/runninghub-proxy"
Documentation = "https://runninghub-proxy.readthedocs.io"
Repository = "https://github.com/runninghub-proxy/runninghub-proxy"
Issues = "https://github.com/runninghub-proxy/runninghub-proxy/issues"

[project.scripts]
runninghub-proxy = "runninghub_proxy.server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["runninghub_proxy"]

[tool.hatch.build.targets.sdist]
include = [
    "/runninghub_proxy",
    "/docs",
    "/tests",
    "README.md",
    "LICENSE*",
]

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["runninghub_proxy"]
known_third_party = ["fastapi", "playwright", "pydantic", "uvicorn"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_any_generics = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[tool.ruff]
target-version = "py39"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["runninghub_proxy"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
