# RunningHub Proxy API - API Reference

## Table of Contents
- [Authentication](#authentication)
- [Workflow Operations](#workflow-operations)
- [WebSocket Communication](#websocket-communication)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Examples](#examples)

## Base URL
```
Production: https://your-domain.com
Development: http://localhost:8000
```

## Authentication

### POST /auth/login
Initiate authentication flow with RunningHub.cn.

**Request Body:**
```json
{
  "method": "phone_sms|wechat|password",
  "phone": "13800138000",           // Required for phone_sms
  "sms_code": "123456",             // Optional for phone_sms
  "username": "<EMAIL>",   // Required for password
  "password": "password123",        // Required for password
  "timeout": 300                    // Optional, default 300 seconds
}
```

**Response:**
```json
{
  "status": "authenticated|authenticating|failed",
  "session_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-01-01T12:00:00Z",
  "user_info": {
    "user_id": "user123",
    "username": "testuser"
  },
  "message": "Authentication successful",
  "requires_manual_action": false,
  "manual_action_type": null,
  "qr_code_url": null
}
```

**Manual Action Response (WeChat):**
```json
{
  "status": "authenticating",
  "message": "Please scan the WeChat QR code to complete authentication",
  "requires_manual_action": true,
  "manual_action_type": "qr_scan",
  "qr_code_url": "/temp/wechat_qr_1234567890.png"
}
```

### GET /auth/status
Check current authentication status.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Response:**
```json
{
  "is_authenticated": true,
  "session_valid": true,
  "user_info": {
    "user_id": "user123",
    "username": "testuser"
  },
  "session_expires_at": "2024-01-01T12:00:00Z",
  "time_until_expiry": 3600
}
```

### POST /auth/logout
Terminate current session.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "logout_all_sessions": false  // Optional, default false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Logout successful",
  "sessions_cleared": 1
}
```

## Workflow Operations

### GET /workflows/{workflow_id}/schema
Extract workflow schema from RunningHub.cn.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Parameters:**
- `workflow_id` (path): RunningHub workflow identifier

**Response:**
```json
{
  "workflow_id": "1938718769584001025",
  "title": "时尚杂志封面替换-Flux Redux+Pulid",
  "description": "Fashion magazine cover replacement workflow",
  "author": "workflow_author",
  "tags": ["fashion", "magazine", "flux"],
  "nodeInfo_list": [
    {
      "node_id": "1",
      "field_name": "prompt",
      "field_value": "[value placeholder]",
      "class_type": "TextInput",
      "_meta": {
        "title": "Text Input Node",
        "original_value": "default prompt"
      }
    }
  ],
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T10:00:00Z"
}
```

### POST /workflows/{workflow_id}/execute
Execute workflow with streaming progress updates.

**Headers:**
```
Authorization: Bearer <session_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "nodeInfo_list": [
    {
      "node_id": "1",
      "field_name": "prompt",
      "field_value": "A beautiful fashion model on magazine cover",
      "class_type": "TextInput"
    },
    {
      "node_id": "2", 
      "field_name": "image_url",
      "field_value": "https://example.com/image.jpg",
      "class_type": "ImageInput"
    }
  ],
  "execution_options": {
    "priority": "normal",
    "callback_url": "https://your-app.com/webhook"
  }
}
```

**Response (Server-Sent Events):**
```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

event: progress
data: {"execution_id": "exec123", "workflow_id": "1938718769584001025", "status": "running", "progress_percent": 0.0, "message": "Workflow execution started", "started_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-01T10:00:00Z"}

event: progress
data: {"execution_id": "exec123", "workflow_id": "1938718769584001025", "status": "running", "progress_percent": 25.0, "message": "Processing nodes...", "started_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-01T10:01:00Z"}

event: progress
data: {"execution_id": "exec123", "workflow_id": "1938718769584001025", "status": "running", "progress_percent": 75.0, "message": "Generating output...", "started_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-01T10:02:00Z"}

event: progress
data: {"execution_id": "exec123", "workflow_id": "1938718769584001025", "status": "completed", "progress_percent": 100.0, "message": "Workflow execution completed", "started_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-01T10:03:00Z"}
```

### GET /workflows/{workflow_id}/executions/{execution_id}/status
Get current status of workflow execution.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Response:**
```json
{
  "execution_id": "exec123",
  "workflow_id": "1938718769584001025",
  "status": "running",
  "progress_percent": 45.0,
  "current_node": "node_5",
  "message": "Processing image transformation",
  "started_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T10:01:30Z",
  "estimated_completion": "2024-01-01T10:05:00Z"
}
```

### DELETE /workflows/{workflow_id}/executions/{execution_id}
Cancel running workflow execution.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Response:**
```json
{
  "message": "Execution exec123 cancelled successfully"
}
```

### GET /workflows/history
Get workflow execution history.

**Headers:**
```
Authorization: Bearer <session_token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 20)
- `workflow_id` (optional): Filter by workflow ID

**Response:**
```json
{
  "total_count": 150,
  "page": 1,
  "page_size": 20,
  "executions": [
    {
      "execution_id": "exec123",
      "workflow_id": "1938718769584001025",
      "workflow_title": "Fashion Magazine Cover",
      "status": "completed",
      "started_at": "2024-01-01T10:00:00Z",
      "completed_at": "2024-01-01T10:03:00Z",
      "execution_time": 180.5,
      "output_count": 3
    }
  ]
}
```

## WebSocket Communication

### WebSocket /ws/logs
Real-time log streaming and system events.

**Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/logs?session_token=<token>');
```

**Message Types:**

**Connection Confirmation:**
```json
{
  "event": "connected",
  "data": {
    "message": "WebSocket connected successfully",
    "authenticated": true,
    "timestamp": 1704110400.123
  }
}
```

**Log Messages:**
```json
{
  "event": "log",
  "data": {
    "timestamp": "2024-01-01T10:00:00.123Z",
    "level": "INFO",
    "logger": "workflow_handler",
    "message": "Starting workflow execution",
    "function": "execute_workflow",
    "line": 123
  }
}
```

**Authentication Status:**
```json
{
  "event": "auth_status",
  "data": {
    "status": "authenticated",
    "message": "User logged in successfully",
    "timestamp": 1704110400.123
  }
}
```

**Workflow Progress:**
```json
{
  "event": "workflow_progress",
  "data": {
    "execution_id": "exec123",
    "status": "running",
    "progress_percent": 45.0,
    "message": "Processing nodes..."
  }
}
```

**Client Messages:**
```json
// Ping
{"type": "ping"}

// Subscribe to events
{"type": "subscribe", "events": ["workflow_progress", "auth_status"]}

// Unsubscribe from events
{"type": "unsubscribe", "events": ["log"]}
```

## Error Handling

### Error Response Format
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": {
    "field": "Additional error context",
    "code": 422
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTH_REQUIRED` | 401 | Authentication required |
| `AUTH_FAILED` | 401 | Authentication failed |
| `SESSION_EXPIRED` | 401 | Session has expired |
| `WORKFLOW_NOT_FOUND` | 404 | Workflow not found |
| `EXECUTION_ERROR` | 500 | Workflow execution failed |
| `BROWSER_ERROR` | 500 | Browser automation error |
| `SCHEMA_ERROR` | 500 | Schema extraction failed |
| `CONCURRENCY_LIMIT` | 503 | Too many concurrent executions |

## Rate Limiting

### Limits
- **Authentication**: 10 requests per minute per IP
- **Schema Extraction**: 30 requests per hour per user
- **Workflow Execution**: 5 concurrent executions per user
- **WebSocket Connections**: 10 connections per user

### Rate Limit Headers
```
X-RateLimit-Limit: 30
X-RateLimit-Remaining: 25
X-RateLimit-Reset: 1704114000
Retry-After: 60
```

## Examples

### Complete Workflow Example

```python
import requests
import json
from sseclient import SSEClient

# 1. Authenticate
auth_response = requests.post('http://localhost:8000/auth/login', json={
    'method': 'phone_sms',
    'phone': '13800138000',
    'sms_code': '123456'
})
session_token = auth_response.json()['session_token']

headers = {'Authorization': f'Bearer {session_token}'}

# 2. Extract schema
schema_response = requests.get(
    'http://localhost:8000/workflows/1938718769584001025/schema',
    headers=headers
)
schema = schema_response.json()

# 3. Prepare execution request
execution_request = {
    'nodeInfo_list': [
        {
            'node_id': '1',
            'field_name': 'prompt',
            'field_value': 'A beautiful fashion model',
            'class_type': 'TextInput'
        }
    ]
}

# 4. Execute workflow with streaming
response = requests.post(
    'http://localhost:8000/workflows/1938718769584001025/execute',
    headers=headers,
    json=execution_request,
    stream=True
)

# 5. Process SSE stream
client = SSEClient(response)
for event in client.events():
    if event.event == 'progress':
        progress = json.loads(event.data)
        print(f"Progress: {progress['progress_percent']}% - {progress['message']}")
        
        if progress['status'] in ['completed', 'failed']:
            break
```

### WebSocket Example

```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/logs?session_token=<token>');

// Handle messages
ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch(message.event) {
        case 'workflow_progress':
            console.log('Progress:', message.data.progress_percent + '%');
            break;
        case 'log':
            console.log('Log:', message.data.message);
            break;
        case 'error':
            console.error('Error:', message.data.message);
            break;
    }
};

// Send ping
ws.send(JSON.stringify({type: 'ping'}));

// Subscribe to specific events
ws.send(JSON.stringify({
    type: 'subscribe',
    events: ['workflow_progress', 'auth_status']
}));
```
