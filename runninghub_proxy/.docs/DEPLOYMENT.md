# RunningHub Proxy API - Deployment Guide

## Table of Contents
- [Quick Start](#quick-start)
- [System Requirements](#system-requirements)
- [Installation Methods](#installation-methods)
- [Configuration](#configuration)
- [Production Deployment](#production-deployment)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Troubleshooting](#troubleshooting)

## Quick Start

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd runninghub_proxy

# Run automated setup
python setup.py
```

### 2. Configure Environment
```bash
# Copy and edit configuration
cp .env.example .env
nano .env  # Edit configuration values
```

### 3. Start Server
```bash
# With uv (recommended - fastest)
uv run python server.py

# With pip (fallback)
python server.py
```

### 4. Verify Installation
```bash
# Check health endpoint
curl http://localhost:8000/health

# View API documentation
open http://localhost:8000/docs
```

## System Requirements

### Minimum Requirements
- **Python**: 3.9 or higher
- **Memory**: 2GB RAM
- **Storage**: 1GB free space
- **Network**: Internet access for RunningHub.cn

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 4GB RAM
- **Storage**: 5GB free space (for logs and temp files)
- **CPU**: 2+ cores for concurrent workflows

### Operating System Support
- **Linux**: Ubuntu 20.04+, CentOS 8+, Debian 11+
- **macOS**: 10.15+ (Catalina)
- **Windows**: 10/11 with WSL2 (recommended)

### Browser Requirements
- **Chromium**: Automatically installed via Playwright
- **Display**: Xvfb for headless Linux environments

## Installation Methods

### Method 1: Automated Setup (Recommended)

```bash
# Clone repository
git clone <repository-url>
cd runninghub_proxy

# Run setup script
python setup.py
```

The setup script will:
- Check Python version compatibility
- Install dependencies (Poetry or pip)
- Install Playwright browsers
- Create necessary directories
- Generate .env configuration file
- Run basic tests

### Method 2: Manual Installation

#### Using uv (Recommended - Fastest)
```bash
# Install uv if not available
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies and create virtual environment
uv sync

# Install Playwright browsers
uv run playwright install chromium

# Create directories
mkdir -p logs temp downloads
```

#### Using pip (Fallback)
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# Install project in development mode
pip install -e .

# Install Playwright browsers
playwright install chromium

# Create directories
mkdir -p logs temp downloads
```

### Method 3: Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.11-slim

# Install system dependencies and uv
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && curl -LsSf https://astral.sh/uv/install.sh | sh

# Add uv to PATH
ENV PATH="/root/.cargo/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy project configuration
COPY pyproject.toml .
COPY README.md .

# Install dependencies with uv
RUN uv sync --frozen

# Install Playwright browsers
RUN uv run playwright install chromium
RUN uv run playwright install-deps

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs temp downloads

# Expose port
EXPOSE 8000

# Run application
CMD ["uv", "run", "python", "server.py"]
```

```bash
# Build and run Docker container
docker build -t runninghub-proxy .
docker run -p 8000:8000 -v $(pwd)/.env:/app/.env runninghub-proxy
```

## Configuration

### Environment Variables (.env)

```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# API Configuration
API_TITLE="RunningHub Proxy API"
API_VERSION="1.0.0"
API_KEY=your-secret-api-key-here  # Optional

# Authentication
SESSION_TIMEOUT=3600  # 1 hour

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000  # 30 seconds
BROWSER_USER_AGENT="Mozilla/5.0 (compatible; RunningHubProxy/1.0)"

# RunningHub Configuration
RUNNINGHUB_BASE_URL=https://www.runninghub.cn
RUNNINGHUB_LOGIN_TIMEOUT=300000  # 5 minutes

# Workflow Configuration
WORKFLOW_EXECUTION_TIMEOUT=600000  # 10 minutes
MAX_CONCURRENT_WORKFLOWS=5

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/runninghub_proxy.log
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# Storage Configuration
TEMP_DIR=temp
DOWNLOADS_DIR=downloads

# Performance Configuration
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1.0
```

### Configuration Validation

```bash
# Test configuration
python -c "from runninghub_proxy.config.settings import settings; print('✅ Configuration valid')"
```

## Production Deployment

### 1. Reverse Proxy Setup (Nginx)

```nginx
# /etc/nginx/sites-available/runninghub-proxy
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts for long-running requests
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }
}
```

### 2. SSL/TLS Configuration

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Process Management (Systemd)

```ini
# /etc/systemd/system/runninghub-proxy.service
[Unit]
Description=RunningHub Proxy API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/runninghub-proxy
Environment=PATH=/root/.cargo/bin:/opt/runninghub-proxy/.venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/root/.cargo/bin/uv run python server.py
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/runninghub-proxy/logs /opt/runninghub-proxy/temp /opt/runninghub-proxy/downloads

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable runninghub-proxy
sudo systemctl start runninghub-proxy
sudo systemctl status runninghub-proxy
```

### 4. Production Environment Variables

```bash
# Production .env
DEBUG=false
BROWSER_HEADLESS=true
LOG_LEVEL=WARNING
API_KEY=your-secure-production-api-key
SESSION_TIMEOUT=7200  # 2 hours
MAX_CONCURRENT_WORKFLOWS=10
```

### 5. Security Hardening

```bash
# Firewall configuration
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# File permissions
chmod 600 .env
chmod -R 755 logs temp downloads
chown -R www-data:www-data /opt/runninghub-proxy
```

## Monitoring & Maintenance

### 1. Health Monitoring

```bash
# Health check script
#!/bin/bash
# /opt/runninghub-proxy/scripts/health-check.sh

HEALTH_URL="http://localhost:8000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "✅ Service healthy"
    exit 0
else
    echo "❌ Service unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

### 2. Log Monitoring

```bash
# Monitor logs in real-time
tail -f logs/runninghub_proxy.log

# Search for errors
grep -i error logs/runninghub_proxy.log

# Monitor WebSocket connections
curl http://localhost:8000/ws/connections
```

### 3. Performance Monitoring

```bash
# Monitor system resources
htop
iotop
netstat -tulpn | grep :8000

# Monitor application metrics
curl http://localhost:8000/health | jq
```

### 4. Backup Strategy

```bash
# Backup script
#!/bin/bash
# /opt/runninghub-proxy/scripts/backup.sh

BACKUP_DIR="/backup/runninghub-proxy"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup configuration
cp .env $BACKUP_DIR/env_$DATE
cp -r logs $BACKUP_DIR/logs_$DATE

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -type f -mtime +30 -delete
```

### 5. Update Procedure

```bash
# Update script
#!/bin/bash
# /opt/runninghub-proxy/scripts/update.sh

# Stop service
sudo systemctl stop runninghub-proxy

# Backup current version
cp -r /opt/runninghub-proxy /backup/runninghub-proxy-$(date +%Y%m%d)

# Pull updates
git pull origin main

# Update dependencies
uv sync

# Restart service
sudo systemctl start runninghub-proxy

# Verify health
sleep 5
curl -f http://localhost:8000/health || echo "❌ Update failed"
```

## Troubleshooting

### Common Issues

#### 1. Browser Installation Issues
```bash
# Reinstall Playwright browsers
playwright install chromium --force

# Check browser installation
playwright install-deps
```

#### 2. Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x server.py setup.py
```

#### 3. Port Already in Use
```bash
# Find process using port 8000
sudo lsof -i :8000
sudo kill -9 <PID>

# Or change port in .env
echo "PORT=8001" >> .env
```

#### 4. Memory Issues
```bash
# Monitor memory usage
free -h
ps aux | grep python

# Increase swap if needed
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### Debug Mode

```bash
# Enable debug mode
echo "DEBUG=true" >> .env
echo "LOG_LEVEL=DEBUG" >> .env

# Restart service
sudo systemctl restart runninghub-proxy

# Monitor debug logs
tail -f logs/runninghub_proxy.log | grep DEBUG
```

### Performance Tuning

```bash
# Optimize for high concurrency
echo "MAX_CONCURRENT_WORKFLOWS=20" >> .env
echo "BROWSER_TIMEOUT=60000" >> .env
echo "WORKFLOW_EXECUTION_TIMEOUT=1200000" >> .env

# Monitor resource usage
watch -n 1 'ps aux | grep python | head -5'
```

### Support & Maintenance

- **Logs Location**: `logs/runninghub_proxy.log`
- **Configuration**: `.env` file
- **Temporary Files**: `temp/` directory
- **Downloads**: `downloads/` directory
- **Health Check**: `GET /health`
- **API Documentation**: `http://localhost:8000/docs`
