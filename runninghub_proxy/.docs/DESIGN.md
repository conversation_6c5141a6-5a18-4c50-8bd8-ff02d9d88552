# RunningHub Proxy API - Design Documentation

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [System Components](#system-components)
- [API Design](#api-design)
- [Browser Automation Strategy](#browser-automation-strategy)
- [Data Flow](#data-flow)
- [Security Considerations](#security-considerations)
- [Performance & Scalability](#performance--scalability)

## Architecture Overview

The RunningHub Proxy API follows a **modular, three-tier architecture** designed for reliability, maintainability, and scalability:

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Applications                       │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/WebSocket
┌─────────────────────▼───────────────────────────────────────┐
│                  FastAPI Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Auth      │ │  Workflows  │ │      WebSocket          │ │
│  │   Routes    │ │   Routes    │ │      Routes             │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ Dependencies
┌─────────────────────▼───────────────────────────────────────┐
│                Browser Automation Layer                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Browser   │ │   Session   │ │      Page               │ │
│  │   Manager   │ │   Manager   │ │      Operations         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │    Auth     │ │  Workflow   │                           │
│  │   Handler   │ │   Handler   │                           │
│  └─────────────┘ └─────────────┘                           │
└─────────────────────┬───────────────────────────────────────┘
                      │ Playwright
┌─────────────────────▼───────────────────────────────────────┐
│                  RunningHub.cn                              │
│              (Target Web Application)                       │
└─────────────────────────────────────────────────────────────┘
```

### Design Principles

1. **Separation of Concerns**: Clear boundaries between API, automation, and business logic
2. **Modularity**: Independent, reusable components with well-defined interfaces
3. **Resilience**: Comprehensive error handling, retry mechanisms, and graceful degradation
4. **Observability**: Extensive logging, monitoring, and real-time progress tracking
5. **Scalability**: Async/await patterns, connection pooling, and resource management

## System Components

### 1. API Layer (`api_utils/`)

**FastAPI Application (`app.py`)**
- Application lifecycle management
- Middleware configuration (CORS, compression, logging)
- Exception handling and error responses
- Health check endpoints

**Dependencies (`dependencies.py`)**
- Dependency injection for shared resources
- Authentication and authorization
- Concurrency limiting and rate limiting
- Request context management

**Routes**
- `auth.py`: Authentication flow management
- `workflows.py`: Workflow operations and execution
- `websocket.py`: Real-time communication

### 2. Browser Automation Layer (`browser_utils/`)

**Browser Manager (`browser_manager.py`)**
- Playwright browser lifecycle management
- Browser context creation and management
- Session-specific context isolation
- Resource cleanup and optimization

**Session Manager (`session_manager.py`)**
- User session creation and validation
- Authentication state persistence
- Execution history tracking
- Automatic cleanup of expired sessions

**Page Operations (`page_operations.py`)**
- Low-level browser interaction primitives
- Element detection and interaction
- File upload/download handling
- Screenshot and debugging utilities

**Authentication Handler (`auth_handler.py`)**
- Multi-method authentication support
- Manual action coordination (QR codes, SMS)
- Session establishment and validation
- Authentication status monitoring

**Workflow Handler (`workflow_handler.py`)**
- Schema extraction automation
- Workflow execution coordination
- Real-time progress monitoring
- Result processing and file handling

### 3. Configuration Layer (`config/`)

**Settings (`settings.py`)**
- Environment-based configuration
- Pydantic validation and type safety
- Default values and constraints

**Constants (`constants.py`)**
- Application-wide constants and enums
- Status codes and message templates
- Configuration defaults

**Selectors (`selectors.py`)**
- CSS selectors for RunningHub.cn elements
- Timeout configurations
- Wait conditions and strategies

### 4. Data Layer (`models/`)

**Authentication Models (`auth.py`)**
- Request/response models for auth operations
- Session information structures
- Authentication status tracking

**Workflow Models (`workflow.py`)**
- Schema definition and node information
- Execution request and progress tracking
- Result and history data structures

**Exception Models (`exceptions.py`)**
- Custom exception hierarchy
- HTTP exception mapping
- Error context and details

## API Design

### RESTful Endpoints

```
Authentication:
POST   /auth/login          # Initiate authentication
GET    /auth/status         # Check authentication status
POST   /auth/logout         # Terminate session
GET    /auth/session        # Get session details

Workflows:
GET    /workflows/{id}/schema           # Extract workflow schema
POST   /workflows/{id}/execute          # Execute workflow (SSE stream)
GET    /workflows/{id}/executions/{eid}/status  # Get execution status
DELETE /workflows/{id}/executions/{eid}         # Cancel execution
GET    /workflows/history               # Get execution history

System:
GET    /health              # Health check
GET    /ws/connections      # WebSocket statistics
```

### WebSocket Endpoints

```
WebSocket:
/ws/logs                    # Real-time log streaming
```

### Response Formats

**Standard Response Structure:**
```json
{
  "status": "success|error",
  "data": { ... },
  "message": "Human readable message",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Error Response Structure:**
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Server-Sent Events (SSE)

Workflow execution uses SSE for real-time progress updates:

```
event: progress
data: {"execution_id": "...", "status": "running", "progress_percent": 45.0}

event: error
data: {"execution_id": "...", "status": "failed", "message": "..."}

event: complete
data: {"execution_id": "...", "status": "completed", "results": {...}}
```

## Browser Automation Strategy

### Three-Tier Response Strategy

1. **Direct API (Future Enhancement)**
   - Direct integration with RunningHub.cn APIs when available
   - Fastest response times and highest reliability

2. **Browser Automation (Current Implementation)**
   - Playwright-based web automation
   - Handles complex UI interactions and authentication

3. **Fallback Mechanisms**
   - Retry logic with exponential backoff
   - Alternative interaction strategies
   - Graceful degradation and error reporting

### Automation Workflow

**Schema Extraction Process:**
```
1. Navigate to /post/{workflow_id}
2. Extract workflow metadata (title, description, author, tags)
3. Click "运行工作流" button
4. Wait for ComfyUI iframe to load
5. Right-click in iframe main area
6. Click "导出工作流API" from context menu
7. Download and parse API JSON file
8. Transform to standardized nodeInfo_list format
```

**Workflow Execution Process:**
```
1. Navigate to execution interface
2. Fill form inputs based on nodeInfo_list
3. Click "运行" button to start execution
4. Monitor progress indicators and task list
5. Stream real-time progress updates via SSE
6. Detect completion or error conditions
7. Extract and return results
```

### Element Detection Strategy

**Robust Selector Strategy:**
- Primary selectors based on stable element attributes
- Fallback selectors using text content and structure
- Dynamic timeout adjustment based on operation type
- Retry mechanisms with different selector strategies

**Wait Conditions:**
- `networkidle`: For page load completion
- `visible`: For interactive elements
- `attached`: For DOM presence verification
- `detached`: For element removal confirmation

## Data Flow

### Authentication Flow

```
Client Request → FastAPI Auth Route → Auth Handler → Browser Automation
     ↓
Session Creation ← Session Manager ← Authentication Success
     ↓
Response with Session Token → Client
```

### Schema Extraction Flow

```
Client Request → FastAPI Workflow Route → Workflow Handler
     ↓
Browser Navigation → Element Interaction → File Download
     ↓
JSON Parsing → Schema Transformation → Response
```

### Workflow Execution Flow

```
Client Request → FastAPI Workflow Route → Workflow Handler
     ↓
Form Filling → Execution Start → Progress Monitoring
     ↓
SSE Stream ← Progress Updates ← Real-time Monitoring
```

## Security Considerations

### Authentication Security
- Session-based authentication with secure tokens
- Configurable session timeouts
- Automatic session cleanup
- Optional API key authentication

### Browser Security
- Isolated browser contexts per session
- Automatic cookie and storage cleanup
- Headless mode for production environments
- Resource usage monitoring and limits

### Data Protection
- Sensitive data redaction in logs
- Secure temporary file handling
- Automatic cleanup of downloaded files
- Environment-based configuration

### Rate Limiting
- Concurrency limits for workflow executions
- Request rate limiting (configurable)
- Resource usage monitoring
- Graceful degradation under load

## Performance & Scalability

### Async Architecture
- Full async/await implementation
- Non-blocking I/O operations
- Concurrent request handling
- Efficient resource utilization

### Resource Management
- Browser context pooling
- Automatic resource cleanup
- Memory usage monitoring
- Configurable timeout values

### Monitoring & Observability
- Comprehensive logging with Loguru
- Performance metrics tracking
- Real-time WebSocket monitoring
- Health check endpoints

### Scalability Considerations
- Horizontal scaling support
- Stateless design (except sessions)
- External session storage ready (Redis/Database)
- Load balancer compatibility

### Performance Optimizations
- Connection reuse and pooling
- Efficient element detection strategies
- Optimized wait conditions
- Minimal browser resource usage
