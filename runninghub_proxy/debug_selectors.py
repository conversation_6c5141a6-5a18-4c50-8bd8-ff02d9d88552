"""Debug script to test selector availability on RunningHub.cn"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.selectors import RunningHubSelectors, CAPTCHA_PATTERNS, LOGIN_SUCCESS_INDICATORS
from config.settings import settings

async def debug_selectors():
    """Debug and test all selectors on the actual RunningHub.cn page."""
    
    async with async_playwright() as p:
        # Launch browser in non-headless mode for visual debugging
        browser = await p.chromium.launch(
            headless=False,  # Set to False to see what's happening
            slow_mo=1000,    # Slow down actions for visibility
            args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--start-maximized"
            ]
        )
        
        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        page = await context.new_page()
        
        try:
            print("🌐 Navigating to RunningHub.cn...")
            await page.goto("https://www.runninghub.cn", wait_until="networkidle")
            
            print(f"📄 Current URL: {page.url}")
            print(f"📋 Page Title: {await page.title()}")
            
            # Take initial screenshot
            await page.screenshot(path="debug_initial_page.png")
            print("📸 Initial screenshot saved as debug_initial_page.png")
            
            print("\n" + "="*60)
            print("🔍 TESTING LOGIN BUTTON SELECTORS")
            print("="*60)
            
            # Test LOGIN_BUTTON selector - split by comma
            login_selector_string = RunningHubSelectors.LOGIN_BUTTON
            login_selectors = [s.strip() for s in login_selector_string.split(',')]
            
            print(f"\n🎯 Current LOGIN_BUTTON selector: {login_selector_string}")
            print(f"🎯 Split into individual selectors: {login_selectors}")
            
            print("\n🎯 Testing individual login button selectors:")
            working_selectors = []
            
            for i, selector in enumerate(login_selectors, 1):
                try:
                    elements = await page.locator(selector).all()
                    count = len(elements)
                    
                    if count > 0:
                        print(f"✅ {i}. '{selector}' -> Found {count} element(s)")
                        working_selectors.append(selector)
                        
                        # Get details of first element
                        first_element = elements[0]
                        try:
                            text = await first_element.inner_text() if await first_element.is_visible() else "Not visible"
                            classes = await first_element.get_attribute("class") or "No classes"
                            visible = await first_element.is_visible()
                            enabled = await first_element.is_enabled()
                            
                            print(f"    📝 Text: '{text}'")
                            print(f"    🎨 Classes: '{classes}'")
                            print(f"    👁️  Visible: {visible}")
                            print(f"    ⚡ Enabled: {enabled}")
                            
                            if visible and enabled:
                                print(f"    🎯 GOOD CANDIDATE FOR CLICKING!")
                        except Exception as detail_error:
                            print(f"    ⚠️  Could not get element details: {detail_error}")
                        
                    else:
                        print(f"❌ {i}. '{selector}' -> No elements found")
                        
                except Exception as e:
                    print(f"💥 {i}. '{selector}' -> Error: {str(e)}")
            
            # Try alternative selectors
            print("\n🔍 TESTING ALTERNATIVE LOGIN BUTTON SELECTORS")
            alternative_selectors = [
                "button:has-text('登录')",
                "button:has-text('登 录')",
                "a:has-text('登录')",
                "a:has-text('登 录')",
                ".login-btn",
                "button[class*='login']",
                "[class*='login']",
                "button:has-text('立即登录')",
                "button:has-text('Sign In')",
                "button:has-text('Log In')",
                "[role='button']:has-text('登录')"
            ]
            
            for i, selector in enumerate(alternative_selectors, 1):
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        print(f"✅ Alt {i}. '{selector}' -> Found {count} element(s)")
                        working_selectors.append(selector)
                        
                        # Test if clickable
                        first_element = page.locator(selector).first
                        visible = await first_element.is_visible()
                        enabled = await first_element.is_enabled()
                        text = await first_element.inner_text() if visible else "Not visible"
                        
                        print(f"    📝 Text: '{text}' | Visible: {visible} | Enabled: {enabled}")
                    else:
                        print(f"❌ Alt {i}. '{selector}' -> No elements found")
                        
                except Exception as e:
                    print(f"💥 Alt {i}. '{selector}' -> Error: {str(e)}")
            
            print("\n" + "="*60)
            print("🔍 SCANNING ALL BUTTONS ON PAGE")
            print("="*60)
            
            # Get all buttons and analyze them
            all_buttons = await page.locator("button").all()
            print(f"\n📊 Found {len(all_buttons)} button elements on page:")
            
            login_candidates = []
            for i, button in enumerate(all_buttons[:30], 1):  # Limit to first 30 buttons
                try:
                    text = await button.inner_text() if await button.is_visible() else ""
                    classes = await button.get_attribute("class") or ""
                    visible = await button.is_visible()
                    enabled = await button.is_enabled()
                    
                    # Truncate long text/classes for readability
                    display_text = text[:30] + "..." if len(text) > 30 else text
                    display_classes = classes[:50] + "..." if len(classes) > 50 else classes
                    
                    print(f"{i:2d}. Text: '{display_text}' | Classes: '{display_classes}' | V: {visible} | E: {enabled}")
                    
                    # Check if this looks like a login button
                    if any(keyword in text.lower() for keyword in ["登录", "登 录", "login", "sign in"]):
                        login_candidates.append((text, classes, visible, enabled))
                        print(f"    🎯 POTENTIAL LOGIN BUTTON!")
                        
                except Exception as e:
                    print(f"{i:2d}. Error getting button info: {str(e)}")
            
            print(f"\n📊 Found {len(login_candidates)} potential login buttons")
            
            print("\n" + "="*60)
            print("🔍 SCANNING ALL LINKS ON PAGE")
            print("="*60)
            
            # Get all links that might be login buttons
            all_links = await page.locator("a").all()
            login_links = []
            
            for i, link in enumerate(all_links[:30], 1):  # Limit to first 30 links
                try:
                    text = await link.inner_text() if await link.is_visible() else ""
                    href = await link.get_attribute("href") or ""
                    classes = await link.get_attribute("class") or ""
                    visible = await link.is_visible()
                    
                    if any(keyword in text.lower() for keyword in ["登录", "登 录", "login", "sign in"]) or "login" in href.lower():
                        login_links.append((text, href, classes, visible))
                        display_text = text[:20] + "..." if len(text) > 20 else text
                        display_href = href[:30] + "..." if len(href) > 30 else href
                        display_classes = classes[:30] + "..." if len(classes) > 30 else classes
                        print(f"🔗 {len(login_links)}. Text: '{display_text}' | Href: '{display_href}' | Classes: '{display_classes}' | Visible: {visible}")
                        
                except Exception as e:
                    print(f"Error getting link info: {str(e)}")
            
            print(f"\n📊 Found {len(login_links)} potential login links")
            
            print("\n" + "="*60)
            print("🧪 TESTING CLICK ON BEST LOGIN BUTTON")
            print("="*60)
            
            # Try to find and click the best login button
            clicked = False
            
            if working_selectors:
                print(f"🎯 Trying working selectors: {working_selectors}")
                for selector in working_selectors[:3]:  # Try first 3 working selectors
                    try:
                        element = page.locator(selector).first
                        if await element.count() > 0 and await element.is_visible() and await element.is_enabled():
                            print(f"🎯 Attempting to click: {selector}")
                            
                            # Scroll into view and click
                            await element.scroll_into_view_if_needed()
                            await element.click(timeout=5000)
                            
                            print(f"✅ Successfully clicked login button with: {selector}")
                            clicked = True
                            
                            # Wait for modal or page change
                            await page.wait_for_timeout(3000)
                            
                            # Take screenshot after click
                            await page.screenshot(path="debug_after_login_click.png")
                            print("📸 Post-click screenshot saved as debug_after_login_click.png")
                            
                            # Check if login modal appeared
                            modal_selector = RunningHubSelectors.LOGIN_MODAL
                            modal_count = await page.locator(modal_selector).count()
                            visible_modal_count = await page.locator(f"{modal_selector}:visible").count()
                            print(f"🎭 Login modal check '{modal_selector}': {modal_count} total, {visible_modal_count} visible")
                            
                            if visible_modal_count > 0:
                                print("✅ Login modal successfully opened!")
                                
                                # Test form elements in modal
                                print("\n🔍 Testing form elements in modal:")
                                form_selectors = {
                                    "PHONE_INPUT": RunningHubSelectors.PHONE_INPUT,
                                    "SMS_CODE_INPUT": RunningHubSelectors.SMS_CODE_INPUT,
                                    "PASSWORD_INPUT": RunningHubSelectors.PASSWORD_INPUT,
                                    "SEND_SMS_BUTTON": RunningHubSelectors.SEND_SMS_BUTTON,
                                }
                                
                                for name, form_selector in form_selectors.items():
                                    try:
                                        form_count = await page.locator(form_selector).count()
                                        visible_form_count = await page.locator(f"{form_selector}:visible").count()
                                        print(f"📝 {name}: {form_count} total, {visible_form_count} visible")
                                    except Exception as e:
                                        print(f"💥 {name}: Error - {str(e)}")
                            
                            break
                            
                    except Exception as e:
                        print(f"❌ Failed to click '{selector}': {str(e)}")
            
            if not clicked:
                print("❌ Could not find any clickable login button")
                print("🔍 Trying fallback approach with generic selectors...")
                
                fallback_selectors = [
                    "button",  # All buttons
                    "a[href*='login']",  # Login links
                    "[onclick*='login']",  # Click handlers
                ]
                
                for fallback in fallback_selectors:
                    try:
                        elements = await page.locator(fallback).all()
                        print(f"📊 Found {len(elements)} elements with '{fallback}'")
                        
                        for i, elem in enumerate(elements[:5]):  # Check first 5
                            text = await elem.inner_text() if await elem.is_visible() else ""
                            if any(keyword in text.lower() for keyword in ["登录", "login"]):
                                print(f"  {i+1}. Potential: '{text[:30]}'")
                    except Exception as e:
                        print(f"Error with fallback '{fallback}': {e}")
            
            print(f"\n✅ WORKING SELECTORS SUMMARY:")
            for selector in working_selectors:
                print(f"  - {selector}")
            
            # Keep browser open for manual inspection
            print(f"\n🔍 Browser will stay open for 30 seconds for manual inspection...")
            print("🔍 You can manually test clicking login buttons to see what works...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"💥 Error during debugging: {str(e)}")
            await page.screenshot(path="debug_error.png")
            print("📸 Error screenshot saved as debug_error.png")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_selectors())