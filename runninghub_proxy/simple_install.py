#!/usr/bin/env python3
"""Simple installation script for RunningHub Proxy API."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return success status."""
    if description:
        print(f"🔄 {description}...")
    
    print(f"   $ {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, text=True)
        if description:
            print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed")
        print(f"   Error: {e}")
        return False


def main():
    """Main installation function."""
    print("🚀 RunningHub Proxy API - Simple Installation")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ pyproject.toml not found. Please run from the runninghub_proxy directory.")
        return False
    
    print("✅ Found pyproject.toml")
    
    # Test TOML syntax first
    print("\n🔍 Testing TOML syntax...")
    try:
        import tomllib
    except ImportError:
        try:
            import tomli as tomllib
        except ImportError:
            print("Installing tomli for TOML parsing...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "tomli"])
            import tomli as tomllib
    
    try:
        with open("pyproject.toml", "rb") as f:
            tomllib.load(f)
        print("✅ pyproject.toml syntax is valid")
    except Exception as e:
        print(f"❌ pyproject.toml syntax error: {e}")
        return False
    
    # Check if uv is available
    if shutil.which("uv"):
        print("✅ uv is available")
        
        # Try uv sync
        print("\n📦 Installing dependencies with uv...")
        if run_command("uv sync", "Installing dependencies"):
            print("✅ Dependencies installed successfully with uv")
            
            # Install Playwright browsers
            if run_command("uv run playwright install chromium", "Installing Playwright browsers"):
                print("✅ Playwright browsers installed")
            
            # Test import
            if run_command("uv run python -c 'import runninghub_proxy; print(\"Import successful\")'", "Testing module import"):
                print("✅ Module import test passed")
                installation_method = "uv"
            else:
                print("⚠️  Module import test failed with uv")
                installation_method = None
        else:
            print("❌ uv sync failed")
            installation_method = None
    else:
        print("⚠️  uv not found")
        installation_method = None
    
    # Fallback to pip if uv failed
    if not installation_method:
        print("\n📦 Falling back to pip installation...")
        
        # Create virtual environment
        if not Path(".venv").exists():
            if run_command("python -m venv .venv", "Creating virtual environment"):
                print("✅ Virtual environment created")
            else:
                print("❌ Failed to create virtual environment")
                return False
        
        # Determine pip path
        if os.name == 'nt':  # Windows
            pip_cmd = ".venv\\Scripts\\pip"
            python_cmd = ".venv\\Scripts\\python"
        else:  # Unix/Linux/macOS
            pip_cmd = ".venv/bin/pip"
            python_cmd = ".venv/bin/python"
        
        # Install in editable mode
        if run_command(f"{pip_cmd} install -e .", "Installing with pip"):
            print("✅ Dependencies installed successfully with pip")
            
            # Install Playwright browsers
            if run_command(f"{python_cmd} -m playwright install chromium", "Installing Playwright browsers"):
                print("✅ Playwright browsers installed")
            
            # Test import
            if run_command(f"{python_cmd} -c 'import runninghub_proxy; print(\"Import successful\")'", "Testing module import"):
                print("✅ Module import test passed")
                installation_method = "pip"
            else:
                print("⚠️  Module import test failed")
                installation_method = None
    
    # Create directories
    print("\n📁 Creating directories...")
    for directory in ["logs", "temp", "downloads"]:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/")
    
    # Setup environment
    print("\n⚙️  Setting up environment...")
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✅ Created .env from .env.example")
        else:
            print("⚠️  No .env.example found")
    else:
        print("✅ .env already exists")
    
    # Final status
    print("\n" + "=" * 60)
    if installation_method:
        print("🎉 Installation completed successfully!")
        print(f"   Method used: {installation_method}")
        print("\n📋 Next steps:")
        
        if installation_method == "uv":
            print("1. Start the server:")
            print("   uv run python server.py")
            print("\n2. Test the API:")
            print("   curl http://localhost:8000/health")
            print("\n3. View documentation:")
            print("   http://localhost:8000/docs")
        else:
            print("1. Activate virtual environment:")
            if os.name == 'nt':
                print("   .venv\\Scripts\\activate")
            else:
                print("   source .venv/bin/activate")
            print("\n2. Start the server:")
            print("   python server.py")
            print("\n3. Test the API:")
            print("   curl http://localhost:8000/health")
        
        return True
    else:
        print("❌ Installation failed with all methods")
        print("\n🔧 Manual installation steps:")
        print("1. Check Python version (3.9+ required)")
        print("2. Install dependencies manually:")
        print("   pip install fastapi uvicorn playwright pydantic loguru")
        print("3. Install Playwright browsers:")
        print("   playwright install chromium")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Installation interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)
