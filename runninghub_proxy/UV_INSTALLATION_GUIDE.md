# UV Installation Guide for RunningHub Proxy API

## Quick Start (Recommended)

### Option 1: Automated Installation
```bash
cd runninghub_proxy
python install_with_uv.py
```

### Option 2: Manual Step-by-Step

#### 1. Install uv (if not already installed)
```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Reload your shell or restart terminal
source ~/.bashrc  # or source ~/.zshrc
```

#### 2. Verify uv installation
```bash
uv --version
```

#### 3. Create virtual environment and install dependencies
```bash
cd runninghub_proxy

# Create virtual environment and install dependencies
uv sync

# Install Playwright browsers
uv run playwright install chromium
```

#### 4. Create necessary directories
```bash
mkdir -p logs temp downloads
```

#### 5. Setup environment configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional)
nano .env
```

#### 6. Test the installation
```bash
# Test module import
uv run python -c "import runninghub_proxy; print('✅ Import successful')"

# Test configuration
uv run python -c "from runninghub_proxy.config.settings import settings; print('✅ Settings loaded')"

# Test Playwright
uv run python -c "from playwright.async_api import async_playwright; print('✅ Playwright ready')"
```

#### 7. Start the server
```bash
uv run python server.py
```

#### 8. Verify server is running
```bash
# In another terminal
curl http://localhost:8000/health

# Or visit in browser
open http://localhost:8000/docs
```

## Development Commands

### Basic Commands
```bash
# Start server
uv run python server.py

# Run tests
uv run pytest tests/

# Run stability tests
uv run python test_stability.py

# Format code
uv run black .

# Lint code
uv run ruff check .
```

### Using Makefile (if available)
```bash
# Install dependencies
make install

# Start development server
make run

# Run all tests
make test

# Check code quality
make check

# Format code
make format
```

## Troubleshooting

### Issue: uv command not found
**Solution:**
```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Add to PATH (add to ~/.bashrc or ~/.zshrc)
export PATH="$HOME/.cargo/bin:$PATH"

# Reload shell
source ~/.bashrc
```

### Issue: Permission denied during installation
**Solution:**
```bash
# Make sure you have write permissions
chmod +x install_with_uv.py

# Or run with python
python install_with_uv.py
```

### Issue: Playwright installation fails
**Solution:**
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

# Then retry Playwright installation
uv run playwright install chromium
uv run playwright install-deps
```

### Issue: Import errors
**Solution:**
```bash
# Make sure you're in the project directory
cd runninghub_proxy

# Reinstall dependencies
uv sync --reinstall

# Check Python path
uv run python -c "import sys; print(sys.path)"
```

### Issue: Port already in use
**Solution:**
```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or change port in .env file
echo "PORT=8001" >> .env
```

## Advanced Usage

### Development with different dependency groups
```bash
# Install with development dependencies
uv sync --extra dev

# Install with test dependencies only
uv sync --extra test

# Install with all optional dependencies
uv sync --all-extras
```

### Managing dependencies
```bash
# Add a new dependency
uv add requests

# Add a development dependency
uv add --dev pytest-mock

# Remove a dependency
uv remove requests

# Update all dependencies
uv sync --upgrade
```

### Virtual environment management
```bash
# Activate virtual environment (if needed)
source .venv/bin/activate

# Deactivate
deactivate

# Remove virtual environment
rm -rf .venv
uv venv  # Create new one
```

## Performance Tips

### Faster installations
```bash
# Use uv's built-in caching
uv sync --cache-dir ~/.cache/uv

# Parallel installation
uv sync --no-build-isolation
```

### Development workflow
```bash
# Watch for changes and restart server
uv run python -m uvicorn runninghub_proxy.api_utils.app:app --reload

# Run tests in watch mode
uv run pytest tests/ --watch
```

## Verification Checklist

After installation, verify these work:

- [ ] `uv --version` shows uv version
- [ ] `uv run python -c "import runninghub_proxy"` succeeds
- [ ] `uv run python server.py` starts without errors
- [ ] `curl http://localhost:8000/health` returns 200 OK
- [ ] `uv run playwright install chromium` completes successfully
- [ ] Browser automation tests pass

## Getting Help

If you encounter issues:

1. **Check the logs**: Look in `logs/runninghub_proxy.log`
2. **Run diagnostics**: `uv run python test_stability.py`
3. **Check configuration**: Verify `.env` file settings
4. **Update dependencies**: `uv sync --upgrade`
5. **Clean install**: Remove `.venv` and run `uv sync` again

## Next Steps

Once installation is complete:

1. **Configure settings**: Edit `.env` file for your environment
2. **Read documentation**: Check `docs/` directory
3. **Run tests**: Execute `uv run pytest tests/`
4. **Start developing**: Use `make run` or `uv run python server.py`
5. **Deploy**: Follow `docs/DEPLOYMENT.md` for production setup
