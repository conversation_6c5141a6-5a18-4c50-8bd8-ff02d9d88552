#!/usr/bin/env python3
"""Minimal test to verify project structure and basic imports."""

import sys
import os
from pathlib import Path

def test_project_structure():
    """Test that all required files and directories exist."""
    print("🧪 Testing project structure...")
    
    required_files = [
        "README.md",
        "pyproject.toml", 
        "requirements.txt",
        "server.py",
        "setup.py",
        ".env.example",
        "__init__.py",
        "api_utils/__init__.py",
        "api_utils/app.py",
        "api_utils/dependencies.py",
        "api_utils/routes/__init__.py",
        "api_utils/routes/auth.py",
        "api_utils/routes/workflows.py",
        "api_utils/routes/websocket.py",
        "browser_utils/__init__.py",
        "browser_utils/browser_manager.py",
        "browser_utils/session_manager.py",
        "browser_utils/page_operations.py",
        "browser_utils/auth_handler.py",
        "browser_utils/workflow_handler.py",
        "config/__init__.py",
        "config/settings.py",
        "config/constants.py",
        "config/selectors.py",
        "models/__init__.py",
        "models/auth.py",
        "models/workflow.py",
        "models/exceptions.py",
        "logging_utils/__init__.py",
        "logging_utils/setup.py",
        "docs/DESIGN.md",
        "docs/DEPLOYMENT.md",
        "docs/API_REFERENCE.md",
        "tests/test_basic.py",
        "tests/test_implementation.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print(f"✅ All {len(required_files)} required files exist")
        return True

def test_python_syntax():
    """Test that all Python files have valid syntax."""
    print("🧪 Testing Python syntax...")
    
    python_files = []
    for root, dirs, files in os.walk("."):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    syntax_errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source = f.read()
            compile(source, file_path, 'exec')
        except SyntaxError as e:
            syntax_errors.append(f"{file_path}: {e}")
        except Exception as e:
            syntax_errors.append(f"{file_path}: {e}")
    
    if syntax_errors:
        print(f"❌ Syntax errors found:")
        for error in syntax_errors:
            print(f"   {error}")
        return False
    else:
        print(f"✅ All {len(python_files)} Python files have valid syntax")
        return True

def test_configuration_files():
    """Test configuration files."""
    print("🧪 Testing configuration files...")
    
    # Test pyproject.toml
    try:
        import tomllib
    except ImportError:
        try:
            import tomli as tomllib
        except ImportError:
            print("⚠️  Cannot test pyproject.toml - tomllib/tomli not available")
            return True
    
    try:
        with open("pyproject.toml", "rb") as f:
            pyproject_data = tomllib.load(f)
        
        # Check required sections
        required_sections = ["tool.poetry", "tool.poetry.dependencies"]
        for section in required_sections:
            keys = section.split(".")
            data = pyproject_data
            for key in keys:
                if key not in data:
                    print(f"❌ Missing section in pyproject.toml: {section}")
                    return False
                data = data[key]
        
        print("✅ pyproject.toml is valid")
    except Exception as e:
        print(f"❌ Error reading pyproject.toml: {e}")
        return False
    
    # Test .env.example
    try:
        with open(".env.example", "r") as f:
            env_content = f.read()
        
        required_vars = ["HOST", "PORT", "DEBUG", "API_TITLE"]
        missing_vars = []
        for var in required_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing environment variables in .env.example: {missing_vars}")
            return False
        else:
            print("✅ .env.example contains required variables")
    except Exception as e:
        print(f"❌ Error reading .env.example: {e}")
        return False
    
    return True

def test_documentation():
    """Test documentation files."""
    print("🧪 Testing documentation...")
    
    doc_files = [
        ("README.md", ["# RunningHub Proxy API", "## Overview"]),
        ("docs/DESIGN.md", ["# RunningHub Proxy API - Design Documentation", "## Architecture Overview"]),
        ("docs/DEPLOYMENT.md", ["# RunningHub Proxy API - Deployment Guide", "## Quick Start"]),
        ("docs/API_REFERENCE.md", ["# RunningHub Proxy API - API Reference", "## Authentication"])
    ]
    
    for doc_file, required_sections in doc_files:
        try:
            with open(doc_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if missing_sections:
                print(f"❌ {doc_file} missing sections: {missing_sections}")
                return False
            else:
                print(f"✅ {doc_file} contains required sections")
                
        except Exception as e:
            print(f"❌ Error reading {doc_file}: {e}")
            return False
    
    return True

def test_import_structure():
    """Test basic import structure without dependencies."""
    print("🧪 Testing import structure...")
    
    # Test that files can be parsed as modules
    test_imports = [
        "config.constants",
        "config.selectors", 
        "models.auth",
        "models.workflow",
        "models.exceptions"
    ]
    
    # Add current directory to Python path
    sys.path.insert(0, ".")
    
    import_errors = []
    for module_name in test_imports:
        try:
            # Try to compile the module file
            module_path = module_name.replace(".", "/") + ".py"
            with open(module_path, 'r', encoding='utf-8') as f:
                source = f.read()
            compile(source, module_path, 'exec')
            print(f"✅ {module_name} syntax is valid")
        except Exception as e:
            import_errors.append(f"{module_name}: {e}")
    
    if import_errors:
        print(f"❌ Import structure errors:")
        for error in import_errors:
            print(f"   {error}")
        return False
    else:
        print("✅ Import structure is valid")
        return True

def main():
    """Run all minimal tests."""
    print("🧪 RunningHub Proxy API - Minimal Test Suite")
    print("=" * 60)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Python Syntax", test_python_syntax),
        ("Configuration Files", test_configuration_files),
        ("Documentation", test_documentation),
        ("Import Structure", test_import_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append(result)
            
            if result:
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All minimal tests passed!")
        print("\n📋 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install Playwright browsers: playwright install chromium")
        print("3. Run setup script: python setup.py")
        print("4. Start server: python server.py")
        print("5. Run full tests: python test_server.py")
        return True
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
