#!/usr/bin/env python3
"""Diagnose why /docs endpoint returns 404."""

import os
import sys
from pathlib import Path


def diagnose_docs_issue():
    """Diagnose the /docs 404 issue."""
    print("🔍 Diagnosing /docs 404 Issue")
    print("=" * 50)
    
    # Check environment variables
    print("📋 Environment Variables:")
    env_vars = ["APP_DEBUG", "DEBUG", "PLAYWRIGHT_DEBUG", "PWDEBUG"]
    for var in env_vars:
        value = os.environ.get(var, "NOT SET")
        print(f"   {var}={value}")
    
    # Check .env file
    print("\n📝 .env File Contents:")
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, "r") as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            if any(var in line for var in ["DEBUG", "APP_DEBUG"]):
                print(f"   Line {i}: {line.strip()}")
    else:
        print("   ❌ .env file not found")
    
    # Test settings import
    print("\n⚙️  Testing Settings Import:")
    try:
        from runninghub_proxy.config.settings import settings
        print("   ✅ Settings imported successfully")
        print(f"   debug: {settings.debug}")
        print(f"   api_title: {settings.api_title}")
        print(f"   api_version: {settings.api_version}")
        
        if hasattr(settings, 'playwright_debug'):
            print(f"   playwright_debug: {settings.playwright_debug}")
        if hasattr(settings, 'playwright_debug_mode'):
            print(f"   playwright_debug_mode: {settings.playwright_debug_mode}")
            
    except Exception as e:
        print(f"   ❌ Settings import failed: {e}")
        return False
    
    # Test FastAPI app creation
    print("\n🚀 Testing FastAPI App Creation:")
    try:
        from runninghub_proxy.api_utils.app import create_app
        app = create_app()
        print("   ✅ FastAPI app created successfully")
        
        # Check app configuration
        print(f"   title: {app.title}")
        print(f"   version: {app.version}")
        print(f"   docs_url: {app.docs_url}")
        print(f"   redoc_url: {app.redoc_url}")
        print(f"   openapi_url: {app.openapi_url}")
        
        # Check routes
        print(f"   Total routes: {len(app.routes)}")
        
        # List some key routes
        print("   Key routes:")
        for route in app.routes:
            if hasattr(route, 'path'):
                if route.path in ["/docs", "/redoc", "/openapi.json", "/health"]:
                    print(f"     {route.path} - {getattr(route, 'methods', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ FastAPI app creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_docs_access():
    """Test direct access to docs."""
    print("\n🌐 Testing Direct Access:")
    
    try:
        import requests
        
        # Test health endpoint first
        print("   Testing /health endpoint...")
        try:
            response = requests.get("http://localhost:8973/health", timeout=5)
            print(f"   /health: {response.status_code}")
        except Exception as e:
            print(f"   /health: Failed - {e}")
            print("   ⚠️  Server may not be running")
            return False
        
        # Test docs endpoint
        print("   Testing /docs endpoint...")
        try:
            response = requests.get("http://localhost:8973/docs", timeout=5)
            print(f"   /docs: {response.status_code}")
            if response.status_code == 404:
                print("   ❌ /docs returns 404 - this is the issue!")
            elif response.status_code == 200:
                print("   ✅ /docs works correctly")
        except Exception as e:
            print(f"   /docs: Failed - {e}")
        
        # Test openapi.json
        print("   Testing /openapi.json endpoint...")
        try:
            response = requests.get("http://localhost:8973/openapi.json", timeout=5)
            print(f"   /openapi.json: {response.status_code}")
        except Exception as e:
            print(f"   /openapi.json: Failed - {e}")
            
    except ImportError:
        print("   ⚠️  requests not available, skipping HTTP tests")
        print("   Install with: pip install requests")


def provide_solutions():
    """Provide solutions based on diagnosis."""
    print("\n💡 Potential Solutions:")
    print("=" * 50)
    
    print("1. 🔧 Check debug setting:")
    print("   Ensure APP_DEBUG=true in .env file")
    print("   The /docs endpoint is only enabled when debug=True")
    print()
    
    print("2. 🔄 Restart the server:")
    print("   Stop the current server (Ctrl+C)")
    print("   Run: uv run python server.py")
    print()
    
    print("3. 📝 Force enable docs:")
    print("   Edit api_utils/app.py line 79-80:")
    print("   Change:")
    print("     docs_url=\"/docs\" if settings.debug else None,")
    print("   To:")
    print("     docs_url=\"/docs\",")
    print()
    
    print("4. 🧪 Test configuration:")
    print("   Run: python -c \"from runninghub_proxy.config.settings import settings; print(f'Debug: {settings.debug}')\"")
    print()
    
    print("5. 🌐 Test endpoints manually:")
    print("   curl http://localhost:8973/health")
    print("   curl http://localhost:8973/docs")
    print("   curl http://localhost:8973/openapi.json")


def main():
    """Main diagnostic function."""
    print("🔍 RunningHub Proxy - /docs 404 Diagnostics")
    print("=" * 60)
    
    # Run diagnostics
    config_ok = diagnose_docs_issue()
    
    if config_ok:
        test_docs_access()
    
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("📊 Summary:")
    if config_ok:
        print("✅ Configuration appears correct")
        print("🔍 Check if debug=True and server is restarted")
    else:
        print("❌ Configuration issues detected")
        print("🔧 Fix configuration errors first")
    
    return config_ok


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
