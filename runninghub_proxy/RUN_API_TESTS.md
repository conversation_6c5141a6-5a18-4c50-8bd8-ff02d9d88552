# 🧪 RunningHub Proxy API - Comprehensive Endpoint Testing

## 🚀 Quick Test Commands

Since your server is running on **http://localhost:8973**, here are the exact commands to test all endpoints:

### **1. Run the Comprehensive Test Script**
```bash
cd runninghub_proxy
python test_endpoints_simple.py
```

### **2. Manual curl Commands for Each Endpoint**

#### **Health Endpoint** ✅
```bash
curl -s -w "\nStatus: %{http_code}\n" http://localhost:8973/health
```

#### **Documentation Endpoints** 📚
```bash
# API Documentation
curl -s -w "\nStatus: %{http_code}\n" http://localhost:8973/docs

# ReDoc Documentation  
curl -s -w "\nStatus: %{http_code}\n" http://localhost:8973/redoc

# OpenAPI Schema
curl -s -w "\nStatus: %{http_code}\n" http://localhost:8973/openapi.json
```

#### **Authentication Endpoints** 🔐
```bash
# Check auth status
curl -s -w "\nStatus: %{http_code}\n" http://localhost:8973/auth/status

# Test login with invalid data (should return 422)
curl -X POST -H "Content-Type: application/json" \
  -d '{"method": "phone_sms", "phone": "invalid_phone"}' \
  -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/auth/login

# Test logout (should require auth - 401)
curl -X POST -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/auth/logout
```

#### **Workflow Endpoints** ⚙️
```bash
# Get workflow schema (should require auth - 401)
curl -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/workflows/test_workflow_123/schema

# Execute workflow (should require auth - 401)
curl -X POST -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "test_workflow_123",
    "nodeInfo_list": [
      {
        "node_id": "test_node",
        "field_name": "test_field",
        "field_value": "test_value", 
        "class_type": "test_type"
      }
    ]
  }' \
  -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/workflows/test_workflow_123/execute

# Get workflow history (should require auth - 401)
curl -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/workflows/history
```

#### **Error Handling** 🚫
```bash
# Test non-existent endpoint (should return 404)
curl -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/nonexistent

# Test invalid method (should return 405)
curl -X PATCH -s -w "\nStatus: %{http_code}\n" \
  http://localhost:8973/health
```

#### **WebSocket Test** 🔌
```bash
# Test WebSocket connection (requires wscat)
# Install: npm install -g wscat
wscat -c ws://localhost:8973/ws/logs
```

## 🔧 **Expected Results**

### **✅ Working Endpoints (Should Return 200)**
- `GET /health` - Health check
- `GET /docs` - API documentation  
- `GET /redoc` - ReDoc documentation
- `GET /openapi.json` - OpenAPI schema

### **🔐 Authentication Required (Should Return 401)**
- `GET /auth/status` - May return 200 or 401
- `POST /auth/logout` - Should return 401 (no auth)
- `GET /workflows/{id}/schema` - Should return 401 (no auth)
- `POST /workflows/{id}/execute` - Should return 401 (no auth)
- `GET /workflows/history` - Should return 401 (no auth)

### **🚫 Error Responses**
- `POST /auth/login` with invalid data - Should return 422 (validation error)
- `GET /nonexistent` - Should return 404 (not found)
- `PATCH /health` - Should return 405 (method not allowed)

### **🔌 WebSocket**
- `ws://localhost:8973/ws/logs` - Should establish connection

## 🧪 **Run Tests Now**

### **Option 1: Python Test Script**
```bash
cd runninghub_proxy
python test_endpoints_simple.py
```

### **Option 2: Async Test Script**
```bash
cd runninghub_proxy
python test_api_endpoints.py
```

### **Option 3: Manual Testing**
Copy and paste the curl commands above one by one.

## 🔍 **What to Look For**

### **✅ Success Indicators:**
1. **Health endpoint returns 200** with JSON response
2. **Documentation endpoints return 200** with HTML/JSON
3. **Auth endpoints handle requests properly** (200/401/422)
4. **Workflow endpoints require authentication** (401)
5. **Error handling works** (404 for non-existent, 405 for wrong method)
6. **WebSocket connection establishes**

### **❌ Issues to Fix:**
1. **500 errors** - Internal server errors
2. **Connection refused** - Server not running
3. **Timeout errors** - Performance issues
4. **Malformed responses** - JSON parsing errors
5. **Missing endpoints** - 404 for expected routes

## 🛠️ **Troubleshooting**

### **If tests fail:**
1. **Check server logs** in the terminal where server is running
2. **Verify server is running** on port 8973
3. **Check for import errors** in the server startup
4. **Verify dependencies** are installed correctly

### **Common Issues:**
- **Import errors**: Run `uv sync` to install dependencies
- **Port conflicts**: Change port in .env file
- **Permission errors**: Check file permissions
- **Browser automation**: Ensure Playwright is installed

## 📊 **Expected Test Results**

After running the tests, you should see:
- **Health endpoint**: ✅ 200 OK
- **Documentation**: ✅ 200 OK (3/3 endpoints)
- **Authentication**: ✅ Mixed responses (401/422 expected)
- **Workflows**: ✅ 401 Unauthorized (expected without auth)
- **Error handling**: ✅ 404/405 as expected
- **WebSocket**: ✅ Connection established

**🎯 Target: 90%+ success rate with expected error codes**
