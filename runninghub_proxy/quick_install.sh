#!/bin/bash
# Quick installation script for RunningHub Proxy API

set -e  # Exit on any error

echo "🚀 RunningHub Proxy API - Quick Installation"
echo "============================================"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: pyproject.toml not found. Please run this script from the runninghub_proxy directory."
    exit 1
fi

echo "✅ Found pyproject.toml"

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.9+ required. Found: $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install uv if not present
if ! command_exists uv; then
    echo "📦 Installing uv package manager..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    
    # Add uv to PATH for current session
    export PATH="$HOME/.cargo/bin:$PATH"
    
    # Check if uv is now available
    if ! command_exists uv; then
        echo "❌ Error: uv installation failed. Please install manually:"
        echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
        echo "   Then restart your terminal and try again."
        exit 1
    fi
else
    echo "✅ uv is already installed"
fi

echo "📦 Installing Python dependencies..."
uv sync

echo "🎭 Installing Playwright browsers..."
uv run playwright install chromium

echo "📁 Creating necessary directories..."
mkdir -p logs temp downloads

echo "⚙️  Setting up environment configuration..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "📝 You can edit .env to customize settings"
else
    echo "✅ .env file already exists"
fi

echo "🧪 Testing installation..."

# Test basic import
if uv run python -c "import runninghub_proxy" 2>/dev/null; then
    echo "✅ Module import successful"
else
    echo "❌ Module import failed"
    exit 1
fi

# Test configuration
if uv run python -c "from runninghub_proxy.config.settings import settings" 2>/dev/null; then
    echo "✅ Configuration loading successful"
else
    echo "❌ Configuration loading failed"
    exit 1
fi

# Test Playwright
if uv run python -c "from playwright.async_api import async_playwright" 2>/dev/null; then
    echo "✅ Playwright import successful"
else
    echo "❌ Playwright import failed"
    exit 1
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file to configure your settings:"
echo "   nano .env"
echo ""
echo "2. Start the server:"
echo "   uv run python server.py"
echo ""
echo "3. Visit the API documentation:"
echo "   http://localhost:8000/docs"
echo ""
echo "4. Run tests:"
echo "   uv run pytest tests/"
echo ""
echo "💡 Useful commands:"
echo "   uv run python server.py          # Start server"
echo "   uv run pytest tests/             # Run tests"
echo "   uv run python test_stability.py  # Run stability tests"
echo "   make run                         # Start server (if make is available)"
echo "   make test                        # Run tests (if make is available)"
echo ""
echo "🔧 Troubleshooting:"
echo "   If you encounter issues, check INSTALL_DEPENDENCIES.md"
echo ""
