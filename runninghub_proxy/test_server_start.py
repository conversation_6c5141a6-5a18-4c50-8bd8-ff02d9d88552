#!/usr/bin/env python3
"""Simple test script to start the RunningHub Proxy server."""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print(f"Python path: {sys.path[:3]}")
print(f"Current directory: {os.getcwd()}")
print(f"Project root: {project_root}")

try:
    print("Testing imports...")
    
    # Test basic imports
    import fastapi
    print("✅ FastAPI imported")
    
    import playwright
    print("✅ Playwright imported")
    
    import uvicorn
    print("✅ Uvicorn imported")
    
    # Test project imports
    from runninghub_proxy.config.settings import settings
    print("✅ Settings imported")
    print(f"Server config: Host={settings.host}, Port={settings.port}")
    
    from runninghub_proxy.api_utils.app import create_app
    print("✅ App factory imported")
    
    # Create app
    app = create_app()
    print("✅ App created successfully")
    
    # Start server
    print(f"Starting server on {settings.host}:{settings.port}...")
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
