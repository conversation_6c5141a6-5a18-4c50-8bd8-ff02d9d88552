# RunningHub Proxy API Configuration

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# API Configuration
API_TITLE="RunningHub Proxy API"
API_VERSION="1.0.0"
API_DESCRIPTION="Web automation proxy for RunningHub.cn workflows"

# Authentication (optional - if not set, no API key required)
API_KEY=your-secret-api-key-here
SESSION_TIMEOUT=3600

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
BROWSER_USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# RunningHub Configuration
RUNNINGHUB_BASE_URL=https://www.runninghub.cn
RUNNINGHUB_LOGIN_TIMEOUT=300000

# Workflow Configuration
WORKFLOW_EXECUTION_TIMEOUT=600000
MAX_CONCURRENT_WORKFLOWS=5

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/runninghub_proxy.log
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# WebSocket Configuration
WEBSOCKET_HEARTBEAT=30
WEBSOCKET_MAX_CONNECTIONS=100

# Storage Configuration
TEMP_DIR=temp
DOWNLOADS_DIR=downloads

# Performance Configuration
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1.0
