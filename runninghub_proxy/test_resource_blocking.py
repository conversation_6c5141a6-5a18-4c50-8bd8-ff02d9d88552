#!/usr/bin/env python3
"""Test resource blocking functionality."""

import asyncio
import sys
from playwright.async_api import async_playwright
from collections import defaultdict


async def test_resource_blocking():
    """Test resource blocking with detailed logging."""
    print("🚫 Testing Resource Blocking (Data Saving Mode)")
    print("=" * 60)
    
    # Track blocked and allowed resources
    blocked_resources = defaultdict(int)
    allowed_resources = defaultdict(int)
    total_data_saved = 0
    
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=False,
            args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--start-maximized",
            ],
            slow_mo=200
        )
        
        try:
            # Create context
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # Set up resource blocking
            async def handle_route(route, request):
                """Handle resource requests with blocking."""
                url = request.url
                resource_type = request.resource_type
                
                # Check if we're on a workflow page
                page_url = route.request.frame.page.url if route.request.frame and route.request.frame.page else ""
                is_workflow_page = (
                    "workflow" in page_url.lower() or
                    "comfyui" in page_url.lower() or
                    "/app" in page_url.lower() or
                    "editor" in page_url.lower()
                )
                
                # Block images and videos on non-workflow pages
                if not is_workflow_page and resource_type in ["image", "media", "font"]:
                    blocked_resources[resource_type] += 1
                    total_data_saved += 50  # Estimate 50KB per blocked resource
                    print(f"🚫 Blocked {resource_type}: {url[:80]}...")
                    await route.abort()
                    return
                
                # Block ads and tracking
                blocked_patterns = [
                    "google-analytics", "googletagmanager", "facebook.com/tr",
                    "doubleclick.net", "googlesyndication", "analytics",
                    "tracking", "metrics", "telemetry", "beacon", "pixel"
                ]
                
                for pattern in blocked_patterns:
                    if pattern in url.lower():
                        blocked_resources["ads/tracking"] += 1
                        total_data_saved += 10  # Estimate 10KB per blocked script
                        print(f"🚫 Blocked ads/tracking: {url[:80]}...")
                        await route.abort()
                        return
                
                # Allow the request
                allowed_resources[resource_type] += 1
                await route.continue_()
            
            # Set up route handler
            await context.route("**/*", handle_route)
            
            # Create page
            page = await context.new_page()
            
            print("🌐 Testing on RunningHub.cn (non-workflow page)")
            print("-" * 40)
            
            # Navigate to RunningHub.cn
            try:
                await page.goto("https://www.runninghub.cn", wait_until="domcontentloaded", timeout=30000)
                print(f"✅ Loaded: {page.url}")
                
                # Wait a bit for resources to load
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"⚠️  Navigation error: {e}")
                print("Testing with Google instead...")
                await page.goto("https://www.google.com", wait_until="domcontentloaded", timeout=30000)
                await asyncio.sleep(3)
            
            print("\n📊 Resource Blocking Results:")
            print("-" * 40)
            
            print("🚫 Blocked Resources:")
            for resource_type, count in blocked_resources.items():
                print(f"   {resource_type}: {count} requests")
            
            print("\n✅ Allowed Resources:")
            for resource_type, count in allowed_resources.items():
                print(f"   {resource_type}: {count} requests")
            
            total_blocked = sum(blocked_resources.values())
            total_allowed = sum(allowed_resources.values())
            total_requests = total_blocked + total_allowed
            
            if total_requests > 0:
                block_percentage = (total_blocked / total_requests) * 100
                print(f"\n📈 Statistics:")
                print(f"   Total Requests: {total_requests}")
                print(f"   Blocked: {total_blocked} ({block_percentage:.1f}%)")
                print(f"   Allowed: {total_allowed} ({100-block_percentage:.1f}%)")
                print(f"   Estimated Data Saved: ~{total_data_saved}KB")
            
            # Test workflow page (should allow images)
            print(f"\n🔧 Testing workflow page behavior...")
            print("-" * 40)
            
            # Reset counters
            workflow_blocked = defaultdict(int)
            workflow_allowed = defaultdict(int)
            
            async def workflow_route_handler(route, request):
                """Route handler for workflow page test."""
                url = request.url
                resource_type = request.resource_type
                
                page_url = route.request.frame.page.url if route.request.frame and route.request.frame.page else ""
                is_workflow_page = "workflow" in page_url.lower()
                
                if not is_workflow_page and resource_type in ["image", "media"]:
                    workflow_blocked[resource_type] += 1
                    await route.abort()
                    return
                
                workflow_allowed[resource_type] += 1
                await route.continue_()
            
            # Create new page for workflow test
            workflow_page = await context.new_page()
            await workflow_page.route("**/*", workflow_route_handler)
            
            # Simulate workflow page URL
            await workflow_page.goto("data:text/html,<html><head><title>Workflow Test</title></head><body><h1>Simulated Workflow Page</h1><img src='https://via.placeholder.com/150' alt='test'></body></html>")
            await asyncio.sleep(2)
            
            print("🔧 Workflow Page Results:")
            print(f"   Images should be ALLOWED on workflow pages")
            print(f"   Blocked: {sum(workflow_blocked.values())}")
            print(f"   Allowed: {sum(workflow_allowed.values())}")
            
        finally:
            await browser.close()
    
    print("\n" + "=" * 60)
    print("🎯 RESOURCE BLOCKING TEST SUMMARY")
    print("=" * 60)
    
    if total_blocked > 0:
        print("✅ Resource blocking is working!")
        print(f"📊 Blocked {total_blocked} unnecessary requests")
        print(f"💾 Estimated data saved: ~{total_data_saved}KB")
        print("🚀 Page loading should be faster")
        print("💰 Data usage significantly reduced")
    else:
        print("⚠️  No resources were blocked")
        print("Check if the blocking logic is working correctly")
    
    print("\n🔧 Configuration:")
    print("   BROWSER_BLOCK_IMAGES=true  (blocks images on non-workflow pages)")
    print("   BROWSER_BLOCK_VIDEOS=true  (blocks videos on non-workflow pages)")
    print("   BROWSER_BLOCK_ADS=true     (blocks ads and tracking)")
    print("\n📝 Note: Images/videos are still allowed on workflow pages!")
    
    return total_blocked > 0


async def main():
    """Main test function."""
    try:
        success = await test_resource_blocking()
        return success
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
