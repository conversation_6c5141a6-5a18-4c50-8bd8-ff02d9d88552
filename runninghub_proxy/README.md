# RunningHub Proxy API

A web automation proxy that converts RunningHub.cn's workflow interface into a programmatic REST API, following the architectural patterns from the AI Studio proxy project.

## Overview

This project provides a RESTful API interface for RunningHub.cn workflows, enabling programmatic access to:
- Authentication management
- Workflow schema discovery
- Workflow execution with streaming progress
- Execution history tracking

## Architecture

The project follows a modular architecture with clear separation of concerns:

- **api_utils/**: FastAPI application, routing, request processing, authentication
- **browser_utils/**: Playwright browser automation, page control, operations
- **config/**: Centralized configuration management with environment variables
- **models/**: Pydantic data models and exception handling
- **logging_utils/**: Comprehensive logging with WebSocket streaming

## API Endpoints

### Authentication
- `POST /auth/login` - Handle manual authentication flow

### Workflow Management
- `GET /workflows/{workflow_id}/schema` - Extract workflow API schema
- `POST /workflows/{workflow_id}/execute` - Execute workflow with streaming progress
- `GET /workflows/history` - Retrieve execution history

## Technology Stack

- **FastAPI**: Modern Python web framework
- **Playwright**: Browser automation
- **Pydantic**: Data validation and serialization
- **AsyncIO**: Asynchronous programming support
- **uv**: Fast Python package manager

## Development Status

🚧 **In Development** - Phase 2: Foundation Setup
