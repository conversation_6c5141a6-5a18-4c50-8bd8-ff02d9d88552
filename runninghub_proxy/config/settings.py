"""Configuration settings for RunningHub Proxy API."""

from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    debug: bool = Field(default=False, description="Debug mode", env="APP_DEBUG")
    
    # API Configuration
    api_title: str = Field(default="RunningHub Proxy API", description="API title")
    api_version: str = Field(default="1.0.0", description="API version")
    api_description: str = Field(
        default="Web automation proxy for RunningHub.cn workflows",
        description="API description"
    )
    
    # Authentication
    api_key: Optional[str] = Field(default=None, description="API key for authentication")
    session_timeout: int = Field(default=3600, description="Session timeout in seconds")
    
    # Browser Configuration
    browser_headless: bool = Field(default=True, description="Run browser in headless mode")
    browser_timeout: int = Field(default=30000, description="Browser timeout in milliseconds")
    browser_user_agent: Optional[str] = Field(default=None, description="Custom user agent")
    browser_block_images: bool = Field(default=True, description="Block images on non-workflow pages to save data")
    browser_block_videos: bool = Field(default=True, description="Block videos on non-workflow pages to save data")
    browser_block_ads: bool = Field(default=True, description="Block ads and tracking scripts")

    # Debug Configuration
    browser_verbose_logging: bool = Field(default=False, description="Enable verbose browser logging")
    network_logging: bool = Field(default=False, description="Enable network request/response logging")
    console_logging: bool = Field(default=False, description="Enable browser console logging")
    playwright_debug: Optional[str] = Field(default=None, description="Playwright debug flags", env="PLAYWRIGHT_DEBUG")
    playwright_debug_mode: bool = Field(default=False, description="Enable Playwright debug mode", env="PWDEBUG")
    
    # RunningHub Configuration
    runninghub_base_url: str = Field(
        default="https://www.runninghub.cn",
        description="RunningHub base URL"
    )
    runninghub_login_timeout: int = Field(
        default=300000,
        description="Login timeout in milliseconds"
    )
    
    # Workflow Configuration
    workflow_execution_timeout: int = Field(
        default=600000,
        description="Workflow execution timeout in milliseconds"
    )
    max_concurrent_workflows: int = Field(
        default=5,
        description="Maximum concurrent workflow executions"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default="logs/runninghub_proxy.log", description="Log file path")
    log_rotation: str = Field(default="1 day", description="Log rotation interval")
    log_retention: str = Field(default="30 days", description="Log retention period")
    
    # WebSocket Configuration
    websocket_heartbeat: int = Field(default=30, description="WebSocket heartbeat interval")
    websocket_max_connections: int = Field(default=100, description="Max WebSocket connections")
    
    # Storage Configuration
    temp_dir: str = Field(default="temp", description="Temporary files directory")
    downloads_dir: str = Field(default="downloads", description="Downloads directory")
    
    # Performance Configuration
    request_timeout: int = Field(default=30, description="HTTP request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Retry delay in seconds")

    @field_validator('debug', mode='before')
    @classmethod
    def parse_debug(cls, v):
        """Parse debug field, handling string values that should be booleans."""
        if isinstance(v, str):
            # Handle Playwright debug strings by returning False
            if 'pw:' in v.lower() or 'browser' in v.lower():
                return False
            # Handle normal boolean string values
            return v.lower() in ('true', '1', 'yes', 'on')
        return bool(v)


# Global settings instance
settings = Settings()
