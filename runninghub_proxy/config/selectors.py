"""CSS selectors for RunningHub.cn interface elements."""

from typing import Dict, Any


class RunningHubSelectors:
    """CSS selectors for RunningHub.cn interface elements."""
    
    # Authentication selectors - Updated for better stability and strict mode compliance
    LOGIN_BUTTON = "button:has-text('登 录'):visible, button:has-text('登录'):visible, .login-btn"
    LOGIN_MODAL = ".ant-modal.loginAndRegisterModal, .cn-login-wrap"
    LOGIN_MODAL_CLOSE = ".ant-modal-close, .modal-close, button[aria-label='Close']"
    
    # Login form selectors - More specific and stable
    PHONE_INPUT = "input[placeholder*='手机号'], input[placeholder*='phone'], input[type='tel']"
    SEND_SMS_BUTTON = ".smsBtn, button:has-text('发送验证码')"
    SMS_CODE_INPUT = "input[placeholder*='验证码'], input[placeholder*='code']"
    PASSWORD_INPUT = "input[type='password'], input[placeholder*='密码']"
    
    # Login tab selectors - Multiple fallbacks
    WECHAT_LOGIN_TAB = "text=微信登录, text=微信扫码登录, button:has-text('微信')"
    PHONE_LOGIN_TAB = "text=验证码登录, button:has-text('验证码登录')"
    PASSWORD_LOGIN_TAB = "text=密码登录, button:has-text('密码')"
    
    # Login action buttons
    LOGIN_SUBMIT = "button:has-text('立即登录'), button:has-text('登录'), .login-submit"
    SEND_CODE_BUTTON = "button:has-text('发送验证码'), button:has-text('获取验证码'), .send-code"
    
    # Error and success indicators
    ERROR_MESSAGE = ".ant-message-error, .error-message, .login-error, [class*='error']"
    SUCCESS_MESSAGE = ".ant-message-success, .success-message, [class*='success']"
    
    # User profile indicators (for login success detection)
    USER_AVATAR = ".user-avatar, .avatar, [class*='avatar']"
    USER_MENU = ".user-menu, .profile-menu, [class*='user-menu']"
    LOGOUT_BUTTON = "text=退出登录, text=登出, button:has-text('退出')"
    
    # Workflow page selectors
    WORKFLOW_TITLE = ".workflow-title, h1, .title"
    RUN_WORKFLOW_BUTTON = "button:has-text('运行工作流'), button:has-text('运行'), .run-btn"
    DOWNLOAD_BUTTON = "[title='下载'], button:has-text('下载'), .download-btn"
    WORKFLOW_DESCRIPTION = ".workflow-description, .description"
    WORKFLOW_AUTHOR = ".workflow-author, .author"
    WORKFLOW_TAGS = ".workflow-tags, .tags"
    
    # Workflow execution interface selectors
    COMFYUI_IFRAME = "iframe[title='comfyUI'], iframe[src*='comfy'], iframe"
    COMFYUI_MAIN = "main, .main-content"
    WORKFLOW_CANVAS = ".workflow-canvas, .react-flow, .canvas"
    NODE_LIBRARY_BUTTON = "button:has-text('Node Library'), button:has-text('节点库')"
    NODES_MAP_BUTTON = "button:has-text('Nodes Map'), button:has-text('节点图')"
    PROMPT_LIBRARY_BUTTON = "button:has-text('Prompt Library'), button:has-text('提示库')"
    
    # Context menu selectors (for API export)
    CONTEXT_MENU = ".ant-dropdown-menu, .context-menu, [role='menu']"
    EXPORT_WORKFLOW_MENU = "text=导出工作流, text=Export Workflow"
    EXPORT_WORKFLOW_API_MENU = "text=导出工作流API, text=Export API"
    
    # Execution controls
    RUN_BUTTON = "button:has-text('运行'), button:has-text('Run'), .run-btn"
    SAVE_BUTTON = "button:has-text('保存'), button:has-text('Save'), .save-btn"
    SAVE_DROPDOWN = ".ant-dropdown-trigger, .save-dropdown"
    PUBLISH_BUTTON = "button:has-text('发布'), button:has-text('Publish'), .publish-btn"
    
    # Task list selectors
    TASK_LIST = ".task-list, .tasks"
    TASK_ITEM = ".task-item, .task"
    TASK_STATUS = ".task-status, .status"
    TASK_PROGRESS = ".task-progress, .progress"

# Timeout configurations for different operations
SELECTOR_TIMEOUTS = {
    "default": 10000,           # 10 seconds for most operations
    "page_load": 30000,         # 30 seconds for page loading
    "login": 60000,             # 60 seconds for login operations
    "workflow_execution": 300000, # 5 minutes for workflow execution
    "element_wait": 5000,       # 5 seconds for element waiting
    "quick_check": 1000,        # 1 second for quick checks
    "user_interaction": 90000,  # 90 seconds for user interaction
}

# Wait conditions for different scenarios
WAIT_CONDITIONS = {
    "visible": "visible",
    "hidden": "hidden",
    "attached": "attached",
    "detached": "detached",
    "stable": "stable",
}

# Retry configurations
RETRY_CONFIG = {
    "max_attempts": 3,
    "base_delay": 1.0,
    "exponential_backoff": True,
    "max_delay": 10.0,
}

# CAPTCHA detection patterns
CAPTCHA_PATTERNS = [
    "text=拖动滑块完成拼图",      # Drag slider to complete puzzle
    "text=点击完成验证",          # Click to complete verification
    "text=请完成安全验证",        # Please complete security verification
    "text=人机验证",              # Human verification
    "[class*='captcha']",         # Generic captcha class
    "[class*='verify']",          # Generic verify class
    "[class*='slider']",          # Slider captcha
    "[id*='captcha']",           # Captcha ID
    "[data-testid*='captcha']",  # Captcha test ID
]

# Login success indicators
LOGIN_SUCCESS_INDICATORS = [
    # User profile elements
    ".user-avatar",
    ".user-profile", 
    ".user-menu",
    "[class*='avatar']",
    "[class*='user-menu']",
    # Navigation elements that appear only when logged in
    "text=个人中心",              # Personal Center
    "text=工作台",                # Workspace
    "text=退出登录",              # Logout
    "text=我的工作流",            # My Workflows
    # URL patterns
    "/dashboard",
    "/workspace", 
    "/profile",
    "/user",
]

# Error message patterns
ERROR_MESSAGE_PATTERNS = [
    ".ant-message-error",
    ".error-message",
    ".login-error",
    "[class*='error']",
    "text=登录失败",              # Login failed
    "text=验证码错误",            # Verification code error
    "text=密码错误",              # Password error
    "text=手机号格式错误",        # Phone number format error
]


# Timeout configurations for different elements (increased for debugging)
SELECTOR_TIMEOUTS = {
    "default": 30000,        # 30 seconds (increased from 10)
    "page_load": 120000,     # 2 minutes for page loading (increased from 60)
    "login": 60000,          # 1 minute for login operations (increased from 30)
    "workflow_load": 120000, # 2 minutes for workflow loading (increased from 60)
    "execution": 300000,     # 5 minutes for workflow execution
    "file_download": 60000,  # 1 minute for file downloads (increased from 30)
    "navigation": 90000,     # 1.5 minutes for navigation (new)
    "element_wait": 45000,   # 45 seconds for element waiting (new)
}

# Wait conditions for different operations
WAIT_CONDITIONS = {
    "page_load": "networkidle",
    "element_visible": "visible",
    "element_hidden": "hidden",
    "element_attached": "attached",
    "element_detached": "detached",
}
