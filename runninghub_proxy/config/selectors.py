"""CSS selectors for RunningHub.cn interface elements."""

from typing import Dict, Any


class RunningHubSelectors:
    """CSS selectors for RunningHub.cn interface elements."""
    
    # Authentication selectors
    LOGIN_BUTTON = "button.login-btn"
    LOGIN_MODAL = ".ant-modal"
    LOGIN_MODAL_CLOSE = ".ant-modal-close"
    PHONE_INPUT = "input[placeholder*='手机号']"
    SMS_CODE_INPUT = "input[placeholder*='验证码']"
    PASSWORD_INPUT = "input[type='password']"
    WECHAT_LOGIN_TAB = "text=微信登录"
    PHONE_LOGIN_TAB = "text=手机登录"
    LOGIN_SUBMIT = "button:has-text('登录')"
    
    # Workflow page selectors
    WORKFLOW_TITLE = ".workflow-title, h1"
    RUN_WORKFLOW_BUTTON = "button:has-text('运行工作流')"
    DOWNLOAD_BUTTON = "[title='下载']"
    WORKFLOW_DESCRIPTION = ".workflow-description"
    WORKFLOW_AUTHOR = ".workflow-author"
    WORKFLOW_TAGS = ".workflow-tags"
    
    # Workflow execution interface selectors
    COMFYUI_IFRAME = "iframe[title='comfyUI']"
    COMFYUI_MAIN = "main"
    WORKFLOW_CANVAS = ".workflow-canvas, .react-flow"
    NODE_LIBRARY_BUTTON = "button:has-text('Node Library')"
    NODES_MAP_BUTTON = "button:has-text('Nodes Map')"
    PROMPT_LIBRARY_BUTTON = "button:has-text('Prompt Library')"
    
    # Context menu selectors (for API export)
    CONTEXT_MENU = ".ant-dropdown-menu"
    EXPORT_WORKFLOW_MENU = "text=导出工作流"
    EXPORT_WORKFLOW_API_MENU = "text=导出工作流API"
    
    # Execution controls
    RUN_BUTTON = "button:has-text('运行')"
    SAVE_BUTTON = "button:has-text('保存')"
    SAVE_DROPDOWN = ".ant-dropdown-trigger"
    PUBLISH_BUTTON = "button:has-text('发布')"
    
    # Task list selectors
    TASK_LIST = ".task-list"
    TASK_ITEM = ".task-item"
    TASK_STATUS = ".task-status"
    TASK_PROGRESS = ".task-progress"
    TASK_DELETE = "button:has-text('删除')"
    TASK_SHARE = "button:has-text('分享')"
    TASK_REGENERATE = "button:has-text('再次生成')"
    
    # Workflow result selectors
    WORKFLOW_RESULT_WRAP = ".workflow-result-wrap"
    RESULT_DRAWER = ".ant-drawer"
    RESULT_IMAGES = ".result-images img"
    RESULT_VIDEOS = ".result-videos video"
    RESULT_FILES = ".result-files a"
    PROGRESS_BAR = ".ant-progress"
    PROGRESS_PERCENT = ".ant-progress-text"
    
    # Form input selectors
    TEXT_INPUT = "input[type='text']"
    TEXTAREA = "textarea"
    NUMBER_INPUT = "input[type='number']"
    SELECT_DROPDOWN = ".ant-select"
    SELECT_OPTION = ".ant-select-item"
    CHECKBOX = ".ant-checkbox"
    RADIO = ".ant-radio"
    SLIDER = ".ant-slider"
    
    # File upload selectors
    FILE_UPLOAD = ".ant-upload"
    FILE_UPLOAD_INPUT = "input[type='file']"
    FILE_UPLOAD_BUTTON = ".ant-upload-btn"
    FILE_LIST = ".ant-upload-list"
    
    # Navigation selectors
    BREADCRUMB = ".ant-breadcrumb"
    PAGINATION = ".ant-pagination"
    BACK_BUTTON = "button:has-text('返回')"
    
    # Loading and status selectors
    LOADING_SPINNER = ".ant-spin"
    LOADING_MASK = ".ant-spin-container"
    SUCCESS_MESSAGE = ".ant-message-success"
    ERROR_MESSAGE = ".ant-message-error"
    WARNING_MESSAGE = ".ant-message-warning"
    
    # Workflow node selectors (inside ComfyUI iframe)
    WORKFLOW_NODE = ".workflow-node"
    NODE_INPUT = ".node-input"
    NODE_OUTPUT = ".node-output"
    NODE_TITLE = ".node-title"
    NODE_WIDGET = ".node-widget"
    
    # Zoom and view controls
    ZOOM_IN_BUTTON = "button:has-text('Zoom In')"
    ZOOM_OUT_BUTTON = "button:has-text('Zoom Out')"
    FIT_VIEW_BUTTON = "button:has-text('Fit View')"
    SELECT_MODE_BUTTON = "button:has-text('Select Mode')"


# Timeout configurations for different elements (increased for debugging)
SELECTOR_TIMEOUTS = {
    "default": 30000,        # 30 seconds (increased from 10)
    "page_load": 120000,     # 2 minutes for page loading (increased from 60)
    "login": 60000,          # 1 minute for login operations (increased from 30)
    "workflow_load": 120000, # 2 minutes for workflow loading (increased from 60)
    "execution": 300000,     # 5 minutes for workflow execution
    "file_download": 60000,  # 1 minute for file downloads (increased from 30)
    "navigation": 90000,     # 1.5 minutes for navigation (new)
    "element_wait": 45000,   # 45 seconds for element waiting (new)
}

# Wait conditions for different operations
WAIT_CONDITIONS = {
    "page_load": "networkidle",
    "element_visible": "visible",
    "element_hidden": "hidden",
    "element_attached": "attached",
    "element_detached": "detached",
}
