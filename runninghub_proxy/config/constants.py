"""Constants for RunningHub Proxy API."""

from enum import Enum
from typing import Dict, Any


class WorkflowStatus(str, Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AuthStatus(str, Enum):
    """Authentication status."""
    UNAUTHENTICATED = "unauthenticated"
    AUTHENTICATING = "authenticating"
    AUTHENTICATED = "authenticated"
    EXPIRED = "expired"
    FAILED = "failed"


class ResponseTier(str, Enum):
    """Response tier for three-tier strategy."""
    DIRECT_API = "direct_api"
    BROWSER_AUTOMATION = "browser_automation"
    FALLBACK = "fallback"


# HTTP Status Codes
HTTP_STATUS = {
    "OK": 200,
    "CREATED": 201,
    "ACCEPTED": 202,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "CONFLICT": 409,
    "UNPROCESSABLE_ENTITY": 422,
    "INTERNAL_SERVER_ERROR": 500,
    "SERVICE_UNAVAILABLE": 503,
}

# API Response Messages
API_MESSAGES = {
    "AUTH_SUCCESS": "Authentication successful",
    "AUTH_REQUIRED": "Authentication required",
    "AUTH_FAILED": "Authentication failed",
    "WORKFLOW_NOT_FOUND": "Workflow not found",
    "WORKFLOW_EXECUTION_STARTED": "Workflow execution started",
    "WORKFLOW_EXECUTION_COMPLETED": "Workflow execution completed",
    "WORKFLOW_EXECUTION_FAILED": "Workflow execution failed",
    "SCHEMA_EXTRACTED": "Workflow schema extracted successfully",
    "INVALID_REQUEST": "Invalid request parameters",
    "SERVER_ERROR": "Internal server error",
}

# Browser Configuration
BROWSER_CONFIG = {
    "viewport": {"width": 1920, "height": 1080},
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "locale": "zh-CN",
    "timezone_id": "Asia/Shanghai",
}

# File Extensions
ALLOWED_EXTENSIONS = {
    "json": [".json"],
    "image": [".png", ".jpg", ".jpeg", ".gif", ".webp"],
    "video": [".mp4", ".avi", ".mov", ".webm"],
}

# Workflow Configuration
WORKFLOW_CONFIG = {
    "max_nodes": 1000,
    "max_execution_time": 3600,  # 1 hour
    "progress_update_interval": 1.0,  # seconds
    "cleanup_delay": 300,  # 5 minutes
}

# WebSocket Events
WEBSOCKET_EVENTS = {
    "AUTH_STATUS": "auth_status",
    "WORKFLOW_PROGRESS": "workflow_progress",
    "WORKFLOW_RESULT": "workflow_result",
    "ERROR": "error",
    "LOG": "log",
}

# Default Values
DEFAULTS = {
    "workflow_timeout": 600,  # 10 minutes
    "page_load_timeout": 30,  # 30 seconds
    "element_timeout": 10,  # 10 seconds
    "retry_attempts": 3,
    "retry_delay": 1.0,
}
