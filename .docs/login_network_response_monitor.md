```mermaid
flowchart TD
    A[Start: _wait_for_user_login_completion] --> B[Initialize monitoring variables]
    B --> C[Set start_time and check_interval=2s]
    C --> D[Enter main monitoring loop]
    
    D --> E{Time < timeout_seconds?}
    E -->|No| P[Timeout reached]
    E -->|Yes| F[Check network monitoring status]
    
    F --> G{user_info_response exists?}
    G -->|Yes| H[✅ Authentication SUCCESS]
    H --> I[Return AuthResponse: AUTHENTICATED]
    
    G -->|No| J{network_error exists?}
    J -->|Yes| K[❌ Network error detected]
    K --> L[Return AuthResponse: FAILED]
    
    J -->|No| M[Check UI fallback indicators]
    M --> N{CAPTCHA detected?}
    N -->|Yes| O[Set captcha_detected = true]
    O --> R[Check for error messages in UI]
    
    N -->|No| R[Check for error messages in UI]
    R --> S{Error message visible?}
    S -->|Yes| T[Extract error text]
    T --> U[Return AuthResponse: FAILED]
    
    S -->|No| V{Log progress interval reached?}
    V -->|Yes| W[Log current status message]
    W --> X[Update last_log_time]
    X --> Y[Sleep 2 seconds]
    
    V -->|No| Y[Sleep 2 seconds]
    Y --> D
    
    P --> Z[Handle network timeout]
    Z --> AA{Final status check}
    AA -->|user_info_response| H
    AA -->|network_error| K
    AA -->|auth_request_detected| BB[Return TIMEOUT: Request detected but no response]
    AA -->|No activity| CC[Return TIMEOUT: No login attempt detected]
    
    subgraph "Network Monitoring (Parallel)"
        NM1[Setup network listeners] --> NM2[Monitor /uc/getUserInfo requests]
        NM2 --> NM3{Response received?}
        NM3 -->|HTTP 200 + code:0| NM4[Set user_info_response]
        NM3 -->|HTTP error| NM5[Set network_error]
        NM3 -->|Request failed| NM6[Set network_error]
        
        NM4 --> NM7[auth_request_detected = true]
        NM5 --> NM7
        NM6 --> NM7
    end
    
    subgraph "Status Messages"
        SM1[auth_request_detected: 'getUserInfo request detected']
        SM2[captcha_detected: 'CAPTCHA challenge active']
        SM3[default: 'waiting for user input']
    end
    
    style H fill:#d4edda,stroke:#155724
    style K fill:#f8d7da,stroke:#721c24
    style U fill:#f8d7da,stroke:#721c24
    style BB fill:#fff3cd,stroke:#856404
    style CC fill:#fff3cd,stroke:#856404
    style NM4 fill:#d1ecf1,stroke:#0c5460
    style NM5 fill:#f8d7da,stroke:#721c24
    style NM6 fill:#f8d7da,stroke:#721c24
```
