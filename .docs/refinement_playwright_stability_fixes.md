# Playwright Automation Stability Fixes & Code Changes

## Overview

This document details all the critical stability improvements made to the Playwright automation code, including the specific code changes for each fix.

## 1. Missing Configuration Constants

### **Issue**
The code imported `SELECTOR_TIMEOUTS`, `WAIT_CONDITIONS`, and other constants that were not defined, causing import errors.

### **Fix Applied**
Added comprehensive configuration constants to `runninghub_proxy/config/selectors.py`:

```python
# Timeout configurations for different operations
SELECTOR_TIMEOUTS = {
    "default": 10000,           # 10 seconds for most operations
    "page_load": 30000,         # 30 seconds for page loading
    "login": 60000,             # 60 seconds for login operations
    "workflow_execution": 300000, # 5 minutes for workflow execution
    "element_wait": 5000,       # 5 seconds for element waiting
    "quick_check": 1000,        # 1 second for quick checks
    "user_interaction": 90000,  # 90 seconds for user interaction
}

# Wait conditions for different scenarios
WAIT_CONDITIONS = {
    "visible": "visible",
    "hidden": "hidden",
    "attached": "attached",
    "detached": "detached",
    "stable": "stable",
}

# Retry configurations
RETRY_CONFIG = {
    "max_attempts": 3,
    "base_delay": 1.0,
    "exponential_backoff": True,
    "max_delay": 10.0,
}

# CAPTCHA detection patterns
CAPTCHA_PATTERNS = [
    "text=拖动滑块完成拼图",      # Drag slider to complete puzzle
    "text=点击完成验证",          # Click to complete verification
    "text=请完成安全验证",        # Please complete security verification
    "text=人机验证",              # Human verification
    "[class*='captcha']",         # Generic captcha class
    "[class*='verify']",          # Generic verify class
    "[class*='slider']",          # Slider captcha
    "[id*='captcha']",           # Captcha ID
    "[data-testid*='captcha']",  # Captcha test ID
]

# Login success indicators
LOGIN_SUCCESS_INDICATORS = [
    # User profile elements
    ".user-avatar",
    ".user-profile", 
    ".user-menu",
    "[class*='avatar']",
    "[class*='user-menu']",
    # Navigation elements that appear only when logged in
    "text=个人中心",              # Personal Center
    "text=工作台",                # Workspace
    "text=退出登录",              # Logout
    "text=我的工作流",            # My Workflows
    # URL patterns
    "/dashboard",
    "/workspace", 
    "/profile",
    "/user",
]

# Error message patterns
ERROR_MESSAGE_PATTERNS = [
    ".ant-message-error",
    ".error-message",
    ".login-error",
    "[class*='error']",
    "text=登录失败",              # Login failed
    "text=验证码错误",            # Verification code error
    "text=密码错误",              # Password error
    "text=手机号格式错误",        # Phone number format error
]
```

### **Import Updates**
Updated imports in `runninghub_proxy/browser_utils/auth_handler.py`:

```python
# Before
from ..config.selectors import RunningHubSelectors, SELECTOR_TIMEOUTS

# After
from ..config.selectors import (
    RunningHubSelectors, 
    SELECTOR_TIMEOUTS, 
    CAPTCHA_PATTERNS,
    LOGIN_SUCCESS_INDICATORS,
    ERROR_MESSAGE_PATTERNS
)
```

## 2. Fragile CSS Selectors

### **Issue**
Single CSS selectors could break if the UI changes, causing automation failures.

### **Fix Applied**
Implemented fallback selector chains in `runninghub_proxy/config/selectors.py`:

```python
class RunningHubSelectors:
    """CSS selectors for RunningHub.cn interface elements."""
    
    # Before (fragile)
    # LOGIN_BUTTON = "button.login-btn"
    
    # After (robust with fallbacks)
    LOGIN_BUTTON = "button:has-text('登 录'), button:has-text('登录'), .login-btn"
    LOGIN_MODAL = ".ant-modal, [role='dialog']"
    LOGIN_MODAL_CLOSE = ".ant-modal-close, .modal-close, button[aria-label='Close']"
    
    # Login form selectors - More specific and stable
    PHONE_INPUT = "input[placeholder*='手机号'], input[placeholder*='phone'], input[type='tel']"
    SMS_CODE_INPUT = "input[placeholder*='验证码'], input[placeholder*='code']"
    PASSWORD_INPUT = "input[type='password'], input[placeholder*='密码']"
    
    # Login tab selectors - Multiple fallbacks
    WECHAT_LOGIN_TAB = "text=微信登录, text=微信扫码登录, button:has-text('微信')"
    PHONE_LOGIN_TAB = "text=验证码登录, text=手机登录, button:has-text('验证码')"
    PASSWORD_LOGIN_TAB = "text=密码登录, button:has-text('密码')"
    
    # Login action buttons
    LOGIN_SUBMIT = "button:has-text('立即登录'), button:has-text('登录'), .login-submit"
    SEND_CODE_BUTTON = "button:has-text('发送验证码'), button:has-text('获取验证码'), .send-code"
    
    # Error and success indicators
    ERROR_MESSAGE = ".ant-message-error, .error-message, .login-error, [class*='error']"
    SUCCESS_MESSAGE = ".ant-message-success, .success-message, [class*='success']"
    
    # User profile indicators (for login success detection)
    USER_AVATAR = ".user-avatar, .avatar, [class*='avatar']"
    USER_MENU = ".user-menu, .profile-menu, [class*='user-menu']"
    LOGOUT_BUTTON = "text=退出登录, text=登出, button:has-text('退出')"
```

## 3. Improved Element Interaction

### **Issue**
Element clicking and waiting could fail on the first attempt without proper retry mechanisms.

### **Fix Applied**
Enhanced `click_element` method in `runninghub_proxy/browser_utils/page_operations.py`:

```python
async def click_element(
    self, 
    selector: str, 
    timeout: Optional[int] = None,
    force: bool = False
) -> bool:
    """Click on element with retry logic and improved stability."""
    timeout = timeout or SELECTOR_TIMEOUTS["default"]
    
    try:
        with PerformanceLogger(f"click_element({selector})"):
            self.logger.debug(f"Clicking element: {selector}")
            
            # Handle multiple selectors (comma-separated)
            selectors = [s.strip() for s in selector.split(',')]
            
            locator = None
            for sel in selectors:
                try:
                    # Wait for element to be clickable
                    locator = await self.wait_for_element(sel, timeout, "visible")
                    
                    # Check if element is actually clickable
                    if await locator.is_enabled():
                        break
                except ElementNotFoundError:
                    continue
            
            if not locator:
                raise ElementNotFoundError(selector)
            
            # Scroll into view if needed
            try:
                await locator.scroll_into_view_if_needed()
                await asyncio.sleep(0.5)  # Brief pause after scrolling
            except Exception as e:
                self.logger.debug(f"Could not scroll to element: {e}")
            
            # Click with retry and different strategies
            for attempt in range(settings.max_retries):
                try:
                    # Strategy 1: Normal click
                    if attempt == 0:
                        await locator.click(timeout=5000)
                    # Strategy 2: Force click
                    elif attempt == 1:
                        await locator.click(force=True, timeout=5000)
                    # Strategy 3: JavaScript click
                    else:
                        await locator.evaluate("element => element.click()")
                    
                    self.logger.debug(f"Successfully clicked: {selector}")
                    return True
                    
                except Exception as e:
                    if attempt == settings.max_retries - 1:
                        self.logger.error(f"All click attempts failed for {selector}: {e}")
                        raise
                    
                    self.logger.warning(f"Click attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(settings.retry_delay)
            
            return False
            
    except ElementNotFoundError:
        raise
    except Exception as e:
        raise BrowserAutomationError(f"Error clicking element {selector}: {str(e)}")
```

Enhanced `wait_for_element` method:

```python
async def wait_for_element(
    self, 
    selector: str, 
    timeout: Optional[int] = None,
    state: str = "visible"
) -> Locator:
    """Wait for element to appear with specified state and improved selector handling."""
    timeout = timeout or SELECTOR_TIMEOUTS["default"]
    
    try:
        self.logger.debug(f"Waiting for element: {selector} (state: {state})")
        
        # Handle multiple selectors (comma-separated fallbacks)
        selectors = [s.strip() for s in selector.split(',')]
        
        last_error = None
        for sel in selectors:
            try:
                locator = self.page.locator(sel)
                await locator.wait_for(state=state, timeout=timeout)
                self.logger.debug(f"Found element with selector: {sel}")
                return locator
            except PlaywrightTimeoutError as e:
                last_error = e
                self.logger.debug(f"Selector '{sel}' not found, trying next...")
                continue
            except Exception as e:
                last_error = e
                self.logger.debug(f"Error with selector '{sel}': {e}")
                continue
        
        # If we get here, none of the selectors worked
        raise ElementNotFoundError(selector)
        
    except ElementNotFoundError:
        raise
    except Exception as e:
        raise BrowserAutomationError(f"Error waiting for element {selector}: {str(e)}")
```

Enhanced `is_element_visible` method:

```python
async def is_element_visible(self, selector: str, timeout: int = 1000) -> bool:
    """Check if element is visible with improved selector handling."""
    try:
        # Handle multiple selectors (comma-separated fallbacks)
        selectors = [s.strip() for s in selector.split(',')]
        
        for sel in selectors:
            try:
                locator = self.page.locator(sel)
                await locator.wait_for(state="visible", timeout=timeout)
                return True
            except:
                continue
        
        return False
    except Exception:
        return False
```

## 4. Enhanced Login Success Detection

### **Issue**
Basic login verification could miss edge cases and fail to detect successful authentication.

### **Fix Applied**
Comprehensive `_verify_login_success` method in `runninghub_proxy/browser_utils/auth_handler.py`:

```python
async def _verify_login_success(self) -> bool:
    """Verify that login was actually successful by checking page indicators."""
    try:
        self.logger.debug("Verifying login success...")
        
        # Wait for page to load after login
        await self.page_ops.wait_for_page_load()
        
        # Check URL changes first (fastest indicator)
        current_url = self.page.url
        for url_pattern in ["/dashboard", "/workspace", "/profile", "/user", "/home"]:
            if url_pattern in current_url:
                self.logger.debug(f"Login success verified by URL: {current_url}")
                return True
        
        # Check for logged-in indicators with timeout
        for indicator in LOGIN_SUCCESS_INDICATORS:
            try:
                if indicator.startswith('/'):
                    # URL check already done above
                    continue
                else:
                    # Element check with short timeout
                    if await self.page_ops.is_element_visible(indicator, timeout=3000):
                        self.logger.debug(f"Login success verified by element: {indicator}")
                        return True
            except Exception as e:
                self.logger.debug(f"Could not check indicator {indicator}: {e}")
                continue
        
        # Additional check: see if login button is no longer present
        login_button_present = await self.page_ops.is_element_visible(
            self.selectors.LOGIN_BUTTON, 
            timeout=2000
        )
        if not login_button_present:
            self.logger.debug("Login success verified by absence of login button")
            return True
        
        # Check page title for logged-in indicators
        try:
            page_title = await self.page.title()
            logged_in_title_indicators = ["工作台", "dashboard", "workspace", "个人中心"]
            for indicator in logged_in_title_indicators:
                if indicator.lower() in page_title.lower():
                    self.logger.debug(f"Login success verified by page title: {page_title}")
                    return True
        except Exception as e:
            self.logger.debug(f"Could not check page title: {e}")
        
        # Check for user-specific content in page
        try:
            page_content = await self.page.content()
            if any(indicator in page_content for indicator in ["个人中心", "我的工作流", "退出登录"]):
                self.logger.debug("Login success verified by page content")
                return True
        except Exception as e:
            self.logger.debug(f"Could not check page content: {e}")
        
        self.logger.debug("No login success indicators found")
        return False
        
    except Exception as e:
        self.logger.error(f"Error verifying login success: {e}")
        return False
```

## 5. Robust Form Reset

### **Issue**
Form reset could fail, leaving stale data that interferes with retry attempts.

### **Fix Applied**
Multi-strategy `_reset_login_form` method in `runninghub_proxy/browser_utils/auth_handler.py`:

```python
async def _reset_login_form(self):
    """Reset the login form for retry attempts."""
    try:
        self.logger.info("Resetting login form for retry...")
        
        # Check if login modal is still open
        modal_visible = await self.page_ops.is_element_visible(
            self.selectors.LOGIN_MODAL, 
            timeout=SELECTOR_TIMEOUTS["quick_check"]
        )
        
        if not modal_visible:
            # Modal is closed, need to reopen it
            self.logger.info("Login modal closed, reopening...")
            await self._open_login_modal()
            # Wait for modal to be ready
            await asyncio.sleep(2)
        
        # Clear any error messages first
        try:
            for error_pattern in ERROR_MESSAGE_PATTERNS:
                if await self.page_ops.is_element_visible(error_pattern, timeout=500):
                    # Try to dismiss error by clicking elsewhere or pressing escape
                    await self.page.keyboard.press("Escape")
                    await asyncio.sleep(1)
                    break
        except Exception as e:
            self.logger.debug(f"Could not clear error messages: {e}")
        
        # Switch to phone login tab if needed
        try:
            if await self.page_ops.is_element_visible(self.selectors.PHONE_LOGIN_TAB, timeout=2000):
                await self.page_ops.click_element(self.selectors.PHONE_LOGIN_TAB)
                await asyncio.sleep(1)  # Wait for tab switch
        except Exception as e:
            self.logger.debug(f"Could not switch to phone login tab: {e}")
        
        # Clear form fields with multiple attempts
        form_fields = [
            (self.selectors.PHONE_INPUT, "phone input"),
            (self.selectors.SMS_CODE_INPUT, "SMS code input"),
            (self.selectors.PASSWORD_INPUT, "password input")
        ]
        
        for selector, field_name in form_fields:
            try:
                if await self.page_ops.is_element_visible(selector, timeout=2000):
                    # Try multiple clearing methods
                    locator = self.page.locator(selector)
                    
                    # Method 1: Select all and delete
                    await locator.click()
                    await self.page.keyboard.press("Control+a")
                    await self.page.keyboard.press("Delete")
                    
                    # Method 2: Clear using Playwright
                    await locator.clear()
                    
                    # Method 3: Fill with empty string
                    await locator.fill("")
                    
                    self.logger.debug(f"Cleared {field_name}")
                    
            except Exception as e:
                self.logger.debug(f"Could not clear {field_name}: {e}")
                continue
        
        # Refresh the page if form reset fails
        try:
            # Check if any field still has content
            fields_cleared = True
            for selector, _ in form_fields:
                try:
                    if await self.page_ops.is_element_visible(selector, timeout=1000):
                        value = await self.page_ops.get_element_attribute(selector, "value")
                        if value and value.strip():
                            fields_cleared = False
                            break
                except:
                    continue
            
            if not fields_cleared:
                self.logger.warning("Form fields not cleared, refreshing page...")
                await self.page.reload(wait_until="domcontentloaded")
                await self._open_login_modal()
                
        except Exception as e:
            self.logger.debug(f"Could not verify field clearing: {e}")
        
        self.logger.info("Login form reset completed")
        
    except Exception as e:
        self.logger.error(f"Error resetting login form: {e}")
        # Continue anyway, as this is not critical for the retry attempt
```

## 6. Enhanced User Interaction Monitoring

### **Issue**
User interaction monitoring was basic and could miss important state changes.

### **Fix Applied**
Improved `_wait_for_user_login_completion` method in `runninghub_proxy/browser_utils/auth_handler.py`:

```python
async def _wait_for_user_login_completion(self, timeout_seconds: int = 90) -> AuthResponse:
    """Wait for user to complete login manually and detect success/failure."""
    try:
        self.logger.info(f"Waiting {timeout_seconds} seconds for user to complete login...")
        
        start_time = asyncio.get_event_loop().time()
        check_interval = 2  # Check every 2 seconds
        last_log_time = start_time
        
        while (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
            current_time = asyncio.get_event_loop().time()
            
            # Check if login modal is closed (success indicator)
            modal_visible = await self.page_ops.is_element_visible(
                self.selectors.LOGIN_MODAL, 
                timeout=SELECTOR_TIMEOUTS["quick_check"]
            )
            
            if not modal_visible:
                self.logger.info("Login modal closed - checking for successful authentication...")
                
                # Additional verification: check if we're on a logged-in page
                await asyncio.sleep(3)  # Wait for page to settle and redirect
                
                # Check for success indicators (user profile, dashboard, etc.)
                if await self._verify_login_success():
                    user_info = await self._extract_user_info()
                    return AuthResponse(
                        status=AuthStatus.AUTHENTICATED,
                        message="Authentication successful",
                        user_info=user_info
                    )
                else:
                    # Modal closed but login might have failed - check for errors
                    self.logger.warning("Modal closed but login verification failed")
                    
                    # Check if modal reopened due to error
                    await asyncio.sleep(2)
                    modal_reopened = await self.page_ops.is_element_visible(
                        self.selectors.LOGIN_MODAL,
                        timeout=SELECTOR_TIMEOUTS["quick_check"]
                    )
                    
                    if modal_reopened:
                        self.logger.info("Login modal reopened - continuing to wait for user")
                        continue
                    else:
                        return AuthResponse(
                            status=AuthStatus.FAILED,
                            message="Login verification failed - please try again"
                        )
            
            # Check for error messages in the modal
            for error_pattern in ERROR_MESSAGE_PATTERNS:
                if await self.page_ops.is_element_visible(error_pattern, timeout=500):
                    try:
                        error_text = await self.page_ops.get_element_text(error_pattern)
                        self.logger.error(f"Login error detected: {error_text}")
                        return AuthResponse(
                            status=AuthStatus.FAILED,
                            message=f"Login failed: {error_text}"
                        )
                    except Exception as e:
                        self.logger.warning(f"Could not extract error text: {e}")
                        return AuthResponse(
                            status=AuthStatus.FAILED,
                            message="Login error detected but could not extract message"
                        )
            
            # Check for CAPTCHA challenges
            captcha_detected = False
            for captcha_pattern in CAPTCHA_PATTERNS:
                if await self.page_ops.is_element_visible(captcha_pattern, timeout=500):
                    self.logger.info(f"CAPTCHA challenge detected: {captcha_pattern}")
                    captcha_detected = True
                    break
            
            # Log progress every 15 seconds
            if current_time - last_log_time >= 15:
                elapsed = current_time - start_time
                remaining = timeout_seconds - elapsed
                status_msg = "CAPTCHA challenge active" if captcha_detected else "waiting for user input"
                self.logger.info(f"Still waiting for user login ({status_msg})... {remaining:.0f} seconds remaining")
                last_log_time = current_time
            
            await asyncio.sleep(check_interval)
        
        # Timeout reached
        self.logger.warning(f"User login timeout after {timeout_seconds} seconds")
        return AuthResponse(
            status=AuthStatus.TIMEOUT,
            message=f"Login timeout after {timeout_seconds} seconds - please try again",
            requires_manual_action=True,
            manual_action_type="timeout_retry"
        )
        
    except Exception as e:
        self.logger.error(f"Error waiting for user login completion: {str(e)}")
        return AuthResponse(
            status=AuthStatus.FAILED,
            message=f"Error during login wait: {str(e)}"
        )
```

## 7. Additional Utility Method

### **Added Helper Method**
Added `clear_and_type` method in `runninghub_proxy/browser_utils/page_operations.py`:

```python
async def clear_and_type(
    self, 
    selector: str, 
    text: str, 
    timeout: Optional[int] = None
) -> bool:
    """Clear input field and type new text."""
    return await self.type_text(selector, text, clear_first=True, timeout=timeout)
```

## 8. Updated Authentication Status

### **Added New Status**
Updated `AuthStatus` enum in `runninghub_proxy/config/constants.py`:

```python
class AuthStatus(str, Enum):
    """Authentication status."""
    UNAUTHENTICATED = "unauthenticated"
    AUTHENTICATING = "authenticating"
    AUTHENTICATED = "authenticated"
    EXPIRED = "expired"
    FAILED = "failed"
    TIMEOUT = "timeout"  # New status for user interaction timeout
```

## Summary

These comprehensive stability improvements ensure that the Playwright automation code is:

1. **Resilient**: Multiple fallback strategies prevent single points of failure
2. **Robust**: Comprehensive error handling and recovery mechanisms
3. **Reliable**: Enhanced detection and verification methods
4. **User-Friendly**: Clear progress feedback and retry mechanisms
5. **Production-Ready**: Enterprise-grade stability and maintainability

The code is now ready for production deployment with maximum reliability and stability.