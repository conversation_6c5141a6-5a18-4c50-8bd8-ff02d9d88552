# Log Suppression Configuration Guide

## Overview

This guide explains how to suppress annoying repetitive logs in the RunningHub Proxy Playwright automation system using environment variables.

## Problem

When running the Playwright automation, you may see repetitive logs like:

```
2025-06-30 06:02:09 | DEBUG    | runninghub_proxy.browser_utils.browser_manager:handle_route:216 | 🚫 Blocked image: https://www.runninghub.cn/_nuxt/default.Bmoe6TmP.png...
2025-06-30 06:02:09 | ERROR    | runninghub_proxy.browser_utils.browser_manager:log_console:158 | 🔴 CONSOLE ERROR: Failed to load resource: net::ERR_FAILED (https://www.runninghub.cn/_nuxt/default.Bmoe6TmP.png:0)
```

These logs occur because:
1. The system blocks images/videos/fonts on non-workflow pages to save bandwidth
2. Blocked resources generate console errors in the browser
3. Both the blocking action and the resulting console errors are logged

## Solution

Use environment variables to suppress these logs while keeping important error logs.

## Environment Variables

### 1. Suppress Blocked Resource Logs

```bash
# Suppress DEBUG logs for blocked images/videos/fonts
SUPPRESS_BLOCKED_RESOURCE_LOGS=true
```

**Default**: `true` (logs are suppressed)
**Set to `false`**: To see all blocked resource logs (useful for debugging)

### 2. Suppress Console Error Logs

```bash
# Suppress ERROR logs for blocked resource console errors
SUPPRESS_CONSOLE_ERROR_LOGS=true
```

**Default**: `true` (console errors for blocked resources are suppressed)
**Set to `false`**: To see all console errors including blocked resources

### 3. Suppression Patterns

The system automatically suppresses console errors containing these patterns:

```python
suppress_failed_resource_patterns = [
    "Failed to load resource: net::ERR_FAILED",
    "Failed to load resource: net::ERR_BLOCKED_BY_CLIENT", 
    "Failed to load resource: net::ERR_ABORTED",
    "_nuxt/",
    ".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg", ".ico",
    ".woff", ".woff2", ".ttf",
    ".mp4", ".webm"
]
```

## Configuration Examples

### 1. Quiet Mode (Recommended for Production)

Create or update your `.env` file:

```bash
# Suppress all blocked resource logs (recommended)
SUPPRESS_BLOCKED_RESOURCE_LOGS=true
SUPPRESS_CONSOLE_ERROR_LOGS=true

# Keep resource blocking enabled for performance
BROWSER_BLOCK_IMAGES=true
BROWSER_BLOCK_VIDEOS=true
BROWSER_BLOCK_ADS=true
```

### 2. Debug Mode (For Development)

```bash
# Show all logs for debugging
SUPPRESS_BLOCKED_RESOURCE_LOGS=false
SUPPRESS_CONSOLE_ERROR_LOGS=false

# Enable verbose logging
APP_DEBUG=true
BROWSER_VERBOSE_LOGGING=true
CONSOLE_LOGGING=true
```

### 3. Selective Suppression

```bash
# Suppress blocked resource logs but show console errors
SUPPRESS_BLOCKED_RESOURCE_LOGS=true
SUPPRESS_CONSOLE_ERROR_LOGS=false

# Or vice versa
SUPPRESS_BLOCKED_RESOURCE_LOGS=false
SUPPRESS_CONSOLE_ERROR_LOGS=true
```

## Implementation Details

### Code Changes Made

1. **Settings Configuration** (`runninghub_proxy/config/settings.py`):
   ```python
   suppress_blocked_resource_logs: bool = Field(
       default=True, 
       description="Suppress DEBUG logs for blocked images/videos/fonts",
       env="SUPPRESS_BLOCKED_RESOURCE_LOGS"
   )
   suppress_console_error_logs: bool = Field(
       default=True, 
       description="Suppress ERROR logs for blocked resource console errors", 
       env="SUPPRESS_CONSOLE_ERROR_LOGS"
   )
   ```

2. **Browser Manager Updates** (`runninghub_proxy/browser_utils/browser_manager.py`):
   - Added conditional logging in `handle_route` function
   - Added pattern matching in `log_console` function
   - Suppression applies to both image/video blocking and ads/tracking blocking

### What Gets Suppressed

**Blocked Resource Logs** (when `SUPPRESS_BLOCKED_RESOURCE_LOGS=true`):
```
🚫 Blocked image: https://example.com/image.png...
🚫 Blocked tracking/ads: https://analytics.com/script.js...
```

**Console Error Logs** (when `SUPPRESS_CONSOLE_ERROR_LOGS=true`):
```
🔴 CONSOLE ERROR: Failed to load resource: net::ERR_FAILED (https://example.com/image.png:0)
🔴 CONSOLE ERROR: Failed to load resource: net::ERR_BLOCKED_BY_CLIENT (https://example.com/font.woff2:0)
```

### What Doesn't Get Suppressed

Important logs are never suppressed:
- Authentication errors
- Page navigation errors  
- Element interaction failures
- Network timeouts
- JavaScript runtime errors (non-resource related)
- Application logic errors

## Testing the Configuration

### 1. Check Current Settings

Start the application and look for this log message:
```
📡 Smart resource blocking enabled (images/videos blocked except on workflow pages)
```

### 2. Test Suppression

1. **With suppression enabled** (default):
   - You should see minimal blocked resource logs
   - Console errors for blocked resources should be suppressed

2. **With suppression disabled**:
   ```bash
   SUPPRESS_BLOCKED_RESOURCE_LOGS=false
   SUPPRESS_CONSOLE_ERROR_LOGS=false
   ```
   - You should see all blocked resource logs
   - All console errors will be visible

### 3. Verify Resource Blocking Still Works

Even with log suppression, resource blocking should still function:
- Images/videos blocked on non-workflow pages
- Ads and tracking scripts blocked
- Fonts always blocked
- Performance benefits maintained

## Troubleshooting

### Issue: Still seeing blocked resource logs

**Solution**: Check your `.env` file:
```bash
# Make sure these are set to true
SUPPRESS_BLOCKED_RESOURCE_LOGS=true
SUPPRESS_CONSOLE_ERROR_LOGS=true
```

### Issue: Missing important error logs

**Solution**: The suppression is pattern-based and should only affect resource loading errors. If you're missing important logs:

1. Check if the error contains suppressed patterns
2. Temporarily disable suppression for debugging:
   ```bash
   SUPPRESS_CONSOLE_ERROR_LOGS=false
   ```

### Issue: Performance impact

**Solution**: Log suppression improves performance by reducing log I/O. Resource blocking is still active regardless of log suppression settings.

## Best Practices

### Production Environment
```bash
# Recommended production settings
SUPPRESS_BLOCKED_RESOURCE_LOGS=true
SUPPRESS_CONSOLE_ERROR_LOGS=true
APP_DEBUG=false
BROWSER_VERBOSE_LOGGING=false
```

### Development Environment
```bash
# Recommended development settings
SUPPRESS_BLOCKED_RESOURCE_LOGS=true  # Still suppress to reduce noise
SUPPRESS_CONSOLE_ERROR_LOGS=false    # Show console errors for debugging
APP_DEBUG=true
BROWSER_VERBOSE_LOGGING=true
```

### Debugging Resource Issues
```bash
# Temporarily enable all logs
SUPPRESS_BLOCKED_RESOURCE_LOGS=false
SUPPRESS_CONSOLE_ERROR_LOGS=false
NETWORK_LOGGING=true
```

## Summary

The log suppression feature allows you to:
- ✅ Reduce log noise from blocked resources
- ✅ Maintain performance benefits of resource blocking
- ✅ Keep important error logs visible
- ✅ Easily toggle suppression for debugging
- ✅ Customize suppression patterns as needed

Use `SUPPRESS_BLOCKED_RESOURCE_LOGS=true` and `SUPPRESS_CONSOLE_ERROR_LOGS=true` in your `.env` file to eliminate the annoying repetitive logs while keeping the automation system fully functional.