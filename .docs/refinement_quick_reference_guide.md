# Playwright Automation Quick Reference Guide

## Overview

This guide provides a quick reference for the enhanced Playwright automation code with all stability improvements implemented.

## Key Improvements Summary

| Component | Issue Fixed | Solution Implemented |
|-----------|-------------|---------------------|
| **Configuration** | Missing constants | Added comprehensive timeout and pattern configurations |
| **Selectors** | Fragile single selectors | Implemented fallback selector chains |
| **Element Interaction** | Click/wait failures | Multi-strategy approach with retry logic |
| **Login Detection** | Basic verification | Comprehensive multi-method verification |
| **Form Reset** | Unreliable clearing | Multi-strategy form clearing with fallbacks |
| **User Monitoring** | Limited state tracking | Enhanced 90-second interaction monitoring |
| **Error Handling** | Basic error handling | Comprehensive error recovery mechanisms |

## Configuration Constants

### Timeouts (in milliseconds)
```python
SELECTOR_TIMEOUTS = {
    "default": 10000,           # Standard operations
    "page_load": 30000,         # Page loading
    "login": 60000,             # Login operations
    "workflow_execution": 300000, # Workflow execution
    "element_wait": 5000,       # Element waiting
    "quick_check": 1000,        # Quick checks
    "user_interaction": 90000,  # User interaction window
}
```

### Key Patterns
```python
# CAPTCHA Detection
CAPTCHA_PATTERNS = [
    "text=拖动滑块完成拼图",      # Drag slider puzzle
    "text=点击完成验证",          # Click verification
    "[class*='captcha']",         # Generic captcha
    "[class*='verify']",          # Generic verify
]

# Login Success Indicators
LOGIN_SUCCESS_INDICATORS = [
    ".user-avatar", ".user-menu",
    "text=个人中心", "text=工作台", "text=退出登录",
    "/dashboard", "/workspace", "/profile"
]

# Error Message Patterns
ERROR_MESSAGE_PATTERNS = [
    ".ant-message-error", ".error-message",
    "text=登录失败", "text=验证码错误", "text=密码错误"
]
```

## Enhanced Selectors

### Authentication Selectors
```python
# Robust selectors with multiple fallbacks
LOGIN_BUTTON = "button:has-text('登 录'), button:has-text('登录'), .login-btn"
LOGIN_MODAL = ".ant-modal, [role='dialog']"
PHONE_INPUT = "input[placeholder*='手机号'], input[placeholder*='phone'], input[type='tel']"
SMS_CODE_INPUT = "input[placeholder*='验证码'], input[placeholder*='code']"
PASSWORD_INPUT = "input[type='password'], input[placeholder*='密码']"

# Tab selectors
WECHAT_LOGIN_TAB = "text=微信登录, text=微信扫码登录, button:has-text('微信')"
PHONE_LOGIN_TAB = "text=验证码登录, text=手机登录, button:has-text('验证码')"
PASSWORD_LOGIN_TAB = "text=密码登录, button:has-text('密码')"

# Action buttons
LOGIN_SUBMIT = "button:has-text('立即登录'), button:has-text('登录'), .login-submit"
SEND_CODE_BUTTON = "button:has-text('发送验证码'), button:has-text('获取验证码'), .send-code"
```

## Enhanced Methods

### Multi-Strategy Element Clicking
```python
async def click_element(self, selector: str, timeout: Optional[int] = None, force: bool = False) -> bool:
    # 1. Try multiple selectors (comma-separated fallbacks)
    # 2. Scroll into view if needed
    # 3. Multiple click strategies:
    #    - Normal click
    #    - Force click
    #    - JavaScript click
    # 4. Retry with exponential backoff
```

### Comprehensive Login Success Verification
```python
async def _verify_login_success(self) -> bool:
    # 1. Check URL changes (fastest)
    # 2. Check UI elements (user avatar, menu, etc.)
    # 3. Check page title
    # 4. Check page content
    # 5. Verify login button absence
```

### Robust Form Reset
```python
async def _reset_login_form(self):
    # 1. Check modal state
    # 2. Clear error messages
    # 3. Switch to correct tab
    # 4. Multi-strategy field clearing:
    #    - Keyboard shortcuts (Ctrl+A, Delete)
    #    - Playwright clear()
    #    - Empty string fill
    # 5. Page refresh fallback if needed
```

### Enhanced User Interaction Monitoring
```python
async def _wait_for_user_login_completion(self, timeout_seconds: int = 90) -> AuthResponse:
    # 1. Monitor modal state every 2 seconds
    # 2. Detect CAPTCHA challenges
    # 3. Check for error messages
    # 4. Verify login success when modal closes
    # 5. Progress logging every 15 seconds
    # 6. Handle modal reopening scenarios
```

## Usage Examples

### Basic Authentication Flow
```python
# Initialize authentication handler
auth_handler = AuthHandler(browser_context)

# Create authentication request
auth_request = AuthRequest(
    method=AuthMethod.PHONE_SMS,
    phone="13800138000"  # Optional pre-fill
)

# Start authentication (opens modal and waits for user)
auth_response = await auth_handler.authenticate(auth_request)

# Handle response
if auth_response.status == AuthStatus.AUTHENTICATED:
    print("Login successful!")
    user_info = auth_response.user_info
elif auth_response.status == AuthStatus.TIMEOUT:
    print("Login timeout - user has 90 seconds to complete")
elif auth_response.status == AuthStatus.FAILED:
    print(f"Login failed: {auth_response.message}")
```

### Manual Element Interaction
```python
# Enhanced page operations
page_ops = PageOperations(page)

# Robust clicking with fallbacks
await page_ops.click_element("button:has-text('登录'), .login-btn")

# Multi-selector element waiting
locator = await page_ops.wait_for_element(
    "input[placeholder*='手机号'], input[type='tel']",
    timeout=10000
)

# Reliable visibility checking
is_visible = await page_ops.is_element_visible(
    ".ant-modal, [role='dialog']",
    timeout=5000
)
```

## Error Handling Patterns

### Standard Error Handling
```python
try:
    # Perform operation
    result = await page_ops.click_element(selector)
except ElementNotFoundError:
    # Handle element not found
    logger.error(f"Element not found: {selector}")
except BrowserAutomationError as e:
    # Handle automation errors
    logger.error(f"Automation error: {e}")
except Exception as e:
    # Handle unexpected errors
    logger.error(f"Unexpected error: {e}")
```

### Retry Pattern
```python
for attempt in range(settings.max_retries):
    try:
        # Attempt operation
        result = await operation()
        break
    except Exception as e:
        if attempt == settings.max_retries - 1:
            raise
        logger.warning(f"Attempt {attempt + 1} failed: {e}")
        await asyncio.sleep(settings.retry_delay)
```

## Debugging and Monitoring

### Logging Levels
```python
# Debug: Detailed operation information
self.logger.debug(f"Clicking element: {selector}")

# Info: Important state changes
self.logger.info("Login modal opened successfully")

# Warning: Recoverable issues
self.logger.warning("Click attempt failed, retrying...")

# Error: Serious issues
self.logger.error(f"Authentication failed: {error}")
```

### Performance Monitoring
```python
# Use PerformanceLogger for timing
with PerformanceLogger(f"authenticate({auth_request.method})"):
    # Perform operation
    result = await operation()
```

## Configuration Tuning

### Timeout Adjustments
```python
# Adjust timeouts based on environment
SELECTOR_TIMEOUTS = {
    "default": 15000,  # Increase for slower environments
    "page_load": 45000,  # Increase for slow networks
    "user_interaction": 120000,  # Increase for complex forms
}
```

### Retry Configuration
```python
# Adjust retry behavior
RETRY_CONFIG = {
    "max_attempts": 5,  # Increase for unreliable environments
    "base_delay": 2.0,  # Increase base delay
    "exponential_backoff": True,
    "max_delay": 15.0,  # Increase max delay
}
```

## Best Practices

### 1. Selector Design
- Always provide multiple fallback selectors
- Use text-based selectors for language independence
- Include semantic attributes for framework changes
- Test selectors across different UI states

### 2. Error Handling
- Wrap all operations in try-catch blocks
- Provide meaningful error messages
- Implement graceful degradation
- Log errors with sufficient context

### 3. User Interaction
- Always provide progress feedback
- Handle CAPTCHA challenges gracefully
- Allow sufficient time for manual completion
- Implement retry mechanisms for failed attempts

### 4. Performance
- Use appropriate timeouts for different operations
- Implement efficient element detection strategies
- Minimize unnecessary waits
- Monitor and optimize based on production metrics

## Troubleshooting

### Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| Element not found | UI changes | Check selector fallbacks, add new selectors |
| Click failures | Element not clickable | Use force click or JavaScript click strategy |
| Timeout errors | Slow network/page | Increase timeout values |
| Form reset failures | Persistent data | Use page refresh fallback |
| Login detection failures | New UI indicators | Add new success indicators |

### Debug Commands
```python
# Check element visibility
is_visible = await page_ops.is_element_visible(selector, timeout=1000)

# Get element text for debugging
text = await page_ops.get_element_text(selector)

# Take screenshot for analysis
screenshot = await page_ops.take_screenshot()

# Check page URL and title
url = page.url
title = await page.title()
```

This enhanced Playwright automation system provides enterprise-grade reliability and stability for RunningHub.cn authentication workflows.