# AI Studio Proxy API - Mermaid Architecture Diagram

## Complete System Architecture Diagram

The following Mermaid diagram provides a comprehensive visualization of the AI Studio Proxy API system architecture, showing all major components, their relationships, and data flow patterns.

```mermaid
graph TB
    subgraph "👥 User Interface Layer"
        User[👤 End Users]
        WebUI[🌐 Web UI<br/>Real-time Chat Interface<br/>index.html + webui.js/css]
        APIClient[🔌 API Clients<br/>OpenAI Compatible<br/>Third-party Tools]
        GUILauncher[🖥️ GUI Launcher<br/>Tkinter Interface<br/>gui_launcher.py]
    end

    subgraph "🚀 Entry Points & Launch Layer"
        CLILauncher[⚡ CLI Launcher<br/>Command Line Interface<br/>launch_camoufox.py]
        ServerPy[🎯 Server Entry Point<br/>FastAPI Bootstrap<br/>server.py]
        EnvConfig[⚙️ Environment Config<br/>.env Configuration<br/>config/ modules]
        AuthFiles[🔐 Authentication<br/>API Keys & Auth Profiles<br/>auth_profiles/ + key.txt]
    end

    subgraph "🎯 Core Application Layer"
        FastAPIApp[🚀 FastAPI Application<br/>Lifecycle Management<br/>api_utils/app.py]
        Routes[🛣️ API Routes Handler<br/>Endpoint Definitions<br/>api_utils/routes.py]
        RequestProcessor[⚙️ Request Processor<br/>Three-Tier Coordination<br/>api_utils/request_processor.py]
        QueueWorker[📋 Queue Worker<br/>Async Request Processing<br/>api_utils/queue_worker.py]
        AuthUtils[🔒 Auth Middleware<br/>API Key Validation<br/>api_utils/auth_utils.py]
        Dependencies[🔗 FastAPI Dependencies<br/>Dependency Injection<br/>api_utils/dependencies.py]
    end

    subgraph "🎮 Browser Automation Layer"
        PageController[🎮 Page Controller<br/>Browser Lifecycle<br/>browser_utils/page_controller.py]
        ModelManager[🤖 Model Management<br/>AI Studio Model Switching<br/>browser_utils/model_management.py]
        ScriptManager[📜 Script Manager<br/>User Script Injection v3.0<br/>browser_utils/script_manager.py]
        BrowserOps[🔧 Browser Operations<br/>Page Interactions<br/>browser_utils/operations.py]
        BrowserInit[🚀 Browser Initialization<br/>Context Setup<br/>browser_utils/initialization.py]
    end

    subgraph "🌊 Streaming & Proxy Layer"
        StreamProxy[🌊 Stream Proxy Server<br/>HTTPS Interception<br/>stream/proxy_server.py]
        ProxyConnector[🔌 Proxy Connector<br/>Upstream Proxy Support<br/>stream/proxy_connector.py]
        Interceptors[🕵️ HTTP Interceptors<br/>Request/Response Processing<br/>stream/interceptors.py]
        CertManager[📜 Certificate Manager<br/>Dynamic SSL Certs<br/>stream/cert_manager.py]
        StreamMain[⚡ Stream Entry Point<br/>Proxy Lifecycle<br/>stream/main.py]
    end

    subgraph "📊 Data & Configuration Layer"
        ChatModels[💬 Chat Models<br/>Pydantic Data Structures<br/>models/chat.py]
        Exceptions[❌ Exception Models<br/>Custom Error Handling<br/>models/exceptions.py]
        LoggingModels[📝 Logging Models<br/>WebSocket Log Streaming<br/>models/logging.py]
        ConfigSettings[⚙️ Settings Manager<br/>Environment Variables<br/>config/settings.py]
        ConfigConstants[📋 System Constants<br/>Default Values<br/>config/constants.py]
        ConfigSelectors[🎯 CSS Selectors<br/>Browser Element Targeting<br/>config/selectors.py]
        ConfigTimeouts[⏱️ Timeout Configuration<br/>Operation Timeouts<br/>config/timeouts.py]
    end

    subgraph "📊 Logging & Monitoring Layer"
        LoggingUtils[📊 Logging Setup<br/>Centralized Logging<br/>logging_utils/setup.py]
        WSManager[🔌 WebSocket Manager<br/>Real-time Log Streaming<br/>models/logging.py]
        LogFiles[📁 Log Files<br/>Persistent Logging<br/>logs/]
    end

    subgraph "🌐 External Dependencies"
        CamoufoxBrowser[🦊 Camoufox Browser<br/>Anti-fingerprint Firefox<br/>Playwright Integration]
        AIStudio[🤖 Google AI Studio<br/>aistudio.google.com<br/>Target Service]
        UserScripts[📜 User Scripts<br/>Model Enhancement<br/>more_models.js]
        UpstreamProxy[🌐 Upstream Proxy<br/>Optional SOCKS/HTTP<br/>Network Routing]
    end

    %% User Interaction Flow
    User -->|Launch GUI| GUILauncher
    User -->|Direct CLI| CLILauncher
    User -->|Web Interface| WebUI
    APIClient -->|HTTP Requests| FastAPIApp

    %% Launch & Configuration Flow
    GUILauncher -->|Executes| CLILauncher
    CLILauncher -->|Starts| ServerPy
    CLILauncher -->|Initializes| CamoufoxBrowser
    EnvConfig -->|Configures| ConfigSettings
    ConfigSettings -->|Provides| ConfigConstants
    ConfigSettings -->|Provides| ConfigSelectors
    ConfigSettings -->|Provides| ConfigTimeouts
    AuthFiles -->|Validates| AuthUtils

    %% Core Application Flow
    ServerPy -->|Creates| FastAPIApp
    FastAPIApp -->|Registers| Routes
    FastAPIApp -->|Initializes| LoggingUtils
    Routes -->|Processes| RequestProcessor
    Routes -->|Authenticates| AuthUtils
    Routes -->|Injects| Dependencies
    RequestProcessor -->|Queues| QueueWorker

    %% Browser Automation Flow
    QueueWorker -->|Controls| PageController
    PageController -->|Manages| ModelManager
    PageController -->|Injects| ScriptManager
    PageController -->|Executes| BrowserOps
    BrowserInit -->|Initializes| PageController
    ScriptManager -->|Loads| UserScripts
    PageController -->|Automates| CamoufoxBrowser

    %% Streaming & Proxy Flow
    FastAPIApp -->|Spawns| StreamMain
    StreamMain -->|Creates| StreamProxy
    StreamProxy -->|Uses| ProxyConnector
    StreamProxy -->|Processes| Interceptors
    StreamProxy -->|Manages| CertManager
    ProxyConnector -->|Routes| UpstreamProxy
    StreamProxy -->|Intercepts| AIStudio

    %% Data Model Flow
    ChatModels -->|Validates| RequestProcessor
    Exceptions -->|Handles| RequestProcessor
    LoggingModels -->|Streams| WSManager
    WSManager -->|Updates| WebUI

    %% Browser to AI Studio Interaction
    CamoufoxBrowser -->|Navigates| AIStudio
    BrowserOps -->|Controls| CamoufoxBrowser

    %% Three-Tier Response Strategy
    AIStudio -.->|Tier 1: Stream Proxy<br/>Highest Performance| StreamProxy
    AIStudio -.->|Tier 2: Helper Service<br/>Fallback Option| RequestProcessor
    AIStudio -.->|Tier 3: Playwright<br/>Most Reliable| BrowserOps

    %% Response Flow Back to Client
    StreamProxy -->|Streams| RequestProcessor
    BrowserOps -->|Returns| RequestProcessor
    RequestProcessor -->|Responds| Routes
    Routes -->|Delivers| FastAPIApp
    FastAPIApp -->|Serves| APIClient
    FastAPIApp -->|Updates| WebUI

    %% Logging & Monitoring Flow
    LoggingUtils -->|Writes| LogFiles
    WSManager -->|Streams| WebUI
    FastAPIApp -->|Logs| WSManager

    %% Styling for Visual Clarity
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef entryLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:3px,color:#000
    classDef coreLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px,color:#000
    classDef browserLayer fill:#fff3e0,stroke:#e65100,stroke-width:3px,color:#000
    classDef streamLayer fill:#fce4ec,stroke:#880e4f,stroke-width:3px,color:#000
    classDef dataLayer fill:#f1f8e9,stroke:#33691e,stroke-width:3px,color:#000
    classDef loggingLayer fill:#e0f2f1,stroke:#004d40,stroke-width:3px,color:#000
    classDef externalLayer fill:#fafafa,stroke:#424242,stroke-width:3px,color:#000

    class User,WebUI,APIClient,GUILauncher userLayer
    class CLILauncher,ServerPy,EnvConfig,AuthFiles entryLayer
    class FastAPIApp,Routes,RequestProcessor,QueueWorker,AuthUtils,Dependencies coreLayer
    class PageController,ModelManager,ScriptManager,BrowserOps,BrowserInit browserLayer
    class StreamProxy,ProxyConnector,Interceptors,CertManager,StreamMain streamLayer
    class ChatModels,Exceptions,LoggingModels,ConfigSettings,ConfigConstants,ConfigSelectors,ConfigTimeouts dataLayer
    class LoggingUtils,WSManager,LogFiles loggingLayer
    class CamoufoxBrowser,AIStudio,UserScripts,UpstreamProxy externalLayer
```

## Diagram Components Explanation

### Layer Descriptions

#### 👥 User Interface Layer
- **End Users**: Human users interacting with the system
- **Web UI**: Browser-based interface for real-time chat and monitoring
- **API Clients**: Third-party applications using OpenAI-compatible endpoints
- **GUI Launcher**: Desktop application for easy system management

#### 🚀 Entry Points & Launch Layer
- **CLI Launcher**: Command-line interface for system startup
- **Server Entry Point**: FastAPI application bootstrap
- **Environment Config**: Configuration management and .env file processing
- **Authentication**: API keys and authentication profile management

#### 🎯 Core Application Layer
- **FastAPI Application**: Main web application with lifecycle management
- **API Routes Handler**: HTTP endpoint definitions and routing
- **Request Processor**: Core business logic and three-tier coordination
- **Queue Worker**: Asynchronous request processing and management
- **Auth Middleware**: Authentication and authorization handling
- **Dependencies**: Dependency injection and shared resource management

#### 🎮 Browser Automation Layer
- **Page Controller**: Browser page lifecycle and session management
- **Model Management**: AI Studio model switching and state synchronization
- **Script Manager**: User script injection system with network interception
- **Browser Operations**: Low-level browser interactions and response extraction
- **Browser Initialization**: Browser setup and anti-fingerprinting configuration

#### 🌊 Streaming & Proxy Layer
- **Stream Proxy Server**: HTTPS proxy with SSL inspection capabilities
- **Proxy Connector**: Upstream proxy connection and routing
- **HTTP Interceptors**: Request/response processing and content modification
- **Certificate Manager**: Dynamic SSL certificate generation and management
- **Stream Entry Point**: Proxy server lifecycle and configuration

#### 📊 Data & Configuration Layer
- **Chat Models**: Pydantic data structures for API compatibility
- **Exception Models**: Custom error handling and HTTP status mapping
- **Logging Models**: WebSocket connection management and log streaming
- **Settings Manager**: Environment variable processing and configuration
- **System Constants**: Default values and system-wide constants
- **CSS Selectors**: Browser element targeting for automation
- **Timeout Configuration**: Operation timeouts and retry policies

#### 📊 Logging & Monitoring Layer
- **Logging Setup**: Centralized logging configuration and management
- **WebSocket Manager**: Real-time log streaming to web interface
- **Log Files**: Persistent logging with rotation and archival

#### 🌐 External Dependencies
- **Camoufox Browser**: Anti-fingerprint Firefox for stealth automation
- **Google AI Studio**: Target service for AI model interactions
- **User Scripts**: JavaScript enhancements for extended functionality
- **Upstream Proxy**: Optional network routing through external proxies

### Data Flow Patterns

#### Request Processing Flow
1. **User Input** → API Client → FastAPI Application
2. **Authentication** → Middleware validation
3. **Request Processing** → Three-tier response strategy
4. **Response Delivery** → Streaming back to client

#### Three-Tier Response Strategy
1. **Tier 1**: Stream Proxy (highest performance, direct interception)
2. **Tier 2**: Helper Service (fallback option with external service)
3. **Tier 3**: Browser Automation (most reliable, full feature support)

#### Configuration Flow
1. **Environment Variables** → Settings loading
2. **Configuration Distribution** → Component initialization
3. **Runtime Updates** → Dynamic configuration changes

#### Monitoring Flow
1. **System Events** → Logging system
2. **Real-time Streaming** → WebSocket connections
3. **Dashboard Updates** → Web UI display

### Key Architectural Benefits

#### High Availability
- Multiple fallback mechanisms ensure system resilience
- Three-tier response strategy provides redundancy
- Comprehensive error handling and recovery

#### Performance Optimization
- Stream proxy provides minimal latency for simple requests
- Asynchronous processing with queue management
- Connection pooling and resource reuse

#### Security
- API key authentication with Bearer token support
- SSL inspection and certificate management
- Anti-fingerprinting browser technology

#### Maintainability
- Clear modular separation with defined interfaces
- Comprehensive logging and monitoring
- Configuration management through environment variables

#### Scalability
- Queue-based processing for horizontal scaling
- Stateless design for easy deployment
- Resource management and cleanup

This architecture diagram demonstrates a well-engineered system that balances performance, reliability, security, and maintainability while providing a robust proxy solution for converting web interfaces to API endpoints.
