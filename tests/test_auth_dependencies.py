"""Unit tests for API dependencies and authentication flow."""

import pytest
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON>, patch
from fastapi.security import HTTPAuthorizationCredentials
from fastapi import HTTPException

from runninghub_proxy.api_utils.dependencies import (
    get_current_session, 
    require_authentication,
    get_session_manager
)
from runninghub_proxy.browser_utils.session_manager import Session<PERSON>anager
from runninghub_proxy.models.exceptions import AuthenticationRequiredError, SessionExpiredError


class TestAuthenticationDependencies:
    """Test authentication-related dependencies."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        return AsyncMock(spec=SessionManager)

    @pytest.fixture
    def valid_session_data(self):
        """Create valid session data for testing."""
        return {
            "session_token": "valid-token-123",
            "user_id": "user123",
            "user_info": {
                "username": "testuser",
                "email": "<EMAIL>"
            },
            "is_active": True,
            "created_at": "2024-01-01T12:00:00",
            "expires_at": "2024-01-02T12:00:00"
        }

    @pytest.mark.asyncio
    async def test_get_current_session_with_valid_token(self, mock_session_manager, valid_session_data):
        """Test get_current_session with valid authorization token."""
        # Setup
        mock_session_manager.get_session.return_value = valid_session_data
        mock_session_manager.is_session_valid.return_value = True
        
        auth_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid-token-123"
        )
        
        # Test
        result = await get_current_session(
            session_manager=mock_session_manager,
            authorization=auth_credentials
        )
        
        # Verify
        assert result == valid_session_data
        mock_session_manager.get_session.assert_called_once_with("valid-token-123")
        mock_session_manager.is_session_valid.assert_called_once_with("valid-token-123")

    @pytest.mark.asyncio
    async def test_get_current_session_with_no_authorization(self, mock_session_manager):
        """Test get_current_session with no authorization header."""
        result = await get_current_session(
            session_manager=mock_session_manager,
            authorization=None
        )
        
        assert result is None
        mock_session_manager.get_session.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_current_session_with_nonexistent_token(self, mock_session_manager):
        """Test get_current_session with non-existent token."""
        # Setup
        mock_session_manager.get_session.return_value = None
        
        auth_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="nonexistent-token"
        )
        
        # Test
        result = await get_current_session(
            session_manager=mock_session_manager,
            authorization=auth_credentials
        )
        
        # Verify
        assert result is None
        mock_session_manager.get_session.assert_called_once_with("nonexistent-token")
        mock_session_manager.is_session_valid.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_current_session_with_expired_token(self, mock_session_manager, valid_session_data):
        """Test get_current_session with expired token."""
        # Setup
        mock_session_manager.get_session.return_value = valid_session_data
        mock_session_manager.is_session_valid.return_value = False
        
        auth_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="expired-token"
        )
        
        # Test and verify exception
        with pytest.raises(SessionExpiredError, match="Session has expired"):
            await get_current_session(
                session_manager=mock_session_manager,
                authorization=auth_credentials
            )

    @pytest.mark.asyncio
    async def test_require_authentication_with_valid_session(self, valid_session_data):
        """Test require_authentication with valid session."""
        result = await require_authentication(session_info=valid_session_data)
        assert result == valid_session_data

    @pytest.mark.asyncio
    async def test_require_authentication_with_no_session(self):
        """Test require_authentication with no session."""
        with pytest.raises(AuthenticationRequiredError, match="Authentication required"):
            await require_authentication(session_info=None)

    @pytest.mark.asyncio 
    async def test_get_session_manager_initialization_wait(self):
        """Test that get_session_manager waits for initialization."""
        # Create mock session manager that becomes initialized after a delay
        mock_sm = Mock(spec=SessionManager)
        mock_sm._initialized = False
        
        async def delayed_init():
            await asyncio.sleep(0.1)
            mock_sm._initialized = True
        
        # Start initialization in background
        import asyncio
        init_task = asyncio.create_task(delayed_init())
        
        # Mock the global session manager
        with patch('runninghub_proxy.api_utils.dependencies._session_manager', mock_sm):
            # This should wait for initialization
            result = await get_session_manager()
            assert result == mock_sm
            assert mock_sm._initialized is True
        
        # Cleanup
        await init_task


class TestAuthenticationFlow:
    """Test complete authentication flow integration."""

    @pytest.mark.asyncio
    async def test_complete_auth_flow_success(self):
        """Test complete authentication flow from login to status check."""
        # This would typically involve:
        # 1. Login request creates session
        # 2. Session is persisted to disk
        # 3. Subsequent requests use session token
        # 4. Token is validated and session restored
        
        from runninghub_proxy.browser_utils.session_manager import SessionManager
        import tempfile
        from pathlib import Path
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
            tmp_path = Path(tmp.name)
        
        try:
            # Step 1: Create session manager and session (simulating login)
            sm = SessionManager(session_file=tmp_path.name)
            while not sm._initialized:
                await asyncio.sleep(0.01)
            
            user_info = {
                "user_id": "flow_test_user",
                "username": "flowtest",
                "email": "<EMAIL>"
            }
            
            session_data = await sm.create_session(user_info=user_info)
            session_token = session_data["session_token"]
            
            # Step 2: Simulate new request with session token
            auth_credentials = HTTPAuthorizationCredentials(
                scheme="Bearer",
                credentials=session_token
            )
            
            # Step 3: Get current session (simulating auth dependency)
            current_session = await get_current_session(
                session_manager=sm,
                authorization=auth_credentials
            )
            
            # Step 4: Verify session data
            assert current_session is not None
            assert current_session["user_id"] == "flow_test_user"
            assert current_session["user_info"]["username"] == "flowtest"
            assert current_session["user_info"]["email"] == "<EMAIL>"
            
            # Step 5: Test require_authentication
            authenticated_session = await require_authentication(current_session)
            assert authenticated_session == current_session
            
            # Step 6: Test that session persists across session manager restarts
            await sm.cleanup()
            
            # Create new session manager (simulating app restart)
            sm2 = SessionManager(session_file=tmp_path.name)
            while not sm2._initialized:
                await asyncio.sleep(0.01)
            
            # Try to get session again
            current_session_after_restart = await get_current_session(
                session_manager=sm2,
                authorization=auth_credentials
            )
            
            assert current_session_after_restart is not None
            assert current_session_after_restart["user_id"] == "flow_test_user"
            
            await sm2.cleanup()
            
        finally:
            # Cleanup
            if tmp_path.exists():
                tmp_path.unlink()

    @pytest.mark.asyncio
    async def test_auth_flow_with_session_expiry(self):
        """Test authentication flow when session expires."""
        from runninghub_proxy.browser_utils.session_manager import SessionManager
        import tempfile
        from pathlib import Path
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
            tmp_path = Path(tmp.name)
        
        try:
            # Create session manager with very short session timeout
            with patch('runninghub_proxy.browser_utils.session_manager.settings') as mock_settings:
                mock_settings.session_timeout = 0.1  # 0.1 seconds
                
                sm = SessionManager(session_file=tmp_path.name)
                while not sm._initialized:
                    await asyncio.sleep(0.01)
                
                user_info = {"user_id": "expiry_test_user", "username": "expirytest"}
                session_data = await sm.create_session(user_info=user_info)
                session_token = session_data["session_token"]
                
                # Verify session is initially valid
                auth_credentials = HTTPAuthorizationCredentials(
                    scheme="Bearer",
                    credentials=session_token
                )
                
                current_session = await get_current_session(
                    session_manager=sm,
                    authorization=auth_credentials
                )
                assert current_session is not None
                
                # Wait for session to expire
                await asyncio.sleep(0.2)
                
                # Try to get session - should raise SessionExpiredError
                with pytest.raises(SessionExpiredError):
                    await get_current_session(
                        session_manager=sm,
                        authorization=auth_credentials
                    )
                
                await sm.cleanup()
                
        finally:
            if tmp_path.exists():
                tmp_path.unlink()


if __name__ == "__main__":
    import asyncio
    pytest.main([__file__, "-v"])
