"""Unit tests for session persistence functionality."""

import asyncio
import json
import tempfile
import pytest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, patch

from runninghub_proxy.browser_utils.session_manager import SessionManager
from runninghub_proxy.models.exceptions import AuthenticationError


class TestSessionPersistence:
    """Test session persistence and restoration functionality."""

    @pytest.fixture
    async def temp_session_manager(self):
        """Create a SessionManager with temporary file for testing."""
        import tempfile
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
            tmp_path = Path(tmp.name)
        
        # Create session manager with custom session file
        session_manager = SessionManager(session_file=tmp_path.name)
        
        # Wait for initialization
        while not session_manager._initialized:
            await asyncio.sleep(0.01)
            
        yield session_manager
        
        # Cleanup
        await session_manager.cleanup()
        if tmp_path.exists():
            tmp_path.unlink()

    @pytest.mark.asyncio
    async def test_session_creation_and_persistence(self, temp_session_manager):
        """Test that sessions are created and persisted to disk."""
        sm = temp_session_manager
        
        # Create a test session
        user_info = {
            "user_id": "test_user_123",
            "username": "testuser",
            "email": "<EMAIL>"
        }
        
        session_data = await sm.create_session(user_info=user_info)
        
        # Verify session was created
        assert session_data["session_token"]
        assert session_data["user_id"] == "test_user_123"
        assert session_data["user_info"] == user_info
        
        # Verify session file was created and contains the session
        assert sm.session_file_path.exists()
        
        with open(sm.session_file_path, "r") as f:
            saved_data = json.load(f)
        
        assert "sessions" in saved_data
        assert session_data["session_token"] in saved_data["sessions"]
        
        # Verify session data in file
        saved_session = saved_data["sessions"][session_data["session_token"]]
        assert saved_session["user_id"] == "test_user_123"
        assert saved_session["user_info"] == user_info

    @pytest.mark.asyncio
    async def test_session_loading_on_startup(self, temp_session_manager):
        """Test that sessions are loaded from disk on startup."""
        sm1 = temp_session_manager
        
        # Create multiple sessions
        user_info_1 = {"user_id": "user1", "username": "user1"}
        user_info_2 = {"user_id": "user2", "username": "user2"}
        
        session1 = await sm1.create_session(user_info=user_info_1)
        session2 = await sm1.create_session(user_info=user_info_2)
        
        # Verify sessions exist
        assert len(sm1.sessions) == 2
        assert len(sm1.user_sessions) == 2
        
        # Create a new session manager with the same file
        sm2 = SessionManager(session_file=sm1.session_file_path.name)
        
        # Wait for initialization
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Verify sessions were loaded
        assert len(sm2.sessions) == 2
        assert session1["session_token"] in sm2.sessions
        assert session2["session_token"] in sm2.sessions
        
        # Verify user sessions mapping was rebuilt
        assert "user1" in sm2.user_sessions
        assert "user2" in sm2.user_sessions
        assert session1["session_token"] in sm2.user_sessions["user1"]
        assert session2["session_token"] in sm2.user_sessions["user2"]
        
        # Cleanup second session manager
        await sm2.cleanup()

    @pytest.mark.asyncio
    async def test_expired_session_filtering_on_load(self, temp_session_manager):
        """Test that expired sessions are filtered out when loading from disk."""
        sm = temp_session_manager
        
        # Manually create session data with expired session
        past_time = datetime.utcnow() - timedelta(hours=1)
        future_time = datetime.utcnow() + timedelta(hours=1)
        
        sessions_data = {
            "sessions": {
                "expired_token": {
                    "session_token": "expired_token",
                    "user_id": "expired_user",
                    "user_info": {"username": "expired"},
                    "created_at": past_time.isoformat(),
                    "expires_at": past_time.isoformat(),
                    "last_activity": past_time.isoformat(),
                    "is_active": True
                },
                "valid_token": {
                    "session_token": "valid_token",
                    "user_id": "valid_user", 
                    "user_info": {"username": "valid"},
                    "created_at": past_time.isoformat(),
                    "expires_at": future_time.isoformat(),
                    "last_activity": past_time.isoformat(),
                    "is_active": True
                }
            },
            "user_sessions": {
                "expired_user": ["expired_token"],
                "valid_user": ["valid_token"]
            }
        }
        
        # Write to session file
        with open(sm.session_file_path, "w") as f:
            json.dump(sessions_data, f)
        
        # Create new session manager to test loading
        sm2 = SessionManager(session_file=sm.session_file_path.name)
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Verify only valid session was loaded
        assert len(sm2.sessions) == 1
        assert "valid_token" in sm2.sessions
        assert "expired_token" not in sm2.sessions
        
        # Verify user sessions mapping only contains valid user
        assert "valid_user" in sm2.user_sessions
        assert "expired_user" not in sm2.user_sessions
        
        # Cleanup
        await sm2.cleanup()

    @pytest.mark.asyncio
    async def test_session_invalidation_persistence(self, temp_session_manager):
        """Test that session invalidation is persisted to disk."""
        sm = temp_session_manager
        
        # Create a session
        user_info = {"user_id": "test_user", "username": "testuser"}
        session_data = await sm.create_session(user_info=user_info)
        session_token = session_data["session_token"]
        
        # Verify session exists
        assert session_token in sm.sessions
        assert sm.sessions[session_token]["is_active"] is True
        
        # Invalidate the session
        await sm.invalidate_session(session_token)
        
        # Verify session is marked as inactive
        assert sm.sessions[session_token]["is_active"] is False
        
        # Verify persistence to disk
        with open(sm.session_file_path, "r") as f:
            saved_data = json.load(f)
        
        saved_session = saved_data["sessions"][session_token]
        assert saved_session["is_active"] is False

    @pytest.mark.asyncio
    async def test_multiple_user_sessions_persistence(self, temp_session_manager):
        """Test persistence of multiple sessions for the same user."""
        sm = temp_session_manager
        
        user_info = {"user_id": "multi_session_user", "username": "multiuser"}
        
        # Create multiple sessions for the same user
        session1 = await sm.create_session(user_info=user_info)
        session2 = await sm.create_session(user_info=user_info)
        session3 = await sm.create_session(user_info=user_info)
        
        # Verify user has multiple sessions
        assert len(sm.user_sessions["multi_session_user"]) == 3
        
        # Create new session manager and load
        sm2 = SessionManager(session_file=sm.session_file_path.name)
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Verify all sessions were loaded and mapped correctly
        assert len(sm2.sessions) == 3
        assert len(sm2.user_sessions["multi_session_user"]) == 3
        
        expected_tokens = {session1["session_token"], session2["session_token"], session3["session_token"]}
        actual_tokens = set(sm2.user_sessions["multi_session_user"])
        assert expected_tokens == actual_tokens
        
        # Cleanup
        await sm2.cleanup()

    @pytest.mark.asyncio
    async def test_session_file_creation_on_first_save(self):
        """Test that session file and directory are created when needed."""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file_path = Path(temp_dir) / "new_subdir" / "sessions.json"
            
            # Ensure file doesn't exist
            assert not session_file_path.exists()
            assert not session_file_path.parent.exists()
            
            # Create session manager
            sm = SessionManager(session_file=str(session_file_path))
            while not sm._initialized:
                await asyncio.sleep(0.01)
            
            # Create a session to trigger save
            user_info = {"user_id": "test", "username": "test"}
            await sm.create_session(user_info=user_info)
            
            # Verify file and directory were created
            assert session_file_path.exists()
            assert session_file_path.parent.exists()
            
            # Verify content
            with open(session_file_path, "r") as f:
                data = json.load(f)
            
            assert "sessions" in data
            assert "user_sessions" in data
            assert len(data["sessions"]) == 1
            
            # Cleanup
            await sm.cleanup()

    @pytest.mark.asyncio
    async def test_malformed_session_file_handling(self, temp_session_manager):
        """Test handling of malformed session files."""
        sm = temp_session_manager
        
        # Write malformed JSON to session file
        with open(sm.session_file_path, "w") as f:
            f.write("invalid json content")
        
        # Create new session manager - should handle error gracefully
        sm2 = SessionManager(session_file=sm.session_file_path.name)
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Should start with empty sessions despite malformed file
        assert len(sm2.sessions) == 0
        assert len(sm2.user_sessions) == 0
        
        # Should still be able to create new sessions
        user_info = {"user_id": "test", "username": "test"}
        session_data = await sm2.create_session(user_info=user_info)
        
        assert session_data["session_token"]
        assert len(sm2.sessions) == 1
        
        # Cleanup
        await sm2.cleanup()

    @pytest.mark.asyncio
    async def test_datetime_serialization_deserialization(self, temp_session_manager):
        """Test proper datetime handling in persistence."""
        sm = temp_session_manager
        
        # Create session
        user_info = {"user_id": "datetime_test", "username": "datetester"}
        session_data = await sm.create_session(user_info=user_info)
        
        original_created_at = session_data["created_at"]
        original_expires_at = session_data["expires_at"]
        
        # Load in new session manager
        sm2 = SessionManager(session_file=sm.session_file_path.name)
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Verify datetime objects were properly restored
        loaded_session = sm2.sessions[session_data["session_token"]]
        
        assert isinstance(loaded_session["created_at"], datetime)
        assert isinstance(loaded_session["expires_at"], datetime)
        assert isinstance(loaded_session["last_activity"], datetime)
        
        # Verify datetime values are approximately equal (within 1 second)
        assert abs((loaded_session["created_at"] - original_created_at).total_seconds()) < 1
        assert abs((loaded_session["expires_at"] - original_expires_at).total_seconds()) < 1
        
        # Cleanup
        await sm2.cleanup()


class TestSessionValidation:
    """Test session validation and token extraction."""

    @pytest.mark.asyncio
    async def test_session_validation_with_valid_token(self, temp_session_manager):
        """Test session validation with valid token."""
        sm = temp_session_manager
        
        user_info = {"user_id": "valid_user", "username": "validuser"}
        session_data = await sm.create_session(user_info=user_info)
        session_token = session_data["session_token"]
        
        # Test validation
        is_valid = await sm.is_session_valid(session_token)
        assert is_valid is True
        
        # Test get_session
        retrieved_session = await sm.get_session(session_token)
        assert retrieved_session is not None
        assert retrieved_session["user_id"] == "valid_user"

    @pytest.mark.asyncio
    async def test_session_validation_with_expired_token(self, temp_session_manager):
        """Test session validation with expired token."""
        sm = temp_session_manager
        
        # Create session with short expiry
        user_info = {"user_id": "expired_user", "username": "expireduser"}
        
        # Mock settings to have very short session timeout
        with patch('runninghub_proxy.browser_utils.session_manager.settings') as mock_settings:
            mock_settings.session_timeout = 0.1  # 0.1 seconds
            session_data = await sm.create_session(user_info=user_info)
        
        session_token = session_data["session_token"]
        
        # Wait for expiration
        await asyncio.sleep(0.2)
        
        # Test validation - should be invalid
        is_valid = await sm.is_session_valid(session_token)
        assert is_valid is False
        
        # Session should be marked as inactive
        session_info = await sm.get_session(session_token)
        assert session_info["is_active"] is False

    @pytest.mark.asyncio
    async def test_session_validation_with_invalid_token(self, temp_session_manager):
        """Test session validation with non-existent token."""
        sm = temp_session_manager
        
        fake_token = "non-existent-token-12345"
        
        is_valid = await sm.is_session_valid(fake_token)
        assert is_valid is False
        
        retrieved_session = await sm.get_session(fake_token)
        assert retrieved_session is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
