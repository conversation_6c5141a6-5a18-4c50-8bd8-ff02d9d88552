"""Pytest configuration for RunningHub Proxy tests."""

import pytest
import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment before each test."""
    # Any global test setup can go here
    yield
    # Any global test cleanup can go here


@pytest.fixture
def mock_settings():
    """Mock settings for tests."""
    from unittest.mock import Mock
    
    settings_mock = Mock()
    settings_mock.session_timeout = 3600  # 1 hour default
    settings_mock.api_key = "test-api-key"
    settings_mock.debug = True
    
    return settings_mock


# Configure pytest async
pytest_plugins = ["pytest_asyncio"]
