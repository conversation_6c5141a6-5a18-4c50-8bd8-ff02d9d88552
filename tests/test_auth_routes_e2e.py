"""End-to-end tests for authentication API routes."""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch, Mock
import tempfile
from pathlib import Path

from runninghub_proxy.api_utils.routes.auth import router


@pytest.fixture
def temp_session_file():
    """Create temporary session file for testing."""
    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
        tmp_path = Path(tmp.name)
    yield str(tmp_path)
    if tmp_path.exists():
        tmp_path.unlink()


@pytest.fixture
def mock_auth_handler():
    """Mock AuthHandler for testing."""
    from runninghub_proxy.models.auth import AuthResponse
    from runninghub_proxy.config.constants import AuthStatus
    
    mock_handler = AsyncMock()
    
    # Mock successful authentication
    mock_handler.authenticate.return_value = AuthResponse(
        status=AuthStatus.AUTHENTICATED,
        message="Authentication successful",
        user_info={
            "user_id": "test_user_123",
            "username": "testuser",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        }
    )
    
    return mock_handler


@pytest.fixture
def app_with_mocked_dependencies(temp_session_file, mock_auth_handler):
    """Create FastAPI app with mocked dependencies."""
    from fastapi import FastAPI
    from runninghub_proxy.browser_utils.session_manager import SessionManager
    
    app = FastAPI()
    app.include_router(router, prefix="/auth")
    
    # Create session manager with temp file
    session_manager = SessionManager(session_file=temp_session_file)
    
    # Mock dependencies
    async def get_test_session_manager():
        while not session_manager._initialized:
            await asyncio.sleep(0.01)
        return session_manager
    
    async def get_test_browser_context():
        return Mock()  # Mock browser context
    
    async def get_test_logger():
        from runninghub_proxy.logging_utils.setup import get_logger
        return get_logger(__name__)
    
    def verify_test_api_key():
        return "test-api-key"
    
    # Override dependencies
    from runninghub_proxy.api_utils.dependencies import (
        get_session_manager, get_browser_context, 
        get_logger_dependency, verify_api_key
    )
    
    app.dependency_overrides[get_session_manager] = get_test_session_manager
    app.dependency_overrides[get_browser_context] = get_test_browser_context  
    app.dependency_overrides[get_logger_dependency] = get_test_logger
    app.dependency_overrides[verify_api_key] = verify_test_api_key
    
    # Mock AuthHandler creation
    with patch('runninghub_proxy.api_utils.routes.auth.AuthHandler', return_value=mock_auth_handler):
        yield app, session_manager


class TestAuthenticationRoutes:
    """Test authentication API routes end-to-end."""

    @pytest.mark.asyncio
    async def test_login_and_status_flow(self, app_with_mocked_dependencies):
        """Test complete login and status check flow."""
        app, session_manager = app_with_mocked_dependencies
        
        with TestClient(app) as client:
            # Step 1: Login request
            login_data = {
                "method": "phone_sms",
                "phone": "+1234567890",
                "timeout": 300
            }
            
            response = client.post("/auth/login", json=login_data)
            
            assert response.status_code == 200
            login_result = response.json()
            
            assert login_result["status"] == "authenticated"
            assert "session_token" in login_result
            assert "expires_at" in login_result
            assert login_result["user_info"]["user_id"] == "test_user_123"
            
            session_token = login_result["session_token"]
            
            # Step 2: Check auth status with token
            headers = {"Authorization": f"Bearer {session_token}"}
            status_response = client.get("/auth/status", headers=headers)
            
            assert status_response.status_code == 200
            status_result = status_response.json()
            
            assert status_result["is_authenticated"] is True
            assert status_result["session_valid"] is True
            assert status_result["user_info"]["user_id"] == "test_user_123"
            assert status_result["time_until_expiry"] > 0

    @pytest.mark.asyncio 
    async def test_status_without_token(self, app_with_mocked_dependencies):
        """Test status endpoint without authentication token."""
        app, session_manager = app_with_mocked_dependencies
        
        with TestClient(app) as client:
            response = client.get("/auth/status")
            
            assert response.status_code == 200
            result = response.json()
            
            assert result["is_authenticated"] is False
            assert result["session_valid"] is False
            assert result["user_info"] is None

    @pytest.mark.asyncio
    async def test_status_with_invalid_token(self, app_with_mocked_dependencies):
        """Test status endpoint with invalid token."""
        app, session_manager = app_with_mocked_dependencies
        
        with TestClient(app) as client:
            headers = {"Authorization": "Bearer invalid-token-123"}
            response = client.get("/auth/status")
            
            assert response.status_code == 200
            result = response.json()
            
            assert result["is_authenticated"] is False
            assert result["session_valid"] is False

    @pytest.mark.asyncio
    async def test_logout_flow(self, app_with_mocked_dependencies):
        """Test logout functionality."""
        app, session_manager = app_with_mocked_dependencies
        
        with TestClient(app) as client:
            # Login first
            login_data = {
                "method": "phone_sms", 
                "phone": "+1234567890"
            }
            
            login_response = client.post("/auth/login", json=login_data)
            session_token = login_response.json()["session_token"]
            
            # Verify session is active
            headers = {"Authorization": f"Bearer {session_token}"}
            status_response = client.get("/auth/status", headers=headers)
            assert status_response.json()["is_authenticated"] is True
            
            # Logout
            logout_response = client.post("/auth/logout", headers=headers)
            
            assert logout_response.status_code == 200
            logout_result = logout_response.json()
            
            assert logout_result["success"] is True
            assert logout_result["sessions_cleared"] == 1
            
            # Verify session is no longer valid
            status_response_after = client.get("/auth/status", headers=headers)
            assert status_response_after.json()["is_authenticated"] is False

    @pytest.mark.asyncio
    async def test_session_persistence_across_app_restarts(self, temp_session_file):
        """Test that sessions persist across application restarts."""
        from runninghub_proxy.browser_utils.session_manager import SessionManager
        import asyncio
        
        # Create first session manager and session
        sm1 = SessionManager(session_file=temp_session_file)
        while not sm1._initialized:
            await asyncio.sleep(0.01)
        
        user_info = {
            "user_id": "persistence_test",
            "username": "persisttest",
            "email": "<EMAIL>"
        }
        
        session_data = await sm1.create_session(user_info=user_info)
        session_token = session_data["session_token"]
        
        # Verify session exists
        assert await sm1.is_session_valid(session_token)
        
        # Cleanup first session manager (simulating app shutdown)
        await sm1.cleanup()
        
        # Create second session manager (simulating app restart)
        sm2 = SessionManager(session_file=temp_session_file)
        while not sm2._initialized:
            await asyncio.sleep(0.01)
        
        # Verify session was restored
        assert await sm2.is_session_valid(session_token)
        
        restored_session = await sm2.get_session(session_token)
        assert restored_session is not None
        assert restored_session["user_id"] == "persistence_test"
        assert restored_session["user_info"]["username"] == "persisttest"
        
        # Cleanup
        await sm2.cleanup()

    @pytest.mark.asyncio
    async def test_multiple_concurrent_sessions(self, app_with_mocked_dependencies):
        """Test handling of multiple concurrent sessions."""
        app, session_manager = app_with_mocked_dependencies
        
        with TestClient(app) as client:
            sessions = []
            
            # Create multiple sessions
            for i in range(3):
                login_data = {
                    "method": "phone_sms",
                    "phone": f"+123456789{i}"
                }
                
                with patch('runninghub_proxy.api_utils.routes.auth.AuthHandler') as mock_auth_class:
                    mock_handler = AsyncMock()
                    mock_handler.authenticate.return_value = Mock(
                        status="authenticated",
                        user_info={"user_id": f"user_{i}", "username": f"user{i}"},
                        message="Authentication successful"
                    )
                    mock_auth_class.return_value = mock_handler
                    
                    response = client.post("/auth/login", json=login_data)
                    assert response.status_code == 200
                    
                    result = response.json()
                    sessions.append(result["session_token"])
            
            # Verify all sessions are valid
            for i, session_token in enumerate(sessions):
                headers = {"Authorization": f"Bearer {session_token}"}
                response = client.get("/auth/status", headers=headers)
                
                assert response.status_code == 200
                result = response.json()
                assert result["is_authenticated"] is True
                assert result["user_info"]["user_id"] == f"user_{i}"


if __name__ == "__main__":
    import asyncio
    pytest.main([__file__, "-v"])
