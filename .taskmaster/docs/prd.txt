# Auth Handler HTTP Request Monitoring Refactoring

## Project Overview
Refactor the authentication handler in the RunningHub proxy to use HTTP request monitoring instead of UI element detection for more reliable authentication status detection.

## Current Problem
The current `_wait_for_user_login_completion` function relies on UI element visibility to detect login success/failure, which can be unreliable due to:
- Modal animation timing issues
- Dynamic UI changes
- Race conditions between DOM updates and checks

## Proposed Solution
Replace UI-based detection with HTTP request monitoring by:
1. Setting up network request listeners in Playwright
2. Monitoring for the specific getUserInfo API call
3. Parsing the response to extract user information
4. Using HTTP response codes and data to determine authentication status

## Technical Requirements

### API Endpoint
- **URL**: `https://www.runninghub.cn/uc/getUserInfo`
- **Method**: GET
- **Success Response**: 
  ```json
  {
    "code": 0,
    "msg": "success", 
    "data": {
      "id": "1900912631111172098",
      "mobile": "136****1407",
      "nickName": "user_orxzl3dy",
      "memberInfo": {...},
      "apiKey": "4d9acc72bdfe4ab0a48a0abcb72c0e46",
      ...
    }
  }
  ```

### Implementation Steps
1. Add network request monitoring to the page
2. Create HTTP request interceptor for getUserInfo endpoint
3. Refactor _wait_for_user_login_completion to use HTTP monitoring
4. Extract user info from HTTP response
5. Handle error cases and timeouts
6. Update tests and documentation

## Success Criteria
- Authentication detection is more reliable
- Faster detection of login success/failure
- Reduced dependency on UI timing
- Better error handling and user feedback
