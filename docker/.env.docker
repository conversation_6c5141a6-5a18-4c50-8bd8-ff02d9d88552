# Docker 环境配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# Docker 主机端口配置
# =============================================================================

# 主机上映射的端口 (外部访问端口)
HOST_FASTAPI_PORT=2048
HOST_STREAM_PORT=3120

# =============================================================================
# 容器内服务端口配置
# =============================================================================

# FastAPI 服务端口 (容器内)
PORT=8000
DEFAULT_FASTAPI_PORT=2048
DEFAULT_CAMOUFOX_PORT=9222

# 流式代理服务配置
STREAM_PORT=3120

# =============================================================================
# 代理配置
# =============================================================================

# HTTP/HTTPS 代理设置
# HTTP_PROXY=http://host.docker.internal:7890
# HTTPS_PROXY=http://host.docker.internal:7890

# 统一代理配置 (优先级高于 HTTP_PROXY/HTTPS_PROXY)
# UNIFIED_PROXY_CONFIG=http://host.docker.internal:7890

# 代理绕过列表 (用分号分隔)
# NO_PROXY=localhost;127.0.0.1;*.local

# =============================================================================
# 日志配置
# =============================================================================

# 服务器日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
SERVER_LOG_LEVEL=INFO

# 是否重定向 print 输出到日志
SERVER_REDIRECT_PRINT=false

# 启用调试日志
DEBUG_LOGS_ENABLED=false

# 启用跟踪日志
TRACE_LOGS_ENABLED=false

# =============================================================================
# 认证配置
# =============================================================================

# 自动保存认证信息
AUTO_SAVE_AUTH=false

# 认证保存超时时间 (秒)
AUTH_SAVE_TIMEOUT=30

# 自动确认登录
AUTO_CONFIRM_LOGIN=true

# =============================================================================
# 浏览器配置
# =============================================================================

# 启动模式 (normal, headless, virtual_display, direct_debug_no_browser)
LAUNCH_MODE=headless

# =============================================================================
# API 默认参数配置
# =============================================================================

# 默认温度值 (0.0-2.0)
DEFAULT_TEMPERATURE=1.0

# 默认最大输出令牌数
DEFAULT_MAX_OUTPUT_TOKENS=65536

# 默认 Top-P 值 (0.0-1.0)
DEFAULT_TOP_P=0.95

# 默认停止序列 (JSON 数组格式)
DEFAULT_STOP_SEQUENCES=["用户:"]

# =============================================================================
# 超时配置 (毫秒)
# =============================================================================

# 响应完成总超时时间
RESPONSE_COMPLETION_TIMEOUT=300000

# 轮询间隔
POLLING_INTERVAL=300
POLLING_INTERVAL_STREAM=180

# 静默超时
SILENCE_TIMEOUT_MS=60000

# =============================================================================
# 脚本注入配置
# =============================================================================

# 是否启用油猴脚本注入功能
ENABLE_SCRIPT_INJECTION=true

# 油猴脚本文件路径（相对于容器内 /app 目录）
USERSCRIPT_PATH=browser_utils/more_modles.js

# 注意：MODEL_CONFIG_PATH 已废弃
# 模型数据现在直接从 USERSCRIPT_PATH 指定的油猴脚本中解析

# =============================================================================
# Docker 特定配置
# =============================================================================

# 容器内存限制
# 默认不限制。如需限制容器资源，请在你的 .env 文件中取消注释并设置以下值。
# 例如: DOCKER_MEMORY_LIMIT=1g或DOCKER_MEMORY_LIMIT=1024m
# 注意：DOCKER_MEMORY_LIMIT和DOCKER_MEMSWAP_LIMIT相同时，不会使用SWAP
# DOCKER_MEMORY_LIMIT=
# DOCKER_MEMSWAP_LIMIT=

# 容器重启策略相关
# 这些配置项在 docker-compose.yml 中使用

# 健康检查间隔 (秒)
HEALTHCHECK_INTERVAL=30

# 健康检查超时 (秒)
HEALTHCHECK_TIMEOUT=10

# 健康检查重试次数
HEALTHCHECK_RETRIES=3

# =============================================================================
# 网络配置说明
# =============================================================================

# 在 Docker 环境中访问主机服务，请使用：
# - Linux: host.docker.internal
# - macOS: host.docker.internal  
# - Windows: host.docker.internal
# 
# 例如，如果主机上有代理服务运行在 7890 端口：
# HTTP_PROXY=http://host.docker.internal:7890
